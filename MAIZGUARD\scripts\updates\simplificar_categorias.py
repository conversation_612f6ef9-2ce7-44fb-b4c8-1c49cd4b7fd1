from app.database.config import engine
from sqlalchemy import text

def simplificar_categorias():
    """
    Simplifica las categorías para mostrar solo:
    - Fases: Maduración y Cosecha
    - Temporadas: Temporada de lluvia y Temporada de sequía
    """
    try:
        with engine.connect() as conn:
            # 1. Actualizar las plagas para usar solo las categorías deseadas
            print("Actualizando plagas a categorías simplificadas...")
            
            # Obtener IDs de las categorías deseadas
            result = conn.execute(text("SELECT id_fase_cultivo FROM fases_cultivo WHERE descripcion IN ('Maduración', 'Cosecha')"))
            fases_ids = [row[0] for row in result.fetchall()]
            
            result = conn.execute(text("SELECT id_temporada FROM temporadas WHERE descripcion IN ('Temporada de lluvia', 'Temporada de sequía')"))
            temporadas_ids = [row[0] for row in result.fetchall()]
            
            # Verificar si tenemos las categorías necesarias
            if not fases_ids or len(fases_ids) < 2:
                print("Error: No se encontraron las fases 'Maduración' y 'Cosecha'")
                return
                
            if not temporadas_ids or len(temporadas_ids) < 2:
                print("Error: No se encontraron las temporadas 'Temporada de lluvia' y 'Temporada de sequía'")
                return
            
            # Eliminar fases no deseadas (excepto las que están en uso)
            print("\nSimplificando fases de cultivo...")
            conn.execute(text(f"""
                DELETE FROM fases_cultivo 
                WHERE descripcion NOT IN ('Maduración', 'Cosecha') 
                AND id_fase_cultivo NOT IN (SELECT id_fase_cultivo FROM plagas)
            """))
            
            # Eliminar temporadas no deseadas (excepto las que están en uso)
            print("Simplificando temporadas...")
            conn.execute(text(f"""
                DELETE FROM temporadas 
                WHERE descripcion NOT IN ('Temporada de lluvia', 'Temporada de sequía') 
                AND id_temporada NOT IN (SELECT id_temporada FROM plagas)
            """))
            
            # Actualizar plagas que usan otras fases para usar Maduración o Cosecha
            print("\nActualizando plagas con fases incorrectas...")
            conn.execute(text(f"""
                UPDATE plagas 
                SET id_fase_cultivo = {fases_ids[0]} 
                WHERE id_fase_cultivo NOT IN ({','.join(map(str, fases_ids))})
            """))
            
            # Actualizar plagas que usan otras temporadas para usar Lluvia o Sequía
            print("Actualizando plagas con temporadas incorrectas...")
            conn.execute(text(f"""
                UPDATE plagas 
                SET id_temporada = {temporadas_ids[0]} 
                WHERE id_temporada NOT IN ({','.join(map(str, temporadas_ids))})
            """))
            
            # Confirmar cambios
            conn.commit()
            
            # Mostrar categorías actualizadas
            print("\nCategorías simplificadas:")
            
            print("\nFASES DE CULTIVO:")
            result = conn.execute(text("SELECT * FROM fases_cultivo"))
            fases = result.fetchall()
            for fase in fases:
                print(f"ID: {fase[0]}, Descripción: {fase[1]}")
            
            print("\nTEMPORADAS:")
            result = conn.execute(text("SELECT * FROM temporadas"))
            temporadas = result.fetchall()
            for temporada in temporadas:
                print(f"ID: {temporada[0]}, Descripción: {temporada[1]}")
            
            # Mostrar plagas actualizadas
            print("\nPLAGAS ACTUALIZADAS:")
            result = conn.execute(text("""
                SELECT p.id_plaga, p.nombre, fc.descripcion as fase, t.descripcion as temporada, tp.descripcion as tipo
                FROM plagas p
                LEFT JOIN fases_cultivo fc ON p.id_fase_cultivo = fc.id_fase_cultivo
                LEFT JOIN temporadas t ON p.id_temporada = t.id_temporada
                LEFT JOIN tipos_plaga tp ON p.id_tipo_plaga = tp.id_tipo_plaga
                ORDER BY p.id_plaga
            """))
            plagas = result.fetchall()
            for plaga in plagas:
                print(f"ID: {plaga[0]}, Nombre: {plaga[1]}, Fase: {plaga[2]}, Temporada: {plaga[3]}, Tipo: {plaga[4]}")
            
    except Exception as e:
        print(f"Error al simplificar categorías: {e}")

if __name__ == "__main__":
    simplificar_categorias()
