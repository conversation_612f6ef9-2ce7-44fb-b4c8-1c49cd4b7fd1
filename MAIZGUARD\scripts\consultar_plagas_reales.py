#!/usr/bin/env python3
"""
Script para consultar todas las plagas registradas en la base de datos
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.database.config import engine
from sqlalchemy import text

def consultar_plagas_reales():
    """Consulta todas las plagas registradas en la base de datos"""
    try:
        with engine.connect() as conn:
            print("🔍 Consultando plagas registradas en la base de datos...")
            
            # Primero verificar qué tablas existen
            result = conn.execute(text("SHOW TABLES LIKE '%plaga%'"))
            tablas = result.fetchall()
            
            print(f"\n📋 Tablas relacionadas con plagas encontradas:")
            for tabla in tablas:
                print(f"  - {tabla[0]}")
            
            # Intentar consultar la tabla 'plaga' (singular)
            try:
                print(f"\n🌱 Consultando tabla 'plaga':")
                result = conn.execute(text("SELECT * FROM plaga ORDER BY id_plaga"))
                plagas = result.fetchall()
                
                if plagas:
                    print(f"\n✅ Se encontraron {len(plagas)} plagas en la tabla 'plaga':")
                    print("=" * 100)
                    
                    for i, plaga in enumerate(plagas, 1):
                        print(f"\n🐛 PLAGA #{i}")
                        print(f"   ID: {plaga[0]}")
                        print(f"   Nombre: {plaga[1] if len(plaga) > 1 else 'N/A'}")
                        print(f"   Nombre Científico: {plaga[2] if len(plaga) > 2 else 'N/A'}")
                        print(f"   URL Imagen: {plaga[3] if len(plaga) > 3 else 'N/A'}")
                        print(f"   Descripción: {plaga[4] if len(plaga) > 4 else 'N/A'}")
                        print(f"   Recomendaciones: {plaga[5] if len(plaga) > 5 else 'N/A'}")
                        if len(plaga) > 6:
                            print(f"   ID Fase Cultivo: {plaga[6]}")
                        if len(plaga) > 7:
                            print(f"   ID Tipo Plaga: {plaga[7]}")
                        if len(plaga) > 8:
                            print(f"   ID Temporada: {plaga[8]}")
                        if len(plaga) > 9:
                            print(f"   ID Usuario: {plaga[9]}")
                        print("-" * 80)
                else:
                    print("❌ No se encontraron plagas en la tabla 'plaga'")
                    
            except Exception as e:
                print(f"❌ Error al consultar tabla 'plaga': {e}")
            
            # Intentar consultar la tabla 'plagas' (plural)
            try:
                print(f"\n🌱 Consultando tabla 'plagas':")
                result = conn.execute(text("SELECT * FROM plagas ORDER BY id_plaga"))
                plagas = result.fetchall()
                
                if plagas:
                    print(f"\n✅ Se encontraron {len(plagas)} plagas en la tabla 'plagas':")
                    print("=" * 100)
                    
                    for i, plaga in enumerate(plagas, 1):
                        print(f"\n🐛 PLAGA #{i}")
                        print(f"   ID: {plaga[0]}")
                        print(f"   Nombre: {plaga[1] if len(plaga) > 1 else 'N/A'}")
                        print(f"   Nombre Científico: {plaga[2] if len(plaga) > 2 else 'N/A'}")
                        print(f"   URL Imagen: {plaga[3] if len(plaga) > 3 else 'N/A'}")
                        print(f"   Descripción: {plaga[4] if len(plaga) > 4 else 'N/A'}")
                        print(f"   Recomendaciones: {plaga[5] if len(plaga) > 5 else 'N/A'}")
                        if len(plaga) > 6:
                            print(f"   ID Fase Cultivo: {plaga[6]}")
                        if len(plaga) > 7:
                            print(f"   ID Tipo Plaga: {plaga[7]}")
                        if len(plaga) > 8:
                            print(f"   ID Temporada: {plaga[8]}")
                        if len(plaga) > 9:
                            print(f"   ID Usuario: {plaga[9]}")
                        if len(plaga) > 10:
                            print(f"   Fecha Registro: {plaga[10]}")
                        print("-" * 80)
                else:
                    print("❌ No se encontraron plagas en la tabla 'plagas'")
                    
            except Exception as e:
                print(f"❌ Error al consultar tabla 'plagas': {e}")
            
            # Mostrar estructura de las tablas
            for tabla_nombre in ['plaga', 'plagas']:
                try:
                    print(f"\n📋 Estructura de la tabla '{tabla_nombre}':")
                    result = conn.execute(text(f"DESCRIBE {tabla_nombre}"))
                    campos = result.fetchall()
                    
                    for campo in campos:
                        print(f"   {campo[0]:<20} | {campo[1]:<30} | Nulo: {campo[2]:<5} | Clave: {campo[3]:<5}")
                        
                except Exception as e:
                    print(f"❌ Error al obtener estructura de '{tabla_nombre}': {e}")
            
    except Exception as e:
        print(f"❌ Error general: {e}")

if __name__ == "__main__":
    consultar_plagas_reales()
