from http.server import HTTPServer, BaseHTTPRequestHandler

class SimpleHTTPRequestHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        self.send_response(200)
        self.send_header('Content-type', 'text/html')
        self.end_headers()
        self.wfile.write(b'<html><body><h1>Servidor de prueba funcionando!</h1></body></html>')

def run_server(port=8888):
    server_address = ('', port)
    httpd = HTTPServer(server_address, SimpleHTTPRequestHandler)
    print(f'Iniciando servidor en el puerto {port}...')
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print('Servidor detenido.')

if __name__ == '__main__':
    run_server()
