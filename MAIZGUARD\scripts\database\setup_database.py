#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script para inicializar la base de datos de MaizGuard.
"""

import os
import sys
import subprocess
from pathlib import Path

def setup_database():
    """
    Inicializa la base de datos utilizando el script SQL.
    """
    try:
        # Obtener la ruta del script SQL
        script_dir = Path(__file__).parent
        sql_script = script_dir / "setup_database.sql"
        
        if not sql_script.exists():
            print(f"Error: No se encontró el archivo SQL en {sql_script}")
            return
        
        # Ejecutar el script SQL
        print("Inicializando la base de datos...")
        
        # Obtener las credenciales de la base de datos desde .env
        from dotenv import load_dotenv
        load_dotenv()
        
        db_user = os.getenv("DB_USER", "root")
        db_password = os.getenv("DB_PASSWORD", "")
        db_host = os.getenv("DB_HOST", "localhost")
        db_name = os.getenv("DB_NAME", "maizguard")
        
        # Comando para ejecutar el script SQL
        command = f"mysql -u {db_user} -p{db_password} -h {db_host} < {sql_script}"
        
        # Ejecutar el comando
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("Base de datos inicializada correctamente.")
            
            # Crear un usuario administrador
            print("¿Desea crear un usuario administrador? (s/n)")
            respuesta = input().lower()
            
            if respuesta == 's':
                # Importar y ejecutar el script de creación de admin
                sys.path.append(str(script_dir.parent / "utils"))
                from crear_admin import crear_admin
                crear_admin()
        else:
            print(f"Error al inicializar la base de datos: {result.stderr}")
    
    except Exception as e:
        print(f"Error al inicializar la base de datos: {e}")

if __name__ == "__main__":
    setup_database()
