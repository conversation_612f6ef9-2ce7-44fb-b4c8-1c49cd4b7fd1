#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script para actualizar las fases de cultivo de las plagas.
"""

import os
import sys

# Añadir el directorio raíz al path para poder importar los módulos de la aplicación
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from app.database.config import engine
from sqlalchemy import text

def actualizar_fases_plagas():
    """
    Actualiza las fases de cultivo de las plagas para que solo usen Maduración (4) y Cosecha (5).
    """
    try:
        with engine.connect() as conn:
            print("Actualizando fases de cultivo de las plagas...")

            # Verificar si existen las fases 4 y 5
            result = conn.execute(text("SELECT * FROM fase_cultivo WHERE id_fase_cultivo IN (4, 5)"))
            fases = result.fetchall()

            # Si no existen, insertarlas
            if len(fases) < 2:
                # Insertar las fases que faltan
                if not any(fase[0] == 4 for fase in fases):
                    conn.execute(text("INSERT INTO fase_cultivo (id_fase_cultivo, descripcion) VALUES (4, 'Maduración')"))
                if not any(fase[0] == 5 for fase in fases):
                    conn.execute(text("INSERT INTO fase_cultivo (id_fase_cultivo, descripcion) VALUES (5, 'Cosecha')"))

            # Actualizar las plagas para que usen solo Maduración (4) y Cosecha (5)
            # Plagas con ID impar: Maduración (4)
            # Plagas con ID par: Cosecha (5)
            conn.execute(text("UPDATE plaga SET id_fase_cultivo = 4 WHERE id_plaga % 2 = 1"))
            conn.execute(text("UPDATE plaga SET id_fase_cultivo = 5 WHERE id_plaga % 2 = 0"))

            # Confirmar cambios
            conn.commit()

            # Verificar las actualizaciones
            result = conn.execute(text("""
                SELECT p.id_plaga, p.nombre, f.descripcion
                FROM plaga p
                JOIN fase_cultivo f ON p.id_fase_cultivo = f.id_fase_cultivo
                ORDER BY p.id_plaga
            """))

            plagas = result.fetchall()
            print("\nPlagas actualizadas:")
            for plaga in plagas:
                print(f"ID: {plaga[0]}, Nombre: {plaga[1]}, Fase: {plaga[2]}")

            print("\nActualización completada exitosamente.")

    except Exception as e:
        print(f"Error al actualizar fases de plagas: {e}")

if __name__ == "__main__":
    actualizar_fases_plagas()
