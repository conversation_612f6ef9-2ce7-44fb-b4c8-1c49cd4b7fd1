#!/usr/bin/env python3
"""
Script simple para crear la tabla de cultivos
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from app.database.config import engine
from sqlalchemy import text

def crear_cultivos_simple():
    """Crear la tabla de cultivos de forma simple"""
    try:
        with engine.connect() as conn:
            print("🔧 Creando tabla 'cultivos' sin claves foráneas...")
            
            # Crear tabla cultivos sin claves foráneas primero
            conn.execute(text("""
                CREATE TABLE IF NOT EXISTS cultivos (
                    id_cultivo INT AUTO_INCREMENT PRIMARY KEY,
                    nombre VARCHAR(100) NOT NULL,
                    variedad VARCHAR(100),
                    url_img VARCHAR(255),
                    descripcion TEXT NOT NULL,
                    cuidados TEXT,
                    id_region INT NOT NULL DEFAULT 1,
                    id_temporada INT NOT NULL DEFAULT 1,
                    id_usuarios INT NOT NULL DEFAULT 2,
                    fecha_registro DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """))
            
            conn.commit()
            print("✅ Tabla 'cultivos' creada exitosamente.")
            
            # Verificar estructura
            result = conn.execute(text("DESCRIBE cultivos"))
            campos = result.fetchall()
            
            print("\n📋 Estructura de la tabla 'cultivos':")
            for campo in campos:
                print(f"  📝 {campo.Field}: {campo.Type}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    print("🌽 Creando tabla de cultivos...")
    crear_cultivos_simple()
