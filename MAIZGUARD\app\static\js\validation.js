// Validación del formulario de registro
document.addEventListener('DOMContentLoaded', function() {
    const registroForm = document.getElementById('registroForm');
    
    if (registroForm) {
        const password = document.getElementById('inputPassword');
        const confirmPassword = document.getElementById('inputPasswordConfirm');
        const email = document.getElementById('inputEmail');
        
        // Validar que las contraseñas coincidan
        function validatePassword() {
            if (password.value !== confirmPassword.value) {
                confirmPassword.setCustomValidity('Las contraseñas no coinciden');
                confirmPassword.classList.add('is-invalid');
            } else {
                confirmPassword.setCustomValidity('');
                confirmPassword.classList.remove('is-invalid');
            }
        }
        
        // Validar formato de correo electrónico
        function validateEmail() {
            const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
            if (!emailRegex.test(email.value)) {
                email.classList.add('is-invalid');
                return false;
            } else {
                email.classList.remove('is-invalid');
                return true;
            }
        }
        
        // Validar fecha de nacimiento (debe ser mayor de 18 años)
        function validateBirthdate() {
            const birthdate = document.getElementById('inputBirthdate');
            if (birthdate.value) {
                const today = new Date();
                const birthdateValue = new Date(birthdate.value);
                let age = today.getFullYear() - birthdateValue.getFullYear();
                const monthDiff = today.getMonth() - birthdateValue.getMonth();
                
                if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthdateValue.getDate())) {
                    age--;
                }
                
                if (age < 18) {
                    birthdate.setCustomValidity('Debes ser mayor de 18 años');
                    return false;
                } else {
                    birthdate.setCustomValidity('');
                    return true;
                }
            }
            return true;
        }
        
        // Eventos para validación en tiempo real
        password.addEventListener('input', validatePassword);
        confirmPassword.addEventListener('input', validatePassword);
        email.addEventListener('input', validateEmail);
        
        // Validación al enviar el formulario
        registroForm.addEventListener('submit', function(event) {
            validatePassword();
            const isEmailValid = validateEmail();
            const isBirthdateValid = validateBirthdate();
            
            if (!isEmailValid || !isBirthdateValid || password.value !== confirmPassword.value) {
                event.preventDefault();
            }
        });
    }
});
