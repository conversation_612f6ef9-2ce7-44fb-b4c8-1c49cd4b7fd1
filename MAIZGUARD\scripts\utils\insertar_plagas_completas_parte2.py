#!/usr/bin/env python3
"""
Script para insertar hongos, bacterias y virus específicos de maíz en la base de datos MaizGuard
Configuradas para fase de maduración y temporada seca
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from app.database.config import engine
from sqlalchemy import text

def insertar_plagas_parte2():
    """Insertar hongos, bacterias y virus específicos de maíz"""
    try:
        with engine.connect() as conn:
            # Obtener el ID de un usuario administrador
            result = conn.execute(text("SELECT id_usuarios FROM usuarios WHERE id_rol = 1 LIMIT 1"))
            admin_user = result.fetchone()
            
            if not admin_user:
                result = conn.execute(text("SELECT id_usuarios FROM usuarios LIMIT 1"))
                admin_user = result.fetchone()
                admin_user_id = admin_user.id_usuarios if admin_user else 1
            else:
                admin_user_id = admin_user.id_usuarios

            # HONGOS (id_tipo_plaga = 3)
            hongos = [
                {
                    "nombre": "Moho de la Mazorca",
                    "nombre_cientifico": "Fusarium verticillioides",
                    "descripcion": "Causa pudrición en las mazorcas y produce fumonisinas, que son micotoxinas perjudiciales para la salud y la calidad del maíz.",
                    "recomendaciones": """
CONTROL QUÍMICO:
• Fungicida: Propiconazol 25% EC
• Cantidad: 0.5-0.8 litros por hectárea (50-80 ml por 100 m²)
• Aplicación: Aspersión dirigida a mazorcas durante floración
• Frecuencia: 2 aplicaciones con intervalo de 15 días

• Fungicida preventivo: Azoxistrobina 25% SC
• Cantidad: 0.8-1.0 litros por hectárea (80-100 ml por 100 m²)
• Aplicación: Aspersión foliar preventiva
• Momento: Inicio de floración

MANEJO CULTURAL:
• Cosecha oportuna con humedad <20%
• Secado rápido post-cosecha
• Almacenamiento con humedad <14%
• Rotación de cultivos
                    """
                },
                {
                    "nombre": "Aflatoxina",
                    "nombre_cientifico": "Aspergillus flavus",
                    "descripcion": "Produce aflatoxinas, altamente tóxicas, que deterioran el grano y pueden provocar graves problemas de salud.",
                    "recomendaciones": """
CONTROL QUÍMICO:
• Fungicida: Tebuconazol 25% EC
• Cantidad: 0.5-0.7 litros por hectárea (50-70 ml por 100 m²)
• Aplicación: Aspersión preventiva durante llenado de grano
• Frecuencia: 2 aplicaciones con intervalo de 20 días

CONTROL BIOLÓGICO:
• Aplicación de Aspergillus flavus atoxigénico
• Cantidad: 10 kg por hectárea (1 kg por 100 m²)
• Aplicación: Distribución en suelo antes de floración
• Método: Competencia por sustrato

MANEJO POST-COSECHA:
• Secado inmediato <14% humedad
• Almacenamiento en condiciones secas
• Control de temperatura <25°C
• Monitoreo de aflatoxinas en laboratorio
                    """
                },
                {
                    "nombre": "Pudrición del Tallo",
                    "nombre_cientifico": "Bipolaris maydis",
                    "descripcion": "Causa pudrición en el tallo y las mazorcas, produciendo lesiones que afectan la calidad del maíz.",
                    "recomendaciones": """
CONTROL QUÍMICO:
• Fungicida: Mancozeb 80% WP
• Cantidad: 2.0-2.5 kg por hectárea (200-250 g por 100 m²)
• Aplicación: Aspersión foliar preventiva
• Frecuencia: Cada 15 días durante período crítico

• Fungicida sistémico: Carbendazim 50% WP
• Cantidad: 0.5-0.8 kg por hectárea (50-80 g por 100 m²)
• Aplicación: Aspersión foliar
• Momento: Al detectar primeros síntomas
                    """
                },
                {
                    "nombre": "Pudrición por Penicillium",
                    "nombre_cientifico": "Penicillium spp.",
                    "descripcion": "Causa pudrición del grano y puede producir toxinas como la patulina, que afectan la calidad del maíz.",
                    "recomendaciones": """
CONTROL QUÍMICO:
• Fungicida: Imazalil 50% EC
• Cantidad: 0.3-0.5 litros por hectárea (30-50 ml por 100 m²)
• Aplicación: Tratamiento de semillas y aspersión preventiva
• Frecuencia: Una aplicación preventiva

CONTROL POST-COSECHA:
• Secado rápido <14% humedad
• Almacenamiento hermético
• Control de temperatura y humedad
• Limpieza de instalaciones de almacenamiento
                    """
                },
                {
                    "nombre": "Mancha de Alternaria",
                    "nombre_cientifico": "Alternaria spp.",
                    "descripcion": "Provoca manchas y pudriciones en las mazorcas y hojas del maíz, reduciendo la calidad del grano.",
                    "recomendaciones": """
CONTROL QUÍMICO:
• Fungicida: Difenoconazol 25% EC
• Cantidad: 0.3-0.5 litros por hectárea (30-50 ml por 100 m²)
• Aplicación: Aspersión foliar al detectar síntomas
• Frecuencia: Cada 10-15 días según severidad

MANEJO INTEGRADO:
• Eliminación de residuos infectados
• Rotación con cultivos no susceptibles
• Mejoramiento de drenaje
• Uso de variedades resistentes
                    """
                }
            ]

            # BACTERIAS (id_tipo_plaga = 4)
            bacterias = [
                {
                    "nombre": "Mancha Bacteriana del Maíz",
                    "nombre_cientifico": "Xanthomonas vasicola",
                    "descripcion": "Causa manchas acuosas en las hojas del maíz, que pueden secarse y reducir el rendimiento.",
                    "recomendaciones": """
CONTROL QUÍMICO:
• Bactericida: Oxicloruro de cobre 50% WP
• Cantidad: 2.0-3.0 kg por hectárea (200-300 g por 100 m²)
• Aplicación: Aspersión foliar preventiva
• Frecuencia: Cada 10-15 días en condiciones favorables

• Bactericida sistémico: Estreptomicina 20% + Oxitetraciclina 1.5%
• Cantidad: 0.5-0.8 kg por hectárea (50-80 g por 100 m²)
• Aplicación: Aspersión foliar
• Momento: Al detectar primeros síntomas

MANEJO CULTURAL:
• Eliminación de residuos infectados
• Rotación de cultivos
• Evitar riego por aspersión
• Desinfección de herramientas
                    """
                },
                {
                    "nombre": "Podredumbre Blanda Bacteriana",
                    "nombre_cientifico": "Pantoea ananatis",
                    "descripcion": "Provoca podredumbre blanda con manchas blandas y descompuestas en el maíz.",
                    "recomendaciones": """
CONTROL QUÍMICO:
• Bactericida: Sulfato de cobre pentahidratado 25% WP
• Cantidad: 1.5-2.0 kg por hectárea (150-200 g por 100 m²)
• Aplicación: Aspersión foliar preventiva
• Frecuencia: Cada 15 días en condiciones húmedas

CONTROL PREVENTIVO:
• Mejoramiento del drenaje
• Evitar heridas en plantas
• Control de insectos vectores
• Manejo adecuado de humedad
                    """
                },
                {
                    "nombre": "Podredumbre Bacteriana del Maíz",
                    "nombre_cientifico": "Clavibacter michiganensis",
                    "descripcion": "Infección bacteriana con lesiones en hojas y tallos, lo que puede llevar a la pérdida de la planta.",
                    "recomendaciones": """
CONTROL QUÍMICO:
• Bactericida: Kasugamicina 2% SL
• Cantidad: 1.0-1.5 litros por hectárea (100-150 ml por 100 m²)
• Aplicación: Aspersión foliar
• Frecuencia: Cada 10 días al detectar síntomas

MANEJO INTEGRADO:
• Uso de semillas certificadas
• Desinfección de herramientas con alcohol 70%
• Eliminación de plantas infectadas
• Rotación con cultivos no hospederos
                    """
                }
            ]

            # VIRUS (id_tipo_plaga = 5)
            virus = [
                {
                    "nombre": "Virus del Mosaico del Maíz",
                    "nombre_cientifico": "Maize dwarf mosaic virus (MDMV)",
                    "descripcion": "Provoca manchas cloróticas y rayas amarillas en las hojas. En fases avanzadas, las hojas pueden necrosarse. El crecimiento se ve notablemente afectado. Es transmitido por pulgones.",
                    "recomendaciones": """
CONTROL DE VECTORES:
• Insecticida: Imidacloprid 35% SC
• Cantidad: 0.3-0.5 litros por hectárea (30-50 ml por 100 m²)
• Aplicación: Aspersión foliar para control de pulgones
• Frecuencia: Cada 15 días durante período crítico

MANEJO INTEGRADO:
• Uso de variedades resistentes
• Eliminación de malezas hospederas
• Control temprano de pulgones vectores
• Siembra en fechas recomendadas
• Eliminación de plantas infectadas
                    """
                },
                {
                    "nombre": "Virus del Rayado Fino del Maíz",
                    "nombre_cientifico": "Maize fine streak virus (MFSV)",
                    "descripcion": "Causa rayas finas y amarillentas en las hojas. El virus es transmitido por chicharritas (Cicadellidae). Afecta el rendimiento del cultivo.",
                    "recomendaciones": """
CONTROL DE VECTORES:
• Insecticida: Thiamethoxam 25% WG
• Cantidad: 0.2-0.3 kg por hectárea (20-30 g por 100 m²)
• Aplicación: Aspersión foliar para control de chicharritas
• Frecuencia: Cada 10-15 días según presión de vectores

MANEJO PREVENTIVO:
• Control de malezas gramíneas hospederas
• Monitoreo de poblaciones de chicharritas
• Uso de variedades tolerantes
• Barreras físicas en viveros
                    """
                },
                {
                    "nombre": "Virus del Mosaico de la Caña de Azúcar",
                    "nombre_cientifico": "Sugarcane mosaic virus (SCMV)",
                    "descripcion": "Produce patrones de mosaico con manchas verdes claras y oscuras en las hojas. Transmitido por pulgones y otras plagas.",
                    "recomendaciones": """
CONTROL DE VECTORES:
• Insecticida: Acetamiprid 20% SP
• Cantidad: 0.1-0.15 kg por hectárea (10-15 g por 100 m²)
• Aplicación: Aspersión foliar para control de pulgones
• Frecuencia: Cada 15 días durante período de mayor riesgo

MANEJO CULTURAL:
• Eliminación de plantas de caña de azúcar cercanas
• Control estricto de pulgones
• Uso de semillas certificadas libres de virus
• Rotación con cultivos no hospederos
                    """
                }
            ]

            # Insertar HONGOS
            for plaga in hongos:
                result = conn.execute(text("""
                    SELECT COUNT(*) as count FROM plagas 
                    WHERE nombre = :nombre
                """), {"nombre": plaga["nombre"]})
                
                if result.fetchone().count == 0:
                    conn.execute(text("""
                        INSERT INTO plagas (
                            nombre, nombre_cientifico, url_img, descripcion, recomendaciones,
                            id_fase_cultivo, id_tipo_plaga, id_temporada, id_usuarios
                        ) VALUES (
                            :nombre, :nombre_cientifico, :url_img, :descripcion, :recomendaciones,
                            4, 3, 1, :id_usuarios
                        )
                    """), {
                        **plaga,
                        "url_img": f"/static/images/{plaga['nombre'].lower().replace(' ', '-')}.jpg",
                        "id_usuarios": admin_user_id
                    })
                    print(f"✅ Hongo '{plaga['nombre']}' insertado.")

            # Insertar BACTERIAS
            for plaga in bacterias:
                result = conn.execute(text("""
                    SELECT COUNT(*) as count FROM plagas 
                    WHERE nombre = :nombre
                """), {"nombre": plaga["nombre"]})
                
                if result.fetchone().count == 0:
                    conn.execute(text("""
                        INSERT INTO plagas (
                            nombre, nombre_cientifico, url_img, descripcion, recomendaciones,
                            id_fase_cultivo, id_tipo_plaga, id_temporada, id_usuarios
                        ) VALUES (
                            :nombre, :nombre_cientifico, :url_img, :descripcion, :recomendaciones,
                            4, 4, 1, :id_usuarios
                        )
                    """), {
                        **plaga,
                        "url_img": f"/static/images/{plaga['nombre'].lower().replace(' ', '-')}.jpg",
                        "id_usuarios": admin_user_id
                    })
                    print(f"✅ Bacteria '{plaga['nombre']}' insertada.")

            # Insertar VIRUS
            for plaga in virus:
                result = conn.execute(text("""
                    SELECT COUNT(*) as count FROM plagas 
                    WHERE nombre = :nombre
                """), {"nombre": plaga["nombre"]})
                
                if result.fetchone().count == 0:
                    conn.execute(text("""
                        INSERT INTO plagas (
                            nombre, nombre_cientifico, url_img, descripcion, recomendaciones,
                            id_fase_cultivo, id_tipo_plaga, id_temporada, id_usuarios
                        ) VALUES (
                            :nombre, :nombre_cientifico, :url_img, :descripcion, :recomendaciones,
                            4, 5, 1, :id_usuarios
                        )
                    """), {
                        **plaga,
                        "url_img": f"/static/images/{plaga['nombre'].lower().replace(' ', '-')}.jpg",
                        "id_usuarios": admin_user_id
                    })
                    print(f"✅ Virus '{plaga['nombre']}' insertado.")

            conn.commit()
            print(f"\n🎉 Segunda parte completada! Insertados hongos, bacterias y virus.")
            
    except Exception as e:
        print(f"❌ Error al insertar plagas: {e}")

if __name__ == "__main__":
    print("🌽 Insertando plagas completas de maíz - Parte 2...")
    insertar_plagas_parte2()
