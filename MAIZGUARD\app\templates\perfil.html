<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mi Perfil - MAIZGUARD</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <!-- Estilos personalizados -->
    <link rel="stylesheet" href="/static/css/main.css">
    <link rel="stylesheet" href="/static/css/compact.css">
    <link rel="stylesheet" href="/static/css/fixed-navbar.css">
    <link rel="stylesheet" href="/static/css/super-responsive.css">
    <link rel="stylesheet" href="/static/css/small-laptop.css">
    <link rel="preload" href="/static/images/Logo.jpeg" as="image">
</head>
<body class="d-flex flex-column min-vh-100">
    <!-- Incluir el navbar -->
    {% include "partials/navbar.html" %}

    <!-- Espacio de separación después del navbar -->
    <div class="container-fluid py-3">
        <!-- Este div crea un espacio entre el navbar y el contenido -->
    </div>

    <!-- Contenido principal -->
    <main class="container my-4">
        <div class="row">
            <div class="col-md-4 mb-4">
                <div class="card shadow">
                    <div class="card-body text-center">
                        <div class="position-relative d-inline-block mb-3">
                            {% if user.fotografia %}
                            <img id="profile-image" src="{{ user.fotografia }}" alt="Foto de perfil" class="rounded-circle img-fluid border border-3 border-success shadow" style="width: 150px; height: 150px; object-fit: cover;">
                            {% else %}
                            <img id="profile-image" src="/static/images/perfil icono.jpg" alt="Foto de perfil" class="rounded-circle img-fluid border border-3 border-secondary shadow" style="width: 150px; height: 150px; object-fit: cover;">
                            {% endif %}
                            <label for="photo-upload" class="position-absolute bottom-0 end-0 bg-success text-white rounded-circle p-2 shadow-sm" style="cursor: pointer; transition: all 0.3s ease;" title="Cambiar foto de perfil" onmouseover="this.style.transform='scale(1.1)'" onmouseout="this.style.transform='scale(1)'">
                                <i class="fas fa-camera"></i>
                            </label>
                            <input type="file" id="photo-upload" name="photo" accept="image/*" class="d-none">

                            <!-- Indicador de carga -->
                            <div id="upload-spinner" class="position-absolute top-50 start-50 translate-middle d-none">
                                <div class="spinner-border text-success" role="status">
                                    <span class="visually-hidden">Subiendo...</span>
                                </div>
                            </div>
                        </div>

                        <!-- Toast para notificaciones de subida de foto -->
                        <div class="toast-container position-fixed bottom-0 end-0 p-3">
                            <div id="photoToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
                                <div class="toast-header">
                                    <strong class="me-auto" id="photoToastTitle">Subida de foto</strong>
                                    <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
                                </div>
                                <div class="toast-body" id="photoToastMessage">
                                </div>
                            </div>
                        </div>
                        <h3 class="card-title">{{ user.nombre }} {{ user.apellido }}</h3>
                        <p class="text-muted">{{ user.correo_electronico }}</p>
                        <div class="d-grid gap-2">
                            <a href="/perfil/editar" class="btn btn-outline-success">
                                <i class="fas fa-user-edit"></i> Editar perfil
                            </a>
                            <a href="/perfil/cambiar-password" class="btn btn-outline-secondary">
                                <i class="fas fa-key"></i> Cambiar contraseña
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-8">
                <div class="card shadow mb-4">
                    <div class="card-header bg-success text-white">
                        <h4 class="mb-0"><i class="fas fa-info-circle"></i> Información personal</h4>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-sm-4">
                                <strong>Nombre completo:</strong>
                            </div>
                            <div class="col-sm-8">
                                {{ user.nombre }} {{ user.apellido }}
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-sm-4">
                                <strong>Correo electrónico:</strong>
                            </div>
                            <div class="col-sm-8">
                                {{ user.correo_electronico }}
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-sm-4">
                                <strong>Fecha de registro:</strong>
                            </div>
                            <div class="col-sm-8">
                                {{ user.fecha_registro.strftime('%d/%m/%Y') }}
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-sm-4">
                                <strong>Tipo de cuenta:</strong>
                            </div>
                            <div class="col-sm-8">
                                {% if user.id_rol == 1 %}
                                <span class="badge bg-primary">Administrador</span>
                                {% else %}
                                <span class="badge bg-success">Usuario</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card shadow">
                    <div class="card-header bg-success text-white">
                        <h4 class="mb-0"><i class="fas fa-history"></i> Historial de búsquedas</h4>
                    </div>
                    <div class="card-body">
                        {% if historial_busquedas %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Término de búsqueda</th>
                                        <th>Fecha de búsqueda</th>
                                        <th>Acciones</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for busqueda in historial_busquedas %}
                                    <tr>
                                        <td>{{ busqueda.termino_busqueda }}</td>
                                        <td>{{ busqueda.fecha_busqueda.strftime('%d/%m/%Y %H:%M') }}</td>
                                        <td>
                                            <a href="/buscar?q={{ busqueda.termino_busqueda }}" class="btn btn-sm btn-outline-success">
                                                <i class="fas fa-search"></i> Buscar de nuevo
                                            </a>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-search fa-2x text-muted mb-3"></i>
                            <p class="mb-0">No has realizado búsquedas todavía.</p>
                            <p class="mt-2">
                                <a href="/" class="btn btn-success">
                                    <i class="fas fa-search"></i> Ir a la página principal
                                </a>
                            </p>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Incluir el footer -->
    {% include "partials/footer.html" %}

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js" defer></script>
    <!-- Scripts personalizados -->
    <script src="/static/js/scripts.js" defer></script>
    <script src="/static/js/script.js" defer></script>



    <!-- Script para subir foto de perfil -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const photoUpload = document.getElementById('photo-upload');
            const profileImage = document.getElementById('profile-image');
            const uploadSpinner = document.getElementById('upload-spinner');
            const photoToast = document.getElementById('photoToast');
            const photoToastMessage = document.getElementById('photoToastMessage');
            const photoToastTitle = document.getElementById('photoToastTitle');

            // Crear instancia de Bootstrap Toast
            const toast = new bootstrap.Toast(photoToast);

            // Función para mostrar notificación
            function showNotification(title, message, isError = false) {
                photoToastTitle.textContent = title;
                photoToastMessage.textContent = message;

                // Cambiar el color del toast según el tipo de mensaje
                photoToast.classList.remove('bg-danger', 'text-white', 'bg-success');
                if (isError) {
                    photoToast.classList.add('bg-danger', 'text-white');
                } else {
                    photoToast.classList.add('bg-success', 'text-white');
                }

                toast.show();
            }

            // Función para mostrar/ocultar spinner de carga
            function toggleSpinner(show) {
                if (show) {
                    uploadSpinner.classList.remove('d-none');
                    profileImage.style.opacity = '0.5';
                } else {
                    uploadSpinner.classList.add('d-none');
                    profileImage.style.opacity = '1';
                }
            }

            // Función para actualizar el borde de la imagen
            function updateImageBorder(hasPhoto) {
                profileImage.classList.remove('border-success', 'border-secondary');
                if (hasPhoto) {
                    profileImage.classList.add('border-success');
                } else {
                    profileImage.classList.add('border-secondary');
                }
            }

            // Manejar cambio en el input de archivo
            photoUpload.addEventListener('change', function() {
                if (this.files && this.files[0]) {
                    const file = this.files[0];

                    // Validar tipo de archivo
                    if (!file.type.match('image.*')) {
                        showNotification('Error', 'Por favor, selecciona una imagen válida (JPG, PNG, GIF).', true);
                        this.value = ''; // Limpiar el input
                        return;
                    }

                    // Validar tamaño (máximo 5MB)
                    if (file.size > 5 * 1024 * 1024) {
                        showNotification('Error', 'La imagen es demasiado grande. El tamaño máximo es 5MB.', true);
                        this.value = ''; // Limpiar el input
                        return;
                    }

                    // Mostrar spinner de carga
                    toggleSpinner(true);

                    // Crear FormData para enviar el archivo
                    const formData = new FormData();
                    formData.append('foto', file);

                    // Mostrar vista previa
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        profileImage.src = e.target.result;
                        updateImageBorder(true);
                    };
                    reader.readAsDataURL(file);

                    // Enviar archivo al servidor
                    fetch('/api/perfil/foto', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`Error ${response.status}: ${response.statusText}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        toggleSpinner(false);
                        showNotification('¡Éxito!', 'Foto de perfil actualizada correctamente.');

                        // Actualizar la imagen con la URL del servidor (evita problemas de caché)
                        if (data.foto_url) {
                            profileImage.src = data.foto_url + '?t=' + new Date().getTime();
                            updateImageBorder(true);
                        }
                    })
                    .catch(error => {
                        toggleSpinner(false);
                        showNotification('Error', 'No se pudo subir la imagen. Inténtalo de nuevo.', true);
                        console.error('Error:', error);

                        // Restaurar imagen anterior en caso de error
                        {% if user.fotografia %}
                        profileImage.src = "{{ user.fotografia }}";
                        updateImageBorder(true);
                        {% else %}
                        profileImage.src = "/static/images/perfil icono.jpg";
                        updateImageBorder(false);
                        {% endif %}
                    })
                    .finally(() => {
                        // Limpiar el input para permitir subir la misma imagen de nuevo
                        photoUpload.value = '';
                    });
                }
            });

            // Agregar efecto hover al botón de cámara
            const cameraButton = document.querySelector('label[for="photo-upload"]');
            cameraButton.addEventListener('mouseenter', function() {
                this.style.backgroundColor = '#198754';
                this.style.transform = 'scale(1.1)';
            });

            cameraButton.addEventListener('mouseleave', function() {
                this.style.backgroundColor = '#28a745';
                this.style.transform = 'scale(1)';
            });
        });
    </script>
</body>
</html>
