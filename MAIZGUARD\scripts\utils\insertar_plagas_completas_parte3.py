#!/usr/bin/env python3
"""
Script para insertar las plagas restantes específicas de maíz en la base de datos MaizGuard
Configuradas para fase de maduración y temporada seca
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from app.database.config import engine
from sqlalchemy import text

def insertar_plagas_parte3():
    """Insertar las plagas restantes específicas de maíz"""
    try:
        with engine.connect() as conn:
            # Obtener el ID de un usuario administrador
            result = conn.execute(text("SELECT id_usuarios FROM usuarios WHERE id_rol = 1 LIMIT 1"))
            admin_user = result.fetchone()

            if not admin_user:
                result = conn.execute(text("SELECT id_usuarios FROM usuarios LIMIT 1"))
                admin_user = result.fetchone()
                admin_user_id = admin_user.id_usuarios if admin_user else 1
            else:
                admin_user_id = admin_user.id_usuarios

            # Obtener ID del tipo de plaga "Ácaro"
            result = conn.execute(text("SELECT id_tipo_plaga FROM tipos_plaga WHERE nombre = 'Ácaro'"))
            acaro_tipo = result.fetchone()
            acaro_tipo_id = acaro_tipo.id_tipo_plaga if acaro_tipo else 6

            # INSECTOS ADICIONALES
            insectos_adicionales = [
                {
                    "nombre": "Chinche Apestosa del Maíz",
                    "nombre_cientifico": "Nezara viridula",
                    "descripcion": "Se alimenta de los granos de maíz en desarrollo, lo que puede reducir la calidad y el rendimiento de las mazorcas.",
                    "recomendaciones": """
CONTROL QUÍMICO:
• Plaguicida: Cipermetrina 25% EC
• Cantidad: 0.4-0.6 litros por hectárea (40-60 ml por 100 m²)
• Aplicación: Aspersión foliar dirigida a mazorcas
• Frecuencia: Cada 15 días durante llenado de grano

CONTROL BIOLÓGICO:
• Liberación de Trissolcus basalis (parasitoide de huevos)
• Cantidad: 2,000-3,000 individuos por hectárea
• Aplicación: Distribución en focos de infestación
• Momento: Al detectar posturas de huevos
                    """
                },
                {
                    "nombre": "Picudo del Maíz",
                    "nombre_cientifico": "Sitophilus zeamais",
                    "descripcion": "Escarabajo pequeño de color marrón oscuro con un característico pico alargado. Las larvas se desarrollan dentro de los granos, afectando su calidad y valor comercial.",
                    "recomendaciones": """
CONTROL QUÍMICO:
• Plaguicida: Fosfina (Fosfuro de aluminio)
• Cantidad: 3-5 tabletas por tonelada (0.3-0.5 tabletas por 100 kg)
• Aplicación: Fumigación en almacenes herméticos
• Frecuencia: Una aplicación por período de almacenamiento

CONTROL FÍSICO:
• Almacenamiento hermético
• Control de humedad <14%
• Temperatura <15°C
• Limpieza de instalaciones
                    """
                },
                {
                    "nombre": "Gorgojo de los Granos Almacenados",
                    "nombre_cientifico": "Sitophilus oryzae",
                    "descripcion": "Insecto pequeño, de cuerpo alargado y color marrón oscuro. Las larvas perforan los granos, afectando tanto el rendimiento como la calidad del maíz almacenado.",
                    "recomendaciones": """
CONTROL QUÍMICO:
• Plaguicida: Deltametrina 2.5% WP
• Cantidad: 0.5-1.0 kg por tonelada (50-100 g por 100 kg)
• Aplicación: Mezcla con granos antes del almacenamiento
• Método: Distribución uniforme

CONTROL PREVENTIVO:
• Secado adecuado <14% humedad
• Limpieza previa de silos
• Sellado hermético de contenedores
• Monitoreo regular de infestación
                    """
                },
                {
                    "nombre": "Trips del Maíz",
                    "nombre_cientifico": "Frankliniella williamsi",
                    "descripcion": "Estos diminutos insectos de color amarillo claro o marrón se alimentan raspando las hojas y partes aéreas de la planta, lo que puede afectar el rendimiento del cultivo.",
                    "recomendaciones": """
CONTROL QUÍMICO:
• Plaguicida: Spinosad 12% SC
• Cantidad: 0.15-0.25 litros por hectárea (15-25 ml por 100 m²)
• Aplicación: Aspersión foliar con alto volumen de agua
• Frecuencia: Cada 10-15 días según infestación

CONTROL FÍSICO:
• Trampas adhesivas azules: 15-20 trampas por hectárea
• Ubicación: A nivel del cultivo
• Renovación: Cada 15 días
• Monitoreo: Conteo semanal de capturas
                    """
                },
                {
                    "nombre": "Cuca del Maíz",
                    "nombre_cientifico": "Blissus leucopterus",
                    "descripcion": "Insecto pequeño, de color negro con alas blancas. Se alimenta de la savia del maíz, debilitando la planta y reduciendo el rendimiento del cultivo.",
                    "recomendaciones": """
CONTROL QUÍMICO:
• Plaguicida: Malation 57% EC
• Cantidad: 1.0-1.5 litros por hectárea (100-150 ml por 100 m²)
• Aplicación: Aspersión foliar dirigida a la base de plantas
• Frecuencia: Cada 15 días según población

MANEJO CULTURAL:
• Eliminación de malezas gramíneas
• Rotación con cultivos no hospederos
• Manejo adecuado de riego
• Monitoreo regular de poblaciones
                    """
                },
                {
                    "nombre": "Gusano Trozador",
                    "nombre_cientifico": "Agrotis ipsilon",
                    "descripcion": "Orugas de color marrón oscuro o gris que atacan el tallo del maíz cerca de la base, cortando las plantas jóvenes y debilitando las plantas maduras.",
                    "recomendaciones": """
CONTROL QUÍMICO:
• Plaguicida: Clorpirifós 48% EC
• Cantidad: 1.5-2.0 litros por hectárea (150-200 ml por 100 m²)
• Aplicación: Aspersión al suelo alrededor de plantas
• Frecuencia: Al detectar daños en plantas

CONTROL BIOLÓGICO:
• Aplicación de Bacillus thuringiensis
• Cantidad: 1.5-2.0 kg por hectárea (150-200 g por 100 m²)
• Aplicación: Aspersión al suelo en horas de la tarde
• Frecuencia: Cada 7 días
                    """
                }
            ]

            # ÁCAROS ADICIONALES
            acaros_adicionales = [
                {
                    "nombre": "Ácaro de las Raíces",
                    "nombre_cientifico": "Rhizoglyphus echinopus",
                    "descripcion": "Estos ácaros se alimentan de las raíces y materia orgánica en descomposición, reduciendo la capacidad de absorción de nutrientes de las plantas.",
                    "recomendaciones": """
CONTROL QUÍMICO:
• Acaricida: Dicofol 18.5% EC
• Cantidad: 1.0-1.5 litros por hectárea (100-150 ml por 100 m²)
• Aplicación: Drench al suelo alrededor de raíces
• Frecuencia: Una aplicación al detectar síntomas

MANEJO CULTURAL:
• Mejoramiento del drenaje
• Eliminación de materia orgánica en descomposición
• Rotación de cultivos
• Desinfección del suelo
                    """
                },
                {
                    "nombre": "Ácaro del Tetránico",
                    "nombre_cientifico": "Tetranychus urticae",
                    "descripcion": "Conocido como ácaro de dos manchas, es de color verde amarillento con manchas oscuras. Prefiere climas secos y daña al succionar la savia de las hojas.",
                    "recomendaciones": """
CONTROL QUÍMICO:
• Acaricida: Abamectina 1.8% EC
• Cantidad: 0.5-0.8 litros por hectárea (50-80 ml por 100 m²)
• Aplicación: Aspersión foliar con énfasis en el envés
• Frecuencia: Cada 12-15 días, máximo 2 aplicaciones

CONTROL BIOLÓGICO:
• Liberación de Phytoseiulus persimilis
• Cantidad: 5-10 individuos por m²
• Aplicación: Distribución manual en focos
• Condiciones: Humedad relativa >60%
                    """
                },
                {
                    "nombre": "Ácaro de las Espigas",
                    "nombre_cientifico": "Oligonychus mexicanus",
                    "descripcion": "Ácaro de color amarillo que ataca las espigas, debilitándolas al alimentarse de ellas. Las espigas muestran una apariencia deshidratada y los granos se ven reducidos.",
                    "recomendaciones": """
CONTROL QUÍMICO:
• Acaricida: Propargite 57% EC
• Cantidad: 1.0-1.5 litros por hectárea (100-150 ml por 100 m²)
• Aplicación: Aspersión dirigida a espigas
• Frecuencia: Al detectar primeros síntomas

MANEJO PREVENTIVO:
• Monitoreo regular de espigas
• Manejo de humedad relativa
• Eliminación de plantas hospederas alternativas
• Rotación de acaricidas
                    """
                }
            ]

            # HONGOS ADICIONALES
            hongos_adicionales = [
                {
                    "nombre": "Pudrición de Mazorca",
                    "nombre_cientifico": "Fusarium graminearum",
                    "descripcion": "Causa pudrición de la mazorca y produce deoxinivalenol (DON), una micotoxina que afecta la salud del maíz y su calidad.",
                    "recomendaciones": """
CONTROL QUÍMICO:
• Fungicida: Tebuconazol 25% EC
• Cantidad: 0.5-0.8 litros por hectárea (50-80 ml por 100 m²)
• Aplicación: Aspersión dirigida a mazorcas durante floración
• Frecuencia: 2 aplicaciones con intervalo de 15 días

MANEJO POST-COSECHA:
• Cosecha oportuna con humedad <20%
• Secado rápido <14% humedad
• Almacenamiento en condiciones secas
• Monitoreo de micotoxinas
                    """
                },
                {
                    "nombre": "Pudrición de la Raíz",
                    "nombre_cientifico": "Macrophomina phaseolina",
                    "descripcion": "Causa pudrición de la raíz y pudrición del tallo en condiciones de sequía. Este hongo puede afectar la capacidad de absorción de agua y nutrientes.",
                    "recomendaciones": """
CONTROL QUÍMICO:
• Fungicida: Carbendazim 50% WP
• Cantidad: 0.5-0.8 kg por hectárea (50-80 g por 100 m²)
• Aplicación: Drench al suelo
• Frecuencia: Al detectar síntomas

MANEJO CULTURAL:
• Mejoramiento del drenaje
• Rotación con cultivos no hospederos
• Manejo adecuado del riego
• Uso de variedades resistentes
                    """
                },
                {
                    "nombre": "Manchas Negras",
                    "nombre_cientifico": "Alternaria helianthi",
                    "descripcion": "Provoca manchas negruzcas en las hojas y mazorcas, afectando la calidad del grano. Aunque es más común en girasol, también puede afectar al maíz.",
                    "recomendaciones": """
CONTROL QUÍMICO:
• Fungicida: Difenoconazol 25% EC
• Cantidad: 0.3-0.5 litros por hectárea (30-50 ml por 100 m²)
• Aplicación: Aspersión foliar al detectar síntomas
• Frecuencia: Cada 15 días según severidad

MANEJO PREVENTIVO:
• Eliminación de residuos infectados
• Rotación de cultivos
• Mejoramiento de ventilación
• Uso de variedades resistentes
                    """
                },
                {
                    "nombre": "Moho Gris",
                    "nombre_cientifico": "Cladosporium spp.",
                    "descripcion": "Causa manchas grisáceas en las hojas y mazorcas. Aunque es menos frecuente, puede aparecer en condiciones de sequía y alta temperatura.",
                    "recomendaciones": """
CONTROL QUÍMICO:
• Fungicida: Mancozeb 80% WP
• Cantidad: 2.0-2.5 kg por hectárea (200-250 g por 100 m²)
• Aplicación: Aspersión foliar preventiva
• Frecuencia: Cada 20 días en condiciones favorables

MANEJO AMBIENTAL:
• Control de humedad relativa
• Mejoramiento de ventilación
• Eliminación de material infectado
• Monitoreo regular de síntomas
                    """
                }
            ]

            # Insertar INSECTOS ADICIONALES
            for plaga in insectos_adicionales:
                result = conn.execute(text("""
                    SELECT COUNT(*) as count FROM plagas
                    WHERE nombre = :nombre
                """), {"nombre": plaga["nombre"]})

                if result.fetchone().count == 0:
                    conn.execute(text("""
                        INSERT INTO plagas (
                            nombre, nombre_cientifico, url_img, descripcion, recomendaciones,
                            id_fase_cultivo, id_tipo_plaga, id_temporada, id_usuarios
                        ) VALUES (
                            :nombre, :nombre_cientifico, :url_img, :descripcion, :recomendaciones,
                            4, 1, 1, :id_usuarios
                        )
                    """), {
                        **plaga,
                        "url_img": f"/static/images/{plaga['nombre'].lower().replace(' ', '-')}.jpg",
                        "id_usuarios": admin_user_id
                    })
                    print(f"✅ Insecto '{plaga['nombre']}' insertado.")

            # Insertar ÁCAROS ADICIONALES
            for plaga in acaros_adicionales:
                result = conn.execute(text("""
                    SELECT COUNT(*) as count FROM plagas
                    WHERE nombre = :nombre
                """), {"nombre": plaga["nombre"]})

                if result.fetchone().count == 0:
                    conn.execute(text("""
                        INSERT INTO plagas (
                            nombre, nombre_cientifico, url_img, descripcion, recomendaciones,
                            id_fase_cultivo, id_tipo_plaga, id_temporada, id_usuarios
                        ) VALUES (
                            :nombre, :nombre_cientifico, :url_img, :descripcion, :recomendaciones,
                            4, :id_tipo_plaga, 1, :id_usuarios
                        )
                    """), {
                        **plaga,
                        "url_img": f"/static/images/{plaga['nombre'].lower().replace(' ', '-')}.jpg",
                        "id_tipo_plaga": acaro_tipo_id,
                        "id_usuarios": admin_user_id
                    })
                    print(f"✅ Ácaro '{plaga['nombre']}' insertado.")

            # Insertar HONGOS ADICIONALES
            for plaga in hongos_adicionales:
                result = conn.execute(text("""
                    SELECT COUNT(*) as count FROM plagas
                    WHERE nombre = :nombre
                """), {"nombre": plaga["nombre"]})

                if result.fetchone().count == 0:
                    conn.execute(text("""
                        INSERT INTO plagas (
                            nombre, nombre_cientifico, url_img, descripcion, recomendaciones,
                            id_fase_cultivo, id_tipo_plaga, id_temporada, id_usuarios
                        ) VALUES (
                            :nombre, :nombre_cientifico, :url_img, :descripcion, :recomendaciones,
                            4, 3, 1, :id_usuarios
                        )
                    """), {
                        **plaga,
                        "url_img": f"/static/images/{plaga['nombre'].lower().replace(' ', '-')}.jpg",
                        "id_usuarios": admin_user_id
                    })
                    print(f"✅ Hongo '{plaga['nombre']}' insertado.")

            # BACTERIAS ADICIONALES
            bacterias_adicionales = [
                {
                    "nombre": "Bacilo Cereus",
                    "nombre_cientifico": "Bacillus cereus",
                    "descripcion": "Bacteria grampositiva que puede causar podredumbre en el maíz bajo condiciones húmedas.",
                    "recomendaciones": """
CONTROL QUÍMICO:
• Bactericida: Oxicloruro de cobre 50% WP
• Cantidad: 2.0-3.0 kg por hectárea (200-300 g por 100 m²)
• Aplicación: Aspersión foliar preventiva
• Frecuencia: Cada 15 días en condiciones húmedas

MANEJO PREVENTIVO:
• Mejoramiento del drenaje
• Evitar exceso de humedad
• Rotación de cultivos
• Eliminación de residuos infectados
                    """
                },
                {
                    "nombre": "Pseudomonas",
                    "nombre_cientifico": "Pseudomonas syringae",
                    "descripcion": "Causa manchas bacterianas en las hojas, afectando la fotosíntesis.",
                    "recomendaciones": """
CONTROL QUÍMICO:
• Bactericida: Estreptomicina 20% + Oxitetraciclina 1.5%
• Cantidad: 0.5-0.8 kg por hectárea (50-80 g por 100 m²)
• Aplicación: Aspersión foliar
• Frecuencia: Cada 10 días al detectar síntomas

MANEJO INTEGRADO:
• Desinfección de herramientas
• Eliminación de plantas infectadas
• Control de humedad foliar
• Uso de variedades resistentes
                    """
                },
                {
                    "nombre": "Xanthomonas",
                    "nombre_cientifico": "Xanthomonas campestris pv. zeae",
                    "descripcion": "Provoca marchitez bacteriana, con manchas y estrías en las hojas.",
                    "recomendaciones": """
CONTROL QUÍMICO:
• Bactericida: Kasugamicina 2% SL
• Cantidad: 1.0-1.5 litros por hectárea (100-150 ml por 100 m²)
• Aplicación: Aspersión foliar
• Frecuencia: Cada 10-15 días según severidad

CONTROL PREVENTIVO:
• Uso de semillas certificadas
• Eliminación de malezas hospederas
• Desinfección de equipos
• Rotación con cultivos no hospederos
                    """
                },
                {
                    "nombre": "Podredumbre Húmeda",
                    "nombre_cientifico": "Erwinia amylovora",
                    "descripcion": "Provoca podredumbre maloliente y puede causar daños significativos a la planta.",
                    "recomendaciones": """
CONTROL QUÍMICO:
• Bactericida: Sulfato de cobre pentahidratado 25% WP
• Cantidad: 1.5-2.0 kg por hectárea (150-200 g por 100 m²)
• Aplicación: Aspersión foliar preventiva
• Frecuencia: Cada 15 días en condiciones favorables

MANEJO SANITARIO:
• Eliminación inmediata de plantas infectadas
• Desinfección de herramientas con alcohol 70%
• Mejoramiento del drenaje
• Control de vectores
                    """
                }
            ]

            # VIRUS ADICIONALES
            virus_adicionales = [
                {
                    "nombre": "Virus de la Estría Rugosa del Maíz",
                    "nombre_cientifico": "Maize rough dwarf virus (MRDV)",
                    "descripcion": "Provoca un crecimiento enano en las plantas y estrías en las hojas. Es transmitido por insectos vectores como los saltamontes.",
                    "recomendaciones": """
CONTROL DE VECTORES:
• Insecticida: Lambda-cihalotrina 2.5% EC
• Cantidad: 0.3-0.5 litros por hectárea (30-50 ml por 100 m²)
• Aplicación: Aspersión foliar para control de saltamontes
• Frecuencia: Cada 15 días durante período crítico

MANEJO PREVENTIVO:
• Control de malezas gramíneas
• Eliminación de plantas infectadas
• Uso de variedades resistentes
• Monitoreo de poblaciones de vectores
                    """
                },
                {
                    "nombre": "Virus del Rayado Clorótico del Maíz",
                    "nombre_cientifico": "Maize chlorotic mottle virus (MCMV)",
                    "descripcion": "Se presenta con rayas cloróticas o amarillentas en las hojas. Puede combinarse con otros virus y causar la enfermedad del marchitamiento letal del maíz.",
                    "recomendaciones": """
CONTROL DE VECTORES:
• Insecticida: Thiamethoxam 25% WG
• Cantidad: 0.2-0.3 kg por hectárea (20-30 g por 100 m²)
• Aplicación: Aspersión foliar para control de vectores
• Frecuencia: Cada 10-15 días según presión

MANEJO INTEGRADO:
• Eliminación de plantas infectadas
• Control estricto de vectores
• Uso de semillas certificadas
• Rotación con cultivos no hospederos
                    """
                },
                {
                    "nombre": "Virus del Achaparramiento del Maíz",
                    "nombre_cientifico": "Maize streak virus (MSV)",
                    "descripcion": "Provoca un crecimiento reducido en las plantas con manchas amarillas y blancas en las hojas. El virus es transmitido por chicharritas.",
                    "recomendaciones": """
CONTROL DE VECTORES:
• Insecticida: Acetamiprid 20% SP
• Cantidad: 0.1-0.15 kg por hectárea (10-15 g por 100 m²)
• Aplicación: Aspersión foliar para control de chicharritas
• Frecuencia: Cada 15 días durante período de riesgo

MANEJO CULTURAL:
• Eliminación de malezas hospederas
• Control temprano de vectores
• Uso de variedades tolerantes
• Siembra en fechas óptimas
                    """
                }
            ]

            # Insertar BACTERIAS ADICIONALES
            for plaga in bacterias_adicionales:
                result = conn.execute(text("""
                    SELECT COUNT(*) as count FROM plagas
                    WHERE nombre = :nombre
                """), {"nombre": plaga["nombre"]})

                if result.fetchone().count == 0:
                    conn.execute(text("""
                        INSERT INTO plagas (
                            nombre, nombre_cientifico, url_img, descripcion, recomendaciones,
                            id_fase_cultivo, id_tipo_plaga, id_temporada, id_usuarios
                        ) VALUES (
                            :nombre, :nombre_cientifico, :url_img, :descripcion, :recomendaciones,
                            4, 4, 1, :id_usuarios
                        )
                    """), {
                        **plaga,
                        "url_img": f"/static/images/{plaga['nombre'].lower().replace(' ', '-')}.jpg",
                        "id_usuarios": admin_user_id
                    })
                    print(f"✅ Bacteria '{plaga['nombre']}' insertada.")

            # Insertar VIRUS ADICIONALES
            for plaga in virus_adicionales:
                result = conn.execute(text("""
                    SELECT COUNT(*) as count FROM plagas
                    WHERE nombre = :nombre
                """), {"nombre": plaga["nombre"]})

                if result.fetchone().count == 0:
                    conn.execute(text("""
                        INSERT INTO plagas (
                            nombre, nombre_cientifico, url_img, descripcion, recomendaciones,
                            id_fase_cultivo, id_tipo_plaga, id_temporada, id_usuarios
                        ) VALUES (
                            :nombre, :nombre_cientifico, :url_img, :descripcion, :recomendaciones,
                            4, 5, 1, :id_usuarios
                        )
                    """), {
                        **plaga,
                        "url_img": f"/static/images/{plaga['nombre'].lower().replace(' ', '-')}.jpg",
                        "id_usuarios": admin_user_id
                    })
                    print(f"✅ Virus '{plaga['nombre']}' insertado.")

            conn.commit()
            print(f"\n🎉 Tercera parte completada! Insertadas todas las plagas adicionales.")

    except Exception as e:
        print(f"❌ Error al insertar plagas: {e}")

if __name__ == "__main__":
    print("🌽 Insertando plagas completas de maíz - Parte 3...")
    insertar_plagas_parte3()
