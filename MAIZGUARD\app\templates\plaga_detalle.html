<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ plaga.nombre }} - MAIZGUARD</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <!-- Estilos personalizados -->
    <link rel="stylesheet" href="/static/css/main.css">
    <link rel="stylesheet" href="/static/css/navbar.css">
    <link rel="stylesheet" href="/static/css/compact.css">
    <link rel="stylesheet" href="/static/css/super-responsive.css">
    <link rel="stylesheet" href="/static/css/admin-panel.css">
    <link rel="preload" href="/static/images/Logo.jpeg" as="image">
    <!-- Precargar la imagen de la plaga para mejorar el rendimiento -->
    {% if plaga.url_img %}
    <link rel="preload" href="{{ plaga.url_img }}" as="image">
    {% endif %}

    <style>
        .plaga-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }

        .plaga-card {
            border: none;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .plaga-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
        }

        .plaga-img {
            height: 300px;
            object-fit: cover;
            border-radius: 15px;
        }

        .info-card {
            background: linear-gradient(135deg, rgba(46, 204, 64, 0.1) 0%, rgba(255, 255, 255, 0.95) 100%);
            border: none;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }

        .recommendation-card {
            background: linear-gradient(135deg, rgba(255, 87, 34, 0.1) 0%, rgba(255, 255, 255, 0.95) 100%);
            border: none;
            border-radius: 15px;
            padding: 1.5rem;
        }

        .badge-custom {
            font-size: 0.9rem;
            padding: 0.6rem 1rem;
            border-radius: 25px;
            margin: 0.2rem;
        }
    </style>
</head>
<body class="d-flex flex-column min-vh-100">
    <!-- Incluir el navbar -->
    {% include "partials/navbar.html" %}

    <!-- Contenido principal -->
    <!-- Encabezado de la plaga -->
    <div class="plaga-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8 mx-auto text-center">
                    <h2 class="fw-bold mb-2 text-white">
                        <i class="fas fa-bug me-2"></i>{{ plaga.nombre }}
                    </h2>
                    {% if plaga.nombre_cientifico %}
                    <p class="mb-2 text-white">
                        <em>{{ plaga.nombre_cientifico }}</em>
                    </p>
                    {% endif %}
                    <div class="d-flex justify-content-center flex-wrap">
                        <span class="badge-custom bg-primary text-white">
                            <i class="fas fa-tag me-1"></i>{{ plaga.tipo_plaga or 'Sin clasificar' }}
                        </span>
                        <span class="badge-custom bg-success text-white">
                            <i class="fas fa-leaf me-1"></i>{{ plaga.fase_cultivo or 'Todas las fases' }}
                        </span>
                        <span class="badge-custom bg-warning text-dark">
                            <i class="fas fa-calendar me-1"></i>{{ plaga.temporada or 'Todo el año' }}
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <main class="container my-5">
        <div class="row">
            <div class="col-lg-4 mb-4">
                <div class="plaga-card">
                    {% if plaga.url_img %}
                        <img src="{{ plaga.url_img }}" class="plaga-img w-100" alt="{{ plaga.nombre }}" loading="eager">
                    {% else %}
                        <div class="plaga-img w-100 bg-light d-flex align-items-center justify-content-center">
                            <i class="fas fa-bug fa-5x text-muted"></i>
                        </div>
                    {% endif %}

                    <div class="card-body">
                        {% if user_id %}
                        <div class="alert alert-success border-0" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            <strong>Consulta registrada</strong><br>
                            <small>Esta información ha sido guardada en tu historial.</small>
                        </div>
                        {% endif %}

                        <div class="text-center">
                            <a href="/buscar" class="btn btn-outline-primary me-2">
                                <i class="fas fa-search me-1"></i>Nueva Búsqueda
                            </a>
                            <a href="/" class="btn btn-outline-success">
                                <i class="fas fa-home me-1"></i>Inicio
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-8">
                <div class="card shadow mb-4">
                    <div class="card-header bg-success text-white">
                        <h4 class="mb-0"><i class="fas fa-info-circle"></i> Descripción</h4>
                    </div>
                    <div class="card-body">
                        <p>{{ plaga.descripcion }}</p>
                    </div>
                </div>

                <div class="card shadow">
                    <div class="card-header bg-success text-white">
                        <h4 class="mb-0"><i class="fas fa-shield-alt"></i> Recomendaciones</h4>
                    </div>
                    <div class="card-body">
                        <p>{{ plaga.recomendaciones }}</p>
                    </div>
                </div>

                {% if user_id %}
                <div class="mt-4 text-end">
                    <a href="/perfil" class="btn btn-outline-success">
                        <i class="fas fa-history"></i> Ver mi historial de consultas
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </main>

    <!-- Incluir el footer -->
    {% include "partials/footer.html" %}

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js" defer></script>
    <!-- Scripts personalizados -->
    <script src="/static/js/scripts.js" defer></script>
    <script src="/static/js/script.js" defer></script>
</body>
</html>
