/* Estilos para mejorar la visibilidad de los formularios */

/* Estilos para los inputs */
.form-control {
    border: 2px solid #ced4da !important;
    border-radius: 6px !important;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-control:focus {
    border-color: #28a745 !important;
    box-shadow: 0 0 0 0.25rem rgba(40, 167, 69, 0.25) !important;
}

/* Estilos para los inputs flotantes */
.form-floating > .form-control {
    padding: 1rem 0.75rem;
    height: calc(3.5rem + 2px) !important;
}

.form-floating > .form-control:focus {
    border-color: #28a745 !important;
}

.form-floating > label {
    padding: 1rem 0.75rem;
    color: #6c757d;
    font-weight: 500;
}

/* Estilos para los checkboxes */
.form-check-input {
    border: 2px solid #28a745 !important;
    width: 1.2em;
    height: 1.2em;
}

.form-check-input:checked {
    background-color: #28a745 !important;
    border-color: #28a745 !important;
}

/* Estilos para los botones */
.btn-success {
    background-color: #28a745 !important;
    border-color: #28a745 !important;
    font-weight: 600;
    padding: 0.5rem 1.5rem;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.btn-success:hover {
    background-color: #218838 !important;
    border-color: #1e7e34 !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Estilos para los formularios en tarjetas */
.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.card-header {
    background-color: #28a745 !important;
    color: white !important;
    border-bottom: none;
    border-radius: 10px 10px 0 0 !important;
}

.card-body {
    padding: 2rem !important;
}

.card-footer {
    background-color: #f8f9fa;
    border-top: none;
    border-radius: 0 0 10px 10px !important;
}

/* Estilos para los mensajes de error y éxito */
.alert-danger {
    border-left: 4px solid #dc3545;
}

.alert-success {
    border-left: 4px solid #28a745;
}

/* Estilos para los enlaces */
a.text-success {
    color: #28a745 !important;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
}

a.text-success:hover {
    color: #218838 !important;
    text-decoration: underline;
}

/* Estilos para dispositivos móviles */
@media (max-width: 768px) {
    .form-floating > .form-control {
        height: calc(3.2rem + 2px) !important;
    }

    .card-body {
        padding: 1.5rem !important;
    }
}

/* Estilos para botones de mostrar/ocultar contraseña */
.input-group .btn-outline-secondary {
    border-color: #ced4da !important;
    color: #6c757d !important;
    transition: all 0.3s ease;
    border-left: none !important;
    background-color: white;
}

.input-group .btn-outline-secondary:hover {
    background-color: #e9ecef !important;
    border-color: #28a745 !important;
    color: #28a745 !important;
}

.input-group .btn-outline-secondary:focus {
    box-shadow: 0 0 0 0.25rem rgba(40, 167, 69, 0.25) !important;
    border-color: #28a745 !important;
    color: #28a745 !important;
}

.input-group .btn-outline-secondary:active {
    background-color: #28a745 !important;
    border-color: #28a745 !important;
    color: white !important;
}

.input-group .btn-outline-secondary i {
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

/* Estilos para campos de contraseña en input-group */
.input-group input[type="password"],
.input-group input[type="text"] {
    border-right: none !important;
}

.input-group input[type="password"]:focus,
.input-group input[type="text"]:focus {
    border-color: #28a745 !important;
    box-shadow: none !important;
}

.input-group input[type="password"]:focus + .btn-outline-secondary,
.input-group input[type="text"]:focus + .btn-outline-secondary {
    border-color: #28a745 !important;
    border-left: none !important;
}

/* Estilos específicos para botones de contraseña */
.toggle-password {
    cursor: pointer;
    -webkit-user-select: none;
    user-select: none;
}

.toggle-password:hover i {
    transform: scale(1.1);
}

/* Animación suave para el cambio de icono */
.toggle-password i {
    transition: transform 0.2s ease, color 0.3s ease;
}

/* Estilos para mejorar la apariencia en diferentes estados */
.input-group:focus-within .btn-outline-secondary {
    border-color: #28a745 !important;
    z-index: 3;
}

/* Estilos específicos para los botones con ID que empiecen por togglePassword */
[id^="togglePassword"] {
    border-radius: 0 6px 6px 0 !important;
}

[id^="togglePassword"]:hover {
    background-color: #f8f9fa !important;
    border-color: #28a745 !important;
    color: #28a745 !important;
}

/* Estilos para contenedor de contraseña con ojito interno */
.password-input-container {
    position: relative;
    display: block;
    width: 100%;
}

.password-input-container .password-input {
    width: 100%;
    padding-right: 50px !important;
    border-radius: 6px !important;
    border: 1px solid #ced4da;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.password-input-container .password-toggle {
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    color: #6c757d;
    font-size: 1.1rem;
    transition: all 0.2s ease-in-out;
    z-index: 5;
    -webkit-user-select: none;
    user-select: none;
    padding: 4px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
}

.password-input-container .password-toggle:hover {
    color: #28a745;
    background-color: rgba(40, 167, 69, 0.1);
    transform: translateY(-50%);
}

.password-input-container .password-toggle:active {
    transform: translateY(-50%) scale(0.9);
    background-color: rgba(40, 167, 69, 0.2);
}

/* Estados del icono de contraseña */
.password-toggle.fa-eye {
    opacity: 0.8;
}

.password-toggle.fa-eye-slash {
    opacity: 1;
    color: #28a745 !important;
}

/* Efecto cuando la contraseña está visible */
.password-input-container .password-input[type="text"] + .password-toggle {
    color: #28a745;
    background-color: rgba(40, 167, 69, 0.1);
}

/* Efecto de focus en el input con ojito */
.password-input-container .password-input:focus {
    border-color: #28a745 !important;
    box-shadow: 0 0 0 0.25rem rgba(40, 167, 69, 0.25) !important;
}

.password-input-container .password-input:focus + .password-toggle {
    color: #28a745;
}

/* Animación para el cambio de icono */
.password-toggle {
    transition: all 0.2s ease-in-out;
}

/* Responsive para móviles */
@media (max-width: 768px) {
    .password-input-container .password-toggle {
        right: 12px;
        font-size: 1.1rem;
    }

    .password-input-container .password-input {
        padding-right: 40px !important;
    }
}

/* Accesibilidad mejorada */
.password-toggle:focus {
    outline: 2px solid #28a745;
    outline-offset: 2px;
    border-radius: 3px;
}

/* Compatibilidad con temas oscuros */
@media (prefers-color-scheme: dark) {
    .password-toggle {
        color: #adb5bd;
    }

    .password-toggle:hover {
        color: #28a745;
    }
}
