<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestión de Plagas - MAIZGUARD</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Estilos personalizados - EXACTAMENTE IGUAL QUE INICIO -->
    <link rel="stylesheet" href="/static/css/main.css">
    <link rel="stylesheet" href="/static/css/navbar.css">
    <link rel="stylesheet" href="/static/css/compact.css">
    <link rel="stylesheet" href="/static/css/super-responsive.css">
    <link rel="stylesheet" href="/static/css/small-laptop.css">
    <link rel="stylesheet" href="/static/css/admin.css">
    <link rel="stylesheet" href="/static/css/admin-panel.css">
</head>
<body id="admin-app" class="admin-page">
    <!-- Incluir el navbar normal -->
    {% include "partials/navbar.html" %}

    <!-- Contenedor principal sin espaciado extra -->
    <div class="container-fluid admin-container">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block admin-sidebar">
                <div class="sidebar-content">
                    <div class="text-center mb-4">
                        <h5 class="text-white">Panel de Administración</h5>
                        <p class="text-light small">Bienvenido, {{ user.nombre }}</p>
                    </div>

                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="sidebar-link" href="/admin">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="sidebar-link" href="/admin/usuarios">
                                <i class="fas fa-users me-2"></i>
                                Usuarios
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="sidebar-link" href="/admin/cultivos-usuarios">
                                <i class="fas fa-seedling me-2"></i>
                                Cultivos de Usuarios
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="sidebar-link active" href="/admin/plagas">
                                <i class="fas fa-bug me-2"></i>
                                Plagas
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="sidebar-link" href="/">
                                <i class="fas fa-home me-2"></i>
                                Volver al Inicio
                            </a>
                        </li>
                        <li class="nav-item mt-3">
                            <a class="sidebar-link text-danger" href="/logout">
                                <i class="fas fa-sign-out-alt me-2"></i>
                                Cerrar Sesión
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Contenido principal -->
            <main class="col-md-9 col-lg-10 admin-main-content">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Gestión de Plagas</h1>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#modalAgregarPlaga">
                        <i class="fas fa-plus me-2"></i>Agregar Plaga
                    </button>
                </div>

                <!-- Estadísticas de plagas -->
                <div class="row mb-4">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <div class="text-white-50 small">Total Plagas</div>
                                        <div class="h4 mb-0" id="total-plagas">Cargando...</div>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-bug fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <div class="text-white-50 small">Insectos</div>
                                        <div class="h4 mb-0" id="total-insectos">Cargando...</div>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-spider fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card bg-danger text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <div class="text-white-50 small">Hongos</div>
                                        <div class="h4 mb-0" id="total-hongos">Cargando...</div>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-leaf fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <div class="text-white-50 small">Otros</div>
                                        <div class="h4 mb-0" id="total-otros">Cargando...</div>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-virus fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filtros y búsqueda de plagas -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3 bg-gradient-primary">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <h6 class="m-0 font-weight-bold text-white">
                                    <i class="fas fa-bug me-2"></i>Plagas Registradas
                                </h6>
                            </div>
                            <div class="col-md-6">
                                <div class="input-group">
                                    <input type="text" class="form-control" id="buscarPlaga" placeholder="Buscar plaga...">
                                    <button class="btn btn-outline-light" type="button" onclick="filtrarPlagas()">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <!-- Filtros adicionales -->
                        <div class="row p-3 bg-light border-bottom">
                            <div class="col-md-3">
                                <select class="form-select form-select-sm" id="filtroTipo" onchange="filtrarPlagas()">
                                    <option value="">Todos los tipos</option>
                                    <option value="INSECTOS">Insectos</option>
                                    <option value="HONGOS">Hongos</option>
                                    <option value="ÁCAROS">Ácaros</option>
                                    <option value="BACTERIAS">Bacterias</option>
                                    <option value="VIRUS">Virus</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select form-select-sm" id="filtroFasePlaga" onchange="filtrarPlagas()">
                                    <option value="">Todas las fases</option>
                                    <option value="maduración">Maduración</option>
                                    <option value="cosecha">Cosecha</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <button type="button" class="btn btn-outline-success btn-sm" onclick="exportarPlagas()">
                                    <i class="fas fa-download me-1"></i>Exportar
                                </button>
                            </div>
                            <div class="col-md-3 text-end">
                                <span class="badge bg-info" id="contadorPlagas">0 plagas</span>
                            </div>
                        </div>

                        <!-- Grid de plagas mejorado -->
                        <div class="p-3">
                            <div class="row" id="plagas-grid">
                                <div class="col-12 text-center py-5">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Cargando...</span>
                                    </div>
                                    <p class="mt-2 text-muted">Cargando plagas...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Modal para agregar plaga -->
    <div class="modal fade" id="modalAgregarPlaga" tabindex="-1" aria-labelledby="modalAgregarPlagaLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalAgregarPlagaLabel">Agregar Nueva Plaga</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="formAgregarPlaga">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="nombre" class="form-label">Nombre de la Plaga *</label>
                                <input type="text" class="form-control" id="nombre" name="nombre" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="tipo" class="form-label">Tipo *</label>
                                <select class="form-select" id="tipo" name="tipo" required>
                                    <option value="">Seleccionar tipo</option>
                                    <option value="INSECTOS">Insectos</option>
                                    <option value="ÁCAROS">Ácaros</option>
                                    <option value="HONGOS">Hongos</option>
                                    <option value="BACTERIAS">Bacterias</option>
                                    <option value="VIRUS">Virus</option>
                                </select>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="fase_cultivo" class="form-label">Fase del Cultivo *</label>
                                <select class="form-select" id="fase_cultivo" name="fase_cultivo" required>
                                    <option value="">Seleccionar fase</option>
                                    <option value="maduración">Maduración</option>
                                    <option value="cosecha">Cosecha</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="estacion" class="form-label">Estación *</label>
                                <select class="form-select" id="estacion" name="estacion" required>
                                    <option value="">Seleccionar estación</option>
                                    <option value="seca">Estación Seca</option>
                                    <option value="lluviosa">Estación Lluviosa</option>
                                </select>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="descripcion" class="form-label">Descripción *</label>
                            <textarea class="form-control" id="descripcion" name="descripcion" rows="3" required></textarea>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="tipo_plaguicida" class="form-label">Tipo de Plaguicida *</label>
                                <input type="text" class="form-control" id="tipo_plaguicida" name="tipo_plaguicida" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="cantidad_metros_cubicos" class="form-label">Cantidad (m³) *</label>
                                <input type="number" step="0.01" class="form-control" id="cantidad_metros_cubicos" name="cantidad_metros_cubicos" required>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="forma_aplicacion" class="form-label">Forma de Aplicación *</label>
                            <textarea class="form-control" id="forma_aplicacion" name="forma_aplicacion" rows="2" required></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="imagen" class="form-label">Imagen de la Plaga</label>
                            <input type="file" class="form-control" id="imagen" name="imagen" accept="image/*">
                            <div class="form-text">Formatos permitidos: JPG, PNG, GIF. Máximo 5MB.</div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="button" class="btn btn-primary" onclick="guardarPlaga()">Guardar Plaga</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- JavaScript para plagas -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            cargarPlagas();
            cargarEstadisticasPlagas();
        });

        // Variables globales para filtrado de plagas
        let plagasOriginales = [];
        let plagasFiltradas = [];

        async function cargarPlagas() {
            try {
                const response = await fetch('/admin/api/plagas');
                if (response.ok) {
                    const data = await response.json();
                    mostrarPlagas(data.plagas || data);
                } else {
                    const grid = document.getElementById('plagas-grid');
                    grid.innerHTML = '<div class="col-12 text-center text-danger py-5"><i class="fas fa-exclamation-triangle fa-3x mb-3"></i><h5>Error al cargar plagas</h5></div>';
                }
            } catch (error) {
                console.error('Error:', error);
                const grid = document.getElementById('plagas-grid');
                grid.innerHTML = '<div class="col-12 text-center text-danger py-5"><i class="fas fa-wifi fa-3x mb-3"></i><h5>Error de conexión</h5></div>';
            }
        }

        function mostrarPlagas(plagas) {
            plagasOriginales = plagas;
            plagasFiltradas = [...plagas];

            const grid = document.getElementById('plagas-grid');
            const contador = document.getElementById('contadorPlagas');

            if (!plagas || plagas.length === 0) {
                grid.innerHTML = `
                    <div class="col-12 text-center py-5">
                        <i class="fas fa-bug fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No hay plagas registradas</h5>
                        <p class="text-muted">Aún no se han registrado plagas en el sistema.</p>
                    </div>
                `;
                contador.textContent = '0 plagas';
                return;
            }

            contador.textContent = `${plagas.length} plaga${plagas.length !== 1 ? 's' : ''}`;

            let html = '';
            plagas.forEach(plaga => {
                const tipoColor = getTipoColor(plaga.tipo);
                const imagenUrl = plaga.imagen_url || '/static/images/placeholder-plaga.jpg';

                html += `
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="card h-100 shadow-sm border-0 cultivo-card">
                            <div class="card-header bg-gradient-${tipoColor} text-white">
                                <div class="row align-items-center">
                                    <div class="col">
                                        <h6 class="mb-0 fw-bold text-white">
                                            <i class="fas fa-bug me-2"></i>${plaga.nombre || 'Sin nombre'}
                                        </h6>
                                    </div>
                                    <div class="col-auto">
                                        <span class="badge bg-light text-dark">#${plaga.id_plaga}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="row mb-3">
                                    <div class="col-4">
                                        <div class="text-center">
                                            <img src="${imagenUrl}" alt="Plaga" class="rounded" style="width: 60px; height: 60px; object-fit: cover;">
                                        </div>
                                    </div>
                                    <div class="col-8">
                                        <div class="mb-2">
                                            <span class="badge bg-${tipoColor} fs-6 px-3 py-2">
                                                <i class="fas fa-tag me-1"></i>${plaga.tipo || 'Sin tipo'}
                                            </span>
                                        </div>
                                        <div class="mb-2">
                                            <span class="badge bg-info">
                                                <i class="fas fa-leaf me-1"></i>${plaga.fase_cultivo || 'N/A'}
                                            </span>
                                        </div>
                                        <div>
                                            <span class="badge bg-warning text-dark">
                                                <i class="fas fa-calendar me-1"></i>${plaga.estacion || 'Sin temporada'}
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <p class="small text-muted mb-0">
                                        <strong>Descripción:</strong><br>
                                        ${(plaga.descripcion || 'Sin descripción').substring(0, 100)}${plaga.descripcion && plaga.descripcion.length > 100 ? '...' : ''}
                                    </p>
                                </div>
                            </div>
                            <div class="card-footer bg-light">
                                <div class="btn-group w-100" role="group">
                                    <button type="button" class="btn btn-outline-info btn-sm" onclick="verDetallesPlaga(${plaga.id_plaga})" title="Ver detalles">
                                        <i class="fas fa-eye me-1"></i>Ver
                                    </button>
                                    <button type="button" class="btn btn-outline-warning btn-sm" onclick="editarPlaga(${plaga.id_plaga})" title="Editar">
                                        <i class="fas fa-edit me-1"></i>Editar
                                    </button>
                                    <button type="button" class="btn btn-outline-danger btn-sm" onclick="eliminarPlaga(${plaga.id_plaga})" title="Eliminar">
                                        <i class="fas fa-trash me-1"></i>Eliminar
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });

            grid.innerHTML = html;
        }

        function getTipoColor(tipo) {
            switch(tipo?.toUpperCase()) {
                case 'INSECTOS': return 'warning';
                case 'HONGOS': return 'danger';
                case 'ÁCAROS': return 'info';
                case 'BACTERIAS': return 'success';
                case 'VIRUS': return 'secondary';
                default: return 'primary';
            }
        }

        // Función para filtrar plagas
        function filtrarPlagas() {
            const busqueda = document.getElementById('buscarPlaga').value.toLowerCase();
            const filtroTipo = document.getElementById('filtroTipo').value;
            const filtroFase = document.getElementById('filtroFasePlaga').value;

            plagasFiltradas = plagasOriginales.filter(plaga => {
                const coincideBusqueda = !busqueda ||
                    (plaga.nombre && plaga.nombre.toLowerCase().includes(busqueda)) ||
                    (plaga.descripcion && plaga.descripcion.toLowerCase().includes(busqueda));

                const coincideTipo = !filtroTipo || plaga.tipo === filtroTipo;
                const coincideFase = !filtroFase || plaga.fase_cultivo === filtroFase;

                return coincideBusqueda && coincideTipo && coincideFase;
            });

            mostrarPlagas(plagasFiltradas);
        }

        // Función para exportar plagas
        function exportarPlagas() {
            if (plagasFiltradas.length === 0) {
                alert('No hay plagas para exportar');
                return;
            }

            const csv = [
                ['ID', 'Nombre', 'Tipo', 'Descripción', 'Fase', 'Temporada'].join(','),
                ...plagasFiltradas.map(p => [
                    p.id_plaga,
                    `"${p.nombre || ''}"`,
                    `"${p.tipo || ''}"`,
                    `"${(p.descripcion || '').replace(/"/g, '""')}"`,
                    `"${p.fase_cultivo || ''}"`,
                    `"${p.estacion || ''}"`
                ].join(','))
            ].join('\n');

            const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = `plagas_${new Date().toISOString().split('T')[0]}.csv`;
            link.click();
        }

        // Función para ver detalles de plaga
        function verDetallesPlaga(id) {
            const plaga = plagasOriginales.find(p => p.id_plaga === id);
            if (plaga) {
                alert(`Detalles de ${plaga.nombre}:\n\nTipo: ${plaga.tipo}\nDescripción: ${plaga.descripcion}\nFase: ${plaga.fase_cultivo}\nTemporada: ${plaga.estacion}`);
            }
        }

        // Función para editar plaga
        function editarPlaga(id) {
            alert('Función de edición en desarrollo');
        }

        // Función para eliminar plaga
        function eliminarPlaga(id) {
            if (confirm('¿Estás seguro de que quieres eliminar esta plaga?')) {
                alert('Función de eliminación en desarrollo');
            }
        }

        async function cargarEstadisticasPlagas() {
            try {
                const response = await fetch('/admin/api/estadisticas');
                if (response.ok) {
                    const stats = await response.json();
                    document.getElementById('total-plagas').textContent = stats.total_plagas || 0;
                }
                
                // Cargar estadísticas por tipo
                const responsePlagas = await fetch('/admin/api/plagas');
                if (responsePlagas.ok) {
                    const data = await responsePlagas.json();
                    const plagas = data.plagas || data;
                    
                    const insectos = plagas.filter(p => p.tipo === 'INSECTOS').length;
                    const hongos = plagas.filter(p => p.tipo === 'HONGOS').length;
                    const otros = plagas.filter(p => !['INSECTOS', 'HONGOS'].includes(p.tipo)).length;
                    
                    document.getElementById('total-insectos').textContent = insectos;
                    document.getElementById('total-hongos').textContent = hongos;
                    document.getElementById('total-otros').textContent = otros;
                }
            } catch (error) {
                console.error('Error al cargar estadísticas:', error);
            }
        }

        function mostrarPlagas(plagas) {
            const tbody = document.getElementById('plagas-tbody');
            if (!plagas || plagas.length === 0) {
                tbody.innerHTML = '<tr><td colspan="7" class="text-center">No hay plagas registradas</td></tr>';
                return;
            }

            let html = '';
            plagas.forEach(plaga => {
                const tipoBadge = getTipoBadge(plaga.tipo);
                const descripcionCorta = plaga.descripcion ? 
                    (plaga.descripcion.length > 50 ? plaga.descripcion.substring(0, 50) + '...' : plaga.descripcion) : 
                    'Sin descripción';

                html += `
                    <tr>
                        <td>${plaga.id_plaga}</td>
                        <td>${plaga.nombre}</td>
                        <td><span class="badge bg-${tipoBadge}">${plaga.tipo}</span></td>
                        <td>${plaga.fase_cultivo || 'N/A'}</td>
                        <td>${plaga.estacion || 'N/A'}</td>
                        <td title="${plaga.descripcion || ''}">${descripcionCorta}</td>
                        <td>
                            <button class="btn btn-sm btn-outline-info" onclick="verDetallePlaga(${plaga.id_plaga})">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger" onclick="eliminarPlaga(${plaga.id_plaga})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                `;
            });
            tbody.innerHTML = html;
        }

        function getTipoBadge(tipo) {
            switch(tipo) {
                case 'INSECTOS': return 'warning';
                case 'HONGOS': return 'danger';
                case 'ÁCAROS': return 'info';
                case 'BACTERIAS': return 'secondary';
                case 'VIRUS': return 'dark';
                default: return 'primary';
            }
        }

        async function guardarPlaga() {
            const form = document.getElementById('formAgregarPlaga');
            const formData = new FormData(form);
            
            try {
                const response = await fetch('/admin/api/plagas', {
                    method: 'POST',
                    body: formData
                });
                
                if (response.ok) {
                    alert('Plaga agregada exitosamente');
                    form.reset();
                    bootstrap.Modal.getInstance(document.getElementById('modalAgregarPlaga')).hide();
                    cargarPlagas();
                    cargarEstadisticasPlagas();
                } else {
                    const error = await response.json();
                    alert('Error: ' + (error.detail || 'Error al guardar la plaga'));
                }
            } catch (error) {
                alert('Error de conexión');
                console.error('Error:', error);
            }
        }

        async function eliminarPlaga(idPlaga) {
            if (confirm('¿Estás seguro de que quieres eliminar esta plaga?')) {
                try {
                    const response = await fetch(`/admin/api/plagas/${idPlaga}`, {
                        method: 'DELETE'
                    });
                    
                    if (response.ok) {
                        alert('Plaga eliminada exitosamente');
                        cargarPlagas();
                        cargarEstadisticasPlagas();
                    } else {
                        const error = await response.json();
                        alert('Error: ' + (error.detail || 'Error al eliminar la plaga'));
                    }
                } catch (error) {
                    alert('Error de conexión');
                }
            }
        }

        function verDetallePlaga(idPlaga) {
            // Implementar modal de detalle si es necesario
            alert('Funcionalidad de detalle en desarrollo');
        }
    </script>
</body>
</html>
