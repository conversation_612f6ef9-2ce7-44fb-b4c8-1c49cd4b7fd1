from app.database.config import engine
from sqlalchemy import text
import random
from datetime import datetime, timedelta

def ingresar_consultas_ejemplo():
    """
    Ingresa consultas de ejemplo en la base de datos para los usuarios registrados.
    """
    try:
        with engine.connect() as conn:
            # Verificar si hay usuarios registrados
            result = conn.execute(text("SELECT id_usuarios FROM usuarios"))
            usuarios = [row[0] for row in result]
            
            if not usuarios:
                print("No hay usuarios registrados. Primero debe registrar usuarios.")
                return
            
            # Verificar si hay plagas registradas
            result = conn.execute(text("SELECT id_plaga FROM plagas"))
            plagas = [row[0] for row in result]
            
            if not plagas:
                print("No hay plagas registradas en la base de datos.")
                return
            
            # Generar consultas aleatorias para cada usuario
            consultas_creadas = 0
            for id_usuario in usuarios:
                # Generar entre 1 y 5 consultas por usuario
                num_consultas = random.randint(1, 5)
                
                for _ in range(num_consultas):
                    # Seleccionar una plaga aleatoria
                    id_plaga = random.choice(plagas)
                    
                    # Generar una fecha aleatoria en los últimos 30 días
                    dias_atras = random.randint(0, 30)
                    horas_atras = random.randint(0, 23)
                    minutos_atras = random.randint(0, 59)
                    fecha_consulta = datetime.now() - timedelta(days=dias_atras, hours=horas_atras, minutes=minutos_atras)
                    fecha_str = fecha_consulta.strftime('%Y-%m-%d %H:%M:%S')
                    
                    # Insertar la consulta
                    conn.execute(
                        text("INSERT INTO consultas (id_plaga, id_usuarios, fecha_consulta) VALUES (:id_plaga, :id_usuario, :fecha)"),
                        {"id_plaga": id_plaga, "id_usuario": id_usuario, "fecha": fecha_str}
                    )
                    consultas_creadas += 1
            
            conn.commit()
            print(f"Se han creado {consultas_creadas} consultas de ejemplo para {len(usuarios)} usuarios.")
            
            # Mostrar algunas consultas creadas
            result = conn.execute(text("""
                SELECT c.id_consultas, c.fecha_consulta, u.nombre, u.apellido, p.nombre as nombre_plaga
                FROM consultas c
                JOIN usuarios u ON c.id_usuarios = u.id_usuarios
                JOIN plagas p ON c.id_plaga = p.id_plaga
                ORDER BY c.fecha_consulta DESC
                LIMIT 10
            """))
            
            consultas = result.fetchall()
            if consultas:
                print("\nÚltimas consultas registradas:")
                for consulta in consultas:
                    print(f"ID: {consulta[0]}, Fecha: {consulta[1]}, Usuario: {consulta[2]} {consulta[3]}, Plaga: {consulta[4]}")
            
    except Exception as e:
        print(f"Error al ingresar consultas de ejemplo: {e}")

if __name__ == "__main__":
    ingresar_consultas_ejemplo()
