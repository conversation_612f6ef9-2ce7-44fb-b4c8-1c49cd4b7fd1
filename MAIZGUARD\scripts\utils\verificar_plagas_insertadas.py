#!/usr/bin/env python3
"""
Script para verificar las plagas insertadas en la base de datos MaizGuard
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from app.database.config import engine
from sqlalchemy import text

def verificar_plagas():
    """Verificar las plagas insertadas en la base de datos"""
    try:
        with engine.connect() as conn:
            # Contar plagas por tipo
            result = conn.execute(text("""
                SELECT tp.nombre as tipo, COUNT(p.id_plaga) as cantidad
                FROM tipos_plaga tp
                LEFT JOIN plagas p ON tp.id_tipo_plaga = p.id_tipo_plaga
                WHERE p.id_fase_cultivo = 4 AND p.id_temporada = 1
                GROUP BY tp.id_tipo_plaga, tp.nombre
                ORDER BY tp.nombre
            """))

            print("🌽 Plagas insertadas por tipo (Fase: Maduración, Temporada: Seca):")
            print("=" * 60)

            total_plagas = 0
            for row in result:
                print(f"📊 {row.tipo}: {row.cantidad} plagas")
                total_plagas += row.cantidad

            print("=" * 60)
            print(f"🎯 TOTAL DE PLAGAS: {total_plagas}")
            print()

            # Listar todas las plagas insertadas
            result = conn.execute(text("""
                SELECT p.nombre, p.nombre_cientifico, tp.nombre as tipo
                FROM plagas p
                JOIN tipos_plaga tp ON p.id_tipo_plaga = tp.id_tipo_plaga
                WHERE p.id_fase_cultivo = 4 AND p.id_temporada = 1
                ORDER BY tp.nombre, p.nombre
            """))

            print("📋 Lista detallada de plagas insertadas:")
            print("=" * 80)

            current_type = ""
            for row in result:
                if row.tipo != current_type:
                    current_type = row.tipo
                    print(f"\n🔸 {current_type.upper()}:")
                    print("-" * 40)

                print(f"  ✅ {row.nombre}")
                print(f"     📝 {row.nombre_cientifico}")

            print("\n" + "=" * 80)

            # Verificar que se pueden buscar
            print("\n🔍 Verificando funcionalidad de búsqueda:")
            print("-" * 50)

            # Buscar algunas plagas específicas
            busquedas_test = [
                "gusano", "ácaro", "virus", "bacteria", "hongo",
                "escarabajo", "trips", "chinche", "picudo"
            ]

            for termino in busquedas_test:
                result = conn.execute(text("""
                    SELECT COUNT(*) as count FROM plagas
                    WHERE (LOWER(nombre) LIKE LOWER(:termino)
                           OR LOWER(nombre_cientifico) LIKE LOWER(:termino)
                           OR LOWER(descripcion) LIKE LOWER(:termino))
                    AND id_fase_cultivo = 4 AND id_temporada = 1
                """), {"termino": f"%{termino}%"})

                count = result.fetchone().count
                if count > 0:
                    print(f"  🔍 '{termino}': {count} resultados")
                else:
                    print(f"  ❌ '{termino}': Sin resultados")

            print("\n🎉 Verificación completada!")

    except Exception as e:
        print(f"❌ Error al verificar plagas: {e}")

if __name__ == "__main__":
    print("🌽 Verificando plagas insertadas en MaizGuard...")
    verificar_plagas()
