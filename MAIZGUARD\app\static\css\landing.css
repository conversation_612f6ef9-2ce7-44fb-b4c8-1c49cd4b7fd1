/* ===== LANDING PAGE STYLES - MAIZGUARD ===== */

/* === HERO SECTION === */
.hero-section-simple {
    background: linear-gradient(135deg, rgba(57, 169, 0, 0.1) 0%, rgba(255, 255, 255, 0.95) 50%, rgba(255, 102, 0, 0.1) 100%);
    position: relative;
    overflow: hidden;
}

.welcome-section {
    background: linear-gradient(135deg, rgba(57, 169, 0, 0.05) 0%, rgba(255, 255, 255, 0.98) 100%);
    border-bottom: 1px solid rgba(57, 169, 0, 0.2);
}

.min-vh-75 {
    min-height: 75vh;
}

.hero-content {
    color: #333;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
    color: var(--primary-color);
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
    line-height: 1.2;
}

.hero-subtitle {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    color: var(--accent-color);
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

.hero-description {
    font-size: 1.2rem;
    line-height: 1.6;
    margin-bottom: 2.5rem;
    color: #333;
    text-shadow: none;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.btn-hero-primary {
    background: linear-gradient(135deg, var(--accent-color), var(--accent-dark));
    border: 3px solid var(--accent-color);
    color: white !important;
    padding: 1.2rem 2.5rem;
    font-size: 1.3rem;
    font-weight: 800;
    border-radius: 50px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255, 87, 34, 0.4);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.btn-hero-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(255, 87, 34, 0.6);
    color: white !important;
    background: linear-gradient(135deg, var(--accent-dark), var(--accent-color));
}

.btn-hero-secondary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border: 3px solid var(--primary-color);
    color: white;
    padding: 1.2rem 2.5rem;
    font-size: 1.2rem;
    font-weight: 700;
    border-radius: 50px;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    box-shadow: 0 4px 15px rgba(46, 204, 64, 0.4);
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
}

.btn-hero-secondary:hover {
    background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(46, 204, 64, 0.6);
}

.hero-image {
    position: relative;
}

.hero-image img {
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    transition: transform 0.3s ease;
}

.hero-image:hover img {
    transform: scale(1.05);
}

.hero-badge {
    position: absolute;
    top: -10px;
    right: -10px;
    background: linear-gradient(135deg, var(--accent-color), var(--accent-light));
    color: white;
    padding: 0.8rem 1.2rem;
    border-radius: 50px;
    font-weight: 600;
    box-shadow: 0 4px 15px rgba(255, 102, 0, 0.4);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* === FEATURES SECTION === */
.features-section {
    background: linear-gradient(180deg, #f8f9fa 0%, #ffffff 100%);
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.section-subtitle {
    font-size: 1.2rem;
    color: #6c757d;
    margin-bottom: 0;
}

.feature-card {
    background: white;
    padding: 2.5rem 2rem;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    height: 100%;
    border: 1px solid rgba(57, 169, 0, 0.1);
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(57, 169, 0, 0.2);
    border-color: var(--accent-color);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: white;
    font-size: 2rem;
    box-shadow: 0 8px 20px rgba(57, 169, 0, 0.3);
}

.feature-card h4 {
    font-size: 1.4rem;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.feature-card p {
    color: #6c757d;
    line-height: 1.6;
    margin-bottom: 0;
}

/* === RESPONSIVE DESIGN === */
@media (max-width: 991.98px) {
    .hero-title {
        font-size: 2.8rem;
    }
    
    .hero-subtitle {
        font-size: 1.3rem;
    }
    
    .hero-description {
        font-size: 1.1rem;
    }
    
    .hero-buttons {
        justify-content: center;
    }
}

@media (max-width: 767.98px) {
    .hero-section {
        min-height: 90vh;
        text-align: center;
    }
    
    .hero-title {
        font-size: 2.2rem;
    }
    
    .hero-subtitle {
        font-size: 1.2rem;
    }
    
    .hero-description {
        font-size: 1rem;
    }
    
    .btn-hero-primary,
    .btn-hero-secondary {
        padding: 0.8rem 1.5rem;
        font-size: 1rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .feature-card {
        padding: 2rem 1.5rem;
    }
    
    .feature-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }
}

@media (max-width: 575.98px) {
    .hero-title {
        font-size: 1.8rem;
    }
    
    .hero-subtitle {
        font-size: 1.1rem;
    }
    
    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .btn-hero-primary,
    .btn-hero-secondary {
        width: 100%;
        max-width: 280px;
        justify-content: center;
    }
    
    .hero-badge {
        position: static;
        margin-top: 1rem;
        align-self: center;
    }
}
