from fastapi import APIRouter, HTTPException
from typing import List
from pydantic import BaseModel
import aiomysql
from app.routers.fase_cultivo import conectar_db  # Reutilizar la función de conexión a la base de datos

router = APIRouter()

# Función para conectar a la base de datos
async def conectar_db():
    return await aiomysql.connect(
        host="localhost",
        port=3306,
        user="root",
        password="",
        db="MAIZGUARD"
    )
    

# Modelo para la respuesta de estados
class Estado(BaseModel):
    id_estados: int
    nombre_estado: str

# Modelo para los cultivos con estado
class CultivoEstado(BaseModel):
    id_registro_cultivo: int
    descripcion: str
    estado: str


# Modelo para los usuarios activos
class UsuariosActivo(BaseModel):
    id_usuarios: int
    nombre: str
    correo_electronico: str


# Modelo para los usuarios inactivos
class UsuariosInactivo(BaseModel):
    id_usuarios: int
    nombre: str
    correo_electronico: str

# Endpoint para listar todos los estados
@router.get("/ListarEstados", response_model=List[Estado])
async def listar_estados():
    conexion = None
    try:
        conexion = await conectar_db()
        async with conexion.cursor(aiomysql.DictCursor) as cursor:
            # Consultar todos los estados
            query = "SELECT id_estados, nombre_estado FROM estados"
            await cursor.execute(query)
            estados = await cursor.fetchall()

        return estados

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error al listar los estados: {str(e)}")
    finally:
        if conexion:
            conexion.close()

# Endpoint para obtener el estado de los cultivos
@router.get("/EstadoCultivos", response_model=List[CultivoEstado])
async def estado_cultivos():
    conexion = None
    try:
        conexion = await conectar_db()
        async with conexion.cursor(aiomysql.DictCursor) as cursor:
            # Consultar el estado de los cultivos
            query = """
                SELECT rc.id_registro_cultivo, rc.descripcion, e.nombre_estado AS estado
                FROM registro_cultivo rc
                INNER JOIN estados e ON rc.id_estados = e.id_estados
            """
            await cursor.execute(query)
            cultivos = await cursor.fetchall()

        return cultivos

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error al obtener el estado de los cultivos: {str(e)}")
    finally:
        if conexion:
            conexion.close()


# Endpoint para listar los usuarios inactivos (solo para superadmin)
@router.get("/UsuariosActivo", response_model=List[UsuariosActivo])
async def usuarios():
    conexion = None
    try:
        conexion = await conectar_db()
        async with conexion.cursor(aiomysql.DictCursor) as cursor:
            # Consultar los usuarios activos
            query = """
                SELECT u.id_usuarios, u.nombre, u.correo_electronico
                FROM usuarios u
                INNER JOIN estados e ON u.id_estados = e.id_estados
                WHERE e.nombre_estado = 'Activo'
            """

            await cursor.execute(query)
            usuarios = await cursor.fetchall()

        return usuarios

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error al obtener los usuarios inactivos: {str(e)}")
    finally:
        if conexion:
            conexion.close()


# Endpoint para listar los usuarios inactivos (solo para superadmin)
@router.get("/UsuariosInactivo", response_model=List[UsuariosInactivo])
async def usuarios():
    conexion = None
    try:
        conexion = await conectar_db()
        async with conexion.cursor(aiomysql.DictCursor) as cursor:
            # Consultar los usuarios activos
            query = """
                SELECT u.id_usuarios, u.nombre, u.correo_electronico
                FROM usuarios u
                INNER JOIN estados e ON u.id_estados = e.id_estados
                WHERE e.nombre_estado = 'Inactivo'
            """

            await cursor.execute(query)
            usuarios = await cursor.fetchall()

        return usuarios

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error al obtener los usuarios inactivos: {str(e)}")
    finally:
        if conexion:
            conexion.close()