from fastapi import APIRouter, HTTPException, Depends
from typing import List, Optional
from pydantic import BaseModel
import aiomysql
from app.routers.fase_cultivo import conectar_db  # Reutilizar la función de conexión a la base de datos

router = APIRouter()

# Función para conectar a la base de datos
async def conectar_db():
    return await aiomysql.connect(
        host="localhost",
        port=3306,
        user="root",
        password="",
        db="MAIZGUARD"
    )

# Modelo para crear/actualizar una plaga
class PlagaCreate(BaseModel):
    id_usuarios: int
    nombre: str
    nombre_cientifico: str
    url_img: str
    descripcion: str
    recomendaciones: str
    id_fase_cultivo: int
    id_tipo_plaga: int
    id_temporada: int

# Modelo para la respuesta de una plaga
class Plaga(BaseModel):
    id_plaga: int
    id_usuarios: int
    nombre: str
    nombre_cientifico: str
    url_img: str
    descripcion: str
    recomendaciones: str
    id_fase_cultivo: int
    id_tipo_plaga: int
    id_temporada: int

# Modelo para registrar una consulta
class ConsultaCreate(BaseModel):
    id_Plaga: int
    id_usuarios: int

# Modelo para la respuesta de una consulta
class Consulta(BaseModel):
    id_consultas: int
    id_Plaga: int
    id_usuarios: int

# Modelo para la respuesta de una plaga
class Plaga(BaseModel):
    id_Plaga: int
    nombre: str
    nombre_cientifico: str
    url_img: str
    descripcion: str
    recomendaciones: str

# Endpoint para registrar una nueva plaga (solo superadmin)
@router.post("/RegistrarPlaga", response_model=Plaga)
async def registrar_plaga(plaga: PlagaCreate):
    conexion = None
    try:
        conexion = await conectar_db()
        async with conexion.cursor() as cursor:
            # Insertar la nueva plaga
            query = """
                INSERT INTO plagas (id_usuarios, nombre, nombre_cientifico, url_img, descripcion, recomendaciones, id_fase_cultivo, id_tipo_plaga, id_temporada)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
            valores = (
                plaga.id_usuarios,
                plaga.nombre,
                plaga.nombre_cientifico,
                plaga.url_img,
                plaga.descripcion,
                plaga.recomendaciones,
                plaga.id_fase_cultivo,
                plaga.id_tipo_plaga,
                plaga.id_temporada,
            )
            await cursor.execute(query, valores)
            await conexion.commit()

            # Obtener el ID generado
            id_plaga = cursor.lastrowid

        return Plaga(id_plaga=id_plaga, **plaga.dict())

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error al registrar la plaga: {str(e)}")
    finally:
        if conexion:
            conexion.close()

# Endpoint para listar todas las plagas (accesible para usuarios)
@router.get("/ListarPlagas", response_model=List[Plaga])
async def listar_plagas():
    conexion = None
    try:
        conexion = await conectar_db()
        async with conexion.cursor(aiomysql.DictCursor) as cursor:
            # Consultar todas las plagas
            query = "SELECT * FROM plaga"
            await cursor.execute(query)
            plagas = await cursor.fetchall()

        return plagas

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error al listar las plagas: {str(e)}")
    finally:
        if conexion:
            conexion.close()

# Endpoint para buscar una plaga por ID (accesible para usuarios)
@router.get("/Plaga/{id_plaga}", response_model=Plaga)
async def obtener_plaga(id_plaga: int):
    conexion = None
    try:
        conexion = await conectar_db()
        async with conexion.cursor(aiomysql.DictCursor) as cursor:
            # Consultar la plaga por ID
            query = "SELECT * FROM plaga WHERE id_plaga = %s"
            await cursor.execute(query, (id_plaga,))
            plaga = await cursor.fetchone()

            if not plaga:
                raise HTTPException(status_code=404, detail="Plaga no encontrada")

        return plaga

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error al obtener la plaga: {str(e)}")
    finally:
        if conexion:
            conexion.close()

# Endpoint para actualizar una plaga (solo superadmin)
@router.put("/ActualizarPlaga/{id_plaga}")
async def actualizar_plaga(id_plaga: int, plaga: PlagaCreate):
    conexion = None
    try:
        conexion = await conectar_db()
        async with conexion.cursor() as cursor:
            # Verificar si la plaga existe
            query_verificar = "SELECT id_plaga FROM plaga WHERE id_plaga = %s"
            await cursor.execute(query_verificar, (id_plaga,))
            plaga_existente = await cursor.fetchone()

            if not plaga_existente:
                raise HTTPException(status_code=404, detail="Plaga no encontrada")

            # Actualizar la plaga
            query = """
                UPDATE plaga
                SET id_usuarios = %s, nombre = %s, nombre_cientifico = %s, url_img = %s, descripcion = %s, recomendaciones = %s, id_fase_cultivo = %s, id_tipo_plaga = %s, id_temporada = %s
                WHERE id_plaga = %s
            """
            valores = (
                plaga.id_usuarios,
                plaga.nombre,
                plaga.nombre_cientifico,
                plaga.url_img,
                plaga.descripcion,
                plaga.recomendaciones,
                plaga.id_fase_cultivo,
                plaga.id_tipo_plaga,
                plaga.id_temporada,
                id_plaga,
            )
            await cursor.execute(query, valores)
            await conexion.commit()

        return {"mensaje": "Plaga actualizada exitosamente"}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error al actualizar la plaga: {str(e)}")
    finally:
        if conexion:
            conexion.close()

# Endpoint para eliminar una plaga (solo superadmin)
@router.delete("/EliminarPlaga/{id_plaga}")
async def eliminar_plaga(id_plaga: int):
    conexion = None
    try:
        conexion = await conectar_db()
        async with conexion.cursor() as cursor:
            # Verificar si la plaga existe
            query_verificar = "SELECT id_plaga FROM plaga WHERE id_plaga = %s"
            await cursor.execute(query_verificar, (id_plaga,))
            plaga_existente = await cursor.fetchone()

            if not plaga_existente:
                raise HTTPException(status_code=404, detail="Plaga no encontrada")

            # Eliminar la plaga
            query = "DELETE FROM plaga WHERE id_plaga = %s"
            await cursor.execute(query, (id_plaga,))
            await conexion.commit()

        return {"mensaje": "Plaga eliminada exitosamente"}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error al eliminar la plaga: {str(e)}")
    finally:
        if conexion:
            conexion.close()

# Endpoint para buscar una plaga por ID y registrar automáticamente una consulta
@router.get("/BuscarPlaga/{id_Plaga}", response_model=Plaga)
async def buscar_plaga(id_Plaga: int, id_usuarios: int):
    conexion = None
    try:
        conexion = await conectar_db()
        async with conexion.cursor(aiomysql.DictCursor) as cursor:
            # Consultar la plaga por ID
            query_plaga = "SELECT id_Plaga, nombre, nombre_cientifico, url_img, descripcion, recomendaciones FROM plaga WHERE id_Plaga = %s"
            await cursor.execute(query_plaga, (id_Plaga,))
            plaga = await cursor.fetchone()

            if not plaga:
                raise HTTPException(status_code=404, detail="Plaga no encontrada")

            # Verificar si el usuario existe
            query_usuario = "SELECT id_usuarios FROM usuarios WHERE id_usuarios = %s"
            await cursor.execute(query_usuario, (id_usuarios,))
            usuario_existente = await cursor.fetchone()

            if not usuario_existente:
                raise HTTPException(status_code=404, detail="Usuario no encontrado")

            # Registrar la consulta automáticamente
            query_consulta = "INSERT INTO Consultas (id_Plaga, id_usuarios) VALUES (%s, %s)"
            valores_consulta = (id_Plaga, id_usuarios)
            await cursor.execute(query_consulta, valores_consulta)
            await conexion.commit()

        return plaga

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error al buscar la plaga: {str(e)}")
    finally:
        if conexion:
            conexion.close()