from fastapi import APIRouter, HTTPException, UploadFile, File, BackgroundTasks, Depends
from pydantic import BaseModel, EmailStr
from typing import Optional
from datetime import datetime, timedelta
import aiomysql
import bcrypt
from fastapi_mail import FastMail, MessageSchema, ConnectionConfig
import redis
from fastapi.security import OAuth2PasswordBearer
from sqlalchemy.future import select
from app.models.models import usuarios
from secrets import token_hex

router = APIRouter()

# Configuración de la base de datos
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',  # Cambia esto por tu usuario
    'password': '',  # Cambia esto por tu contraseña
    'database': 'MAIZGUARD'
}

# Configuración para enviar correos
conf = ConnectionConfig(
    MAIL_USERNAME="<EMAIL>",  # Dirección de correo
    MAIL_PASSWORD="nwntvvglsqzgohoj",        # Contraseña del correo
    MAIL_FROM="<EMAIL>",     # Dirección de correo que aparece como remitente
    MAIL_PORT=587,                          # Puerto SMTP para TLS
    MAIL_SERVER="smtp.gmail.com",           # Servidor SMTP de Gmail
    MAIL_STARTTLS=True,                     # Habilitar STARTTLS
    MAIL_SSL_TLS=False,                     # Deshabilitar SSL/TLS
    USE_CREDENTIALS=True,                   # Usar credenciales para autenticación
    VALIDATE_CERTS=True                     # Validar certificados SSL
)

# Función para obtener una conexión de la base de datos
async def conectar_db():
    try:
        conexion = await aiomysql.connect(
            host="localhost",
            user="root",
            password="",
            db="MAIZGUARD",
            autocommit=True  # Asegúrate de habilitar autocommit si es necesario
        )
        return conexion
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error al conectar con la base de datos: {str(e)}")

# Modelo para crear usuario
class UsuarioCreate(BaseModel):
    nombre: str
    apellido: str
    contrasena: str
    correo_electronico: EmailStr
    fotografia: Optional[str] = None  # URL de la imagen, puede ser nula
    id_estados: int = 1  # Activo por defecto
    id_rol: int = 2  # Usuario normal por defecto

# Modelo para la respuesta de usuario
class Usuario(BaseModel):
    id_usuarios: int
    nombre: str
    apellido: str
    correo_electronico: str
    fecha_registro: datetime

# Función para verificar si es el único superadmin
async def es_unico_superadmin(id_usuario: int) -> bool:
    conexion = await conectar_db()
    async with conexion.cursor() as cursor:
        await cursor.execute("SELECT COUNT(*) FROM usuarios WHERE id_rol = 1")
        superadmin_count = await cursor.fetchone()
    return superadmin_count[0] == 1 and id_usuario is not None

# Endpoint para crear usuario con verificación
@router.post("/CrearUsuarios")
async def crear_usuario(usuario: UsuarioCreate, background_tasks: BackgroundTasks):
    try:
        # Conexión a la base de datos
        conexion = await conectar_db()
        async with conexion.cursor() as cursor:
            # Validar si el correo ya existe en la base de datos
            query_validar_correo = "SELECT COUNT(*) FROM usuarios WHERE correo_electronico = %s"
            await cursor.execute(query_validar_correo, (usuario.correo_electronico,))
            correo_existente = await cursor.fetchone()

            if correo_existente[0] > 0:
                raise HTTPException(
                    status_code=400,
                    detail="El correo electrónico ya está registrado. Por favor, usa otro."
                )

            # Si id_rol es None o inválido, asignar 2 (usuario normal)
            id_rol = usuario.id_rol if usuario.id_rol in [1, 2] else 2

            # Hashear la contraseña
            hashed_password = bcrypt.hashpw(usuario.contrasena.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')

            # Insertar el nuevo usuario
            query = """INSERT INTO usuarios 
                      (nombre, apellido, contrasena, correo_electronico, fotografia, id_estados, id_rol) 
                      VALUES (%s, %s, %s, %s, %s, %s, %s)"""
            valores = (
                usuario.nombre,
                usuario.apellido,
                hashed_password,
                usuario.correo_electronico,
                usuario.fotografia,
                usuario.id_estados,
                id_rol
            )

            await cursor.execute(query, valores)
            await conexion.commit()

        return {"mensaje": f"Usuario {usuario.nombre} {usuario.apellido} creado exitosamente."}

    except HTTPException as e:
        raise e
    except Exception as e:
        # Manejo genérico de errores
        raise HTTPException(status_code=500, detail=f"Error inesperado: {str(e)}")
    finally:
        if conexion:
            conexion.close()

# Crear superusuario
@router.post("/CrearSuperUsuario")
async def crear_super_usuario(usuario: UsuarioCreate, background_tasks: BackgroundTasks):
    try:
        usuario.id_rol = 1  # Forzar superadmin
        return await crear_usuario(usuario, background_tasks)  # Pasar `background_tasks`
    except HTTPException as e:
        raise e

# Listar usuarios
@router.get("/ListarUsuarios")
async def listar_usuarios():
    conexion = None
    try:
        conexion = await conectar_db()
        async with conexion.cursor(aiomysql.DictCursor) as cursor:
            await cursor.execute("SELECT id_usuarios, nombre, apellido, correo_electronico, fecha_registro FROM usuarios")
            usuarios = await cursor.fetchall()
            return usuarios
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error de base de datos: {str(e)}")
    finally:
        if conexion:
            conexion.close()

@router.get("/usuarios")
async def listar_usuarios(skip: int = 0, limit: int = 10):
    conexion = None
    try:
        conexion = await conectar_db()
        async with conexion.cursor(aiomysql.DictCursor) as cursor:
            query = "SELECT * FROM usuarios LIMIT %s OFFSET %s"
            await cursor.execute(query, (limit, skip))
            usuarios = await cursor.fetchall()
            return usuarios
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error de base de datos: {str(e)}")
    finally:
        if conexion:
            conexion.close()

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")

@router.get("/usuarios/{id_usuario}")
async def obtener_usuario(id_usuario: int, token: str = Depends(oauth2_scheme)):
    # Verificar el token antes de procesar la solicitud
    pass

async def obtener_usuario_por_id(id_usuario: int):
    async with async_session() as session:
        result = await session.execute(select(usuarios).where(usuarios.c.id_usuarios == id_usuario))
        return result.scalars().first()

# Actualizar usuario
@router.put("/usuarios/{id_usuario}")
async def actualizar_usuario(id_usuario: int, usuario: UsuarioCreate):
    try:
        # Conexión a la base de datos
        conexion = await conectar_db()
        async with conexion.cursor() as cursor:
            # Verificar si el usuario existe
            await cursor.execute("SELECT id_usuarios FROM usuarios WHERE id_usuarios = %s", (id_usuario,))
            usuario_existente = await cursor.fetchone()

            if not usuario_existente:
                raise HTTPException(status_code=404, detail="Usuario no encontrado")

            # Hashear la nueva contraseña
            hashed_password = bcrypt.hashpw(usuario.contrasena.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')

            # Actualizar el usuario
            query = """UPDATE usuarios 
                      SET nombre = %s, apellido = %s, 
                      contrasena = %s, correo_electronico = %s, 
                      fotografia = %s 
                      WHERE id_usuarios = %s"""
            valores = (
                usuario.nombre,
                usuario.apellido,
                hashed_password,
                usuario.correo_electronico,
                usuario.fotografia,
                id_usuario
            )

            await cursor.execute(query, valores)
            await conexion.commit()

            return {"mensaje": "Usuario actualizado exitosamente"}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error inesperado: {str(e)}")
    finally:
        if conexion:
            conexion.close()

# Endpoint para colocar automáticamente a los usuarios como "Inactivos" después de 6 meses de inactividad
@router.post("/ActualizarUsuariosInactivos")
async def actualizar_usuarios_inactivos():
    conexion = None
    try:
        conexion = await conectar_db()
        async with conexion.cursor() as cursor:
            # Calcular la fecha límite (hace 6 meses)
            fecha_limite = datetime.now() - timedelta(days=180)

            # Actualizar usuarios que no han iniciado sesión en más de 6 meses
            query = """
                UPDATE usuarios
                SET id_estados = 2  -- Inactivo
                WHERE fecha_ultima_actividad < %s AND id_estados = 1
            """
            await cursor.execute(query, (fecha_limite,))
            await conexion.commit()

        return {"mensaje": "Usuarios inactivos actualizados correctamente"}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error al actualizar usuarios inactivos: {str(e)}")
    finally:
        if conexion:
            conexion.close()

# Endpoint para que el superadmin coloque manualmente a un usuario como "Inactivo"
@router.put("/CambiarEstadoUsuario/{id_usuario}")
async def cambiar_estado_usuario(id_usuario: int, estado: str, token: str = Depends(oauth2_scheme)):
    conexion = None
    try:
        # Verificar si el usuario tiene permisos de superadmin
        # (Puedes implementar una función para verificar el rol del usuario a partir del token)
        if not await es_superadmin(token):
            raise HTTPException(status_code=403, detail="No tienes permisos para realizar esta acción")

        conexion = await conectar_db()
        async with conexion.cursor() as cursor:
            # Verificar si el usuario existe
            query_verificar_usuario = "SELECT id_usuarios FROM usuarios WHERE id_usuarios = %s"
            await cursor.execute(query_verificar_usuario, (id_usuario,))
            usuario_existente = await cursor.fetchone()

            if not usuario_existente:
                raise HTTPException(status_code=404, detail="Usuario no encontrado")

            # Cambiar el estado del usuario
            query_cambiar_estado = """
                UPDATE usuarios
                SET id_estados = (SELECT id_estados FROM estados WHERE nombre_estado = %s)
                WHERE id_usuarios = %s
            """
            await cursor.execute(query_cambiar_estado, (estado, id_usuario))
            await conexion.commit()

        return {"mensaje": f"Estado del usuario actualizado a '{estado}'"}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error al cambiar el estado del usuario: {str(e)}")
    finally:
        if conexion:
            conexion.close()

# Función para verificar si el usuario es superadmin
async def es_superadmin(token: str) -> bool:
    # Decodifica el token y verifica si el usuario tiene el rol de superadmin
    # Implementa la lógica de verificación según tu sistema de autenticación
    return True  # Cambia esto por la lógica real

async def verificar_rol(token: str, rol_requerido: str):
    # Decodifica el token y verifica el rol del usuario
    pass

# Modelo para la solicitud de recuperación de contraseña
class RecuperarContrasenaRequest(BaseModel):
    correo_electronico: EmailStr

# Endpoint para recuperar contraseña
@router.post("/RecuperarContrasena")
async def recuperar_contrasena(request: RecuperarContrasenaRequest, background_tasks: BackgroundTasks):
    conexion = None
    try:
        # Verificar si el correo electrónico existe en la base de datos
        conexion = await conectar_db()
        async with conexion.cursor() as cursor:
            query = "SELECT id_usuarios FROM usuarios WHERE correo_electronico = %s"
            await cursor.execute(query, (request.correo_electronico,))
            usuario = await cursor.fetchone()

            if not usuario:
                raise HTTPException(status_code=404, detail="El correo electrónico no está registrado.")

        # Generar un token único para la recuperación contraseña
        token = "token_unico_generado"  # Genera un token real aquí

        # Enviar el correo de recuperación
        mensaje = MessageSchema(
            subject="Recuperación de contraseña",
            recipients=[request.correo_electronico],
            body=f"Por favor, haz clic en el siguiente enlace para recuperar tu contraseña: http://localhost:8000/recuperar/{token}",
            subtype="html"
        )
        fm = FastMail(conf)
        background_tasks.add_task(fm.send_message, mensaje)

        return {"mensaje": "Se ha enviado un correo electrónico con las instrucciones para recuperar la contraseña."}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error al procesar la solicitud: {str(e)}")
    finally:
        if conexion:
            conexion.close()

# Endpoint para enviar un código de recuperación de contraseña
@router.get("/EnviarCodigoRecuperacion")
async def enviar_codigo_recuperacion(correo_electronico: str, background_tasks: BackgroundTasks):
    conexion = None
    try:
        # Verificar si el correo electrónico existe en la base de datos
        conexion = await conectar_db()
        async with conexion.cursor(aiomysql.DictCursor) as cursor:  # Usar DictCursor
            query = "SELECT id_usuarios FROM usuarios WHERE correo_electronico = %s"
            await cursor.execute(query, (correo_electronico,))
            usuario = await cursor.fetchone()

            if not usuario:
                raise HTTPException(status_code=404, detail="El correo electrónico no está registrado.")

        # Generar un código único de recuperación
        codigo_recuperacion = token_hex(3)  # Genera un código de 6 caracteres hexadecimales

        # Guardar el código en la base de datos o en memoria (puedes usar Redis)
        async with conexion.cursor() as cursor:
            query_guardar_codigo = """
                INSERT INTO codigos_recuperacion (id_usuarios, codigo, fecha_expiracion)
                VALUES (%s, %s, %s)
                ON DUPLICATE KEY UPDATE codigo = %s, fecha_expiracion = %s
            """
            fecha_expiracion = datetime.now() + timedelta(minutes=10)  # Código válido por 10 minutos
            valores = (usuario["id_usuarios"], codigo_recuperacion, fecha_expiracion, codigo_recuperacion, fecha_expiracion)
            await cursor.execute(query_guardar_codigo, valores)
            await conexion.commit()

        # Enviar el código por correo
        mensaje = MessageSchema(
            subject="Código de recuperación de contraseña",
            recipients=[correo_electronico],
            body=f"Tu código de recuperación es: {codigo_recuperacion}. Este código es válido por 10 minutos.",
            subtype="html"
        )
        fm = FastMail(conf)
        background_tasks.add_task(fm.send_message, mensaje)

        return {"mensaje": "El código de recuperación ha sido enviado al correo electrónico proporcionado."}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error al enviar el código de recuperación: {str(e)}")
    finally:
        if conexion:
            conexion.close()

# Endpoint para validar el código de recuperación de contraseña
@router.post("/ValidarCodigoRecuperacion")
async def validar_codigo_recuperacion(correo_electronico: str, codigo: str):
    conexion = None
    try:
        conexion = await conectar_db()
        async with conexion.cursor() as cursor:
            query = """
                SELECT * FROM codigos_recuperacion
                WHERE codigo = %s AND fecha_expiracion > NOW()
            """
            await cursor.execute(query, (codigo,))
            codigo_valido = await cursor.fetchone()

            if not codigo_valido:
                raise HTTPException(status_code=400, detail="El código es inválido o ha expirado.")

        return {"mensaje": "El código es válido."}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error al validar el código: {str(e)}")
    finally:
        if conexion:
            conexion.close()

