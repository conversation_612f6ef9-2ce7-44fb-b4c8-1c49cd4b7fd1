from app.database.config import engine
from sqlalchemy import text

def corregir_temporadas():
    """
    Corrige los nombres de las temporadas.
    """
    try:
        with engine.connect() as conn:
            print("Corrigiendo nombres de temporadas...")
            
            # Actualizar los nombres de las temporadas
            conn.execute(text("UPDATE temporadas SET descripcion = 'Temporada de lluvia' WHERE id_temporada = 1"))
            conn.execute(text("UPDATE temporadas SET descripcion = 'Temporada de sequía' WHERE id_temporada = 2"))
            
            # Confirmar cambios
            conn.commit()
            
            # Mostrar temporadas actualizadas
            print("\nTEMPORADAS CORREGIDAS:")
            result = conn.execute(text("SELECT * FROM temporadas"))
            temporadas = result.fetchall()
            for temporada in temporadas:
                print(f"ID: {temporada[0]}, Descripción: {temporada[1]}")
            
            # Mostrar plagas actualizadas
            print("\nPLAGAS ACTUALIZADAS:")
            result = conn.execute(text("""
                SELECT p.id_plaga, p.nombre, fc.descripcion as fase, t.descripcion as temporada, tp.descripcion as tipo
                FROM plagas p
                LEFT JOIN fases_cultivo fc ON p.id_fase_cultivo = fc.id_fase_cultivo
                LEFT JOIN temporadas t ON p.id_temporada = t.id_temporada
                LEFT JOIN tipos_plaga tp ON p.id_tipo_plaga = tp.id_tipo_plaga
                ORDER BY p.id_plaga
            """))
            plagas = result.fetchall()
            for plaga in plagas:
                print(f"ID: {plaga[0]}, Nombre: {plaga[1]}, Fase: {plaga[2]}, Temporada: {plaga[3]}, Tipo: {plaga[4]}")
            
    except Exception as e:
        print(f"Error al corregir temporadas: {e}")

if __name__ == "__main__":
    corregir_temporadas()
