<!-- Navbar -->
<nav class="navbar navbar-expand-lg navbar-dark bg-success fixed-top">
    <div class="container">
        <a class="navbar-brand" href="/">
            <img src="/static/images/Logo m.png" alt="MAIZGUARD Logo" class="rounded-logo">
        </a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
            <!-- Sección izquierda: Búsqueda -->
            <form class="d-flex search-form" action="/buscar" method="get">
                <div class="input-group">
                    <input class="form-control" type="search" name="q" placeholder="Buscar..." aria-label="Buscar" autocomplete="off" required>
                    <button class="btn btn-light" type="submit" title="Buscar" aria-label="Buscar">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </form>

            <!-- Sección derecha: Enlaces principales y de usuario -->
            <ul class="navbar-nav ms-auto d-flex flex-row">
                <!-- Inicio -->
                <li class="nav-item">
                    <a class="nav-link {% if request.url.path == '/' %}active{% endif %}" href="/"><i class="fas fa-home"></i> Inicio</a>
                </li>

                <!-- Panel para administradores, Cultivos para usuarios normales -->
                {% if user_id %}
                <li class="nav-item">
                    {% if user_role == '1' %}
                        <a class="nav-link {% if request.url.path == '/admin' %}active{% endif %}" href="/admin"><i class="fas fa-tachometer-alt"></i> Panel</a>
                    {% else %}
                        <a class="nav-link {% if request.url.path == '/cultivos' %}active{% endif %}" href="/cultivos"><i class="fas fa-seedling"></i> Cultivos</a>
                    {% endif %}
                </li>
                {% endif %}



                <!-- Usuario autenticado -->
                {% if user_id %}
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <!-- Foto de perfil -->
                        {% if request.state.user and request.state.user.fotografia %}
                        <img src="{{ request.state.user.fotografia }}" alt="Foto de perfil" class="rounded-circle me-2" width="32" height="32">
                        {% else %}
                        <img src="/static/images/perfil icono.jpg" alt="Foto de perfil" class="rounded-circle me-2" width="32" height="32">
                        {% endif %}

                        <!-- Nombre del usuario -->
                        <span>
                            {% if request.state.user %}
                                {{ request.state.user.nombre }}{% if request.state.user.apellido %} {{ request.state.user.apellido }}{% endif %}
                            {% else %}
                                Usuario
                            {% endif %}
                        </span>
                    </a>

                    <!-- Menú desplegable -->
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><h6 class="dropdown-header">
                            {% if request.state.user %}
                                <i class="fas fa-user-circle me-2"></i>{{ request.state.user.nombre }}{% if request.state.user.apellido %} {{ request.state.user.apellido }}{% endif %}
                            {% else %}
                                <i class="fas fa-user-circle me-2"></i>Usuario
                            {% endif %}
                        </h6></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="/perfil"><i class="fas fa-user me-2"></i>Mi perfil</a></li>
                        <li><a class="dropdown-item" href="/perfil/editar"><i class="fas fa-user-edit me-2"></i>Editar perfil</a></li>
                        <li><a class="dropdown-item" href="/perfil/cambiar-password"><i class="fas fa-key me-2"></i>Cambiar contraseña</a></li>

                        <!-- Administración (solo para administradores) -->
                        {% if user_role == '1' %}
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="/admin"><i class="fas fa-cog me-2"></i>Administración</a></li>
                        {% endif %}

                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="/logout"><i class="fas fa-sign-out-alt me-2"></i>Cerrar sesión</a></li>
                    </ul>
                </li>

                <!-- Usuario no autenticado -->
                {% else %}
                <li class="nav-item">
                    <a class="nav-link {% if request.url.path == '/login' %}active{% endif %}" href="/login"><i class="fas fa-sign-in-alt"></i> Login</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if request.url.path == '/registro' %}active{% endif %}" href="/registro"><i class="fas fa-user-plus"></i> Registro</a>
                </li>
                {% endif %}
            </ul>
        </div>
    </div>
</nav>
