from app.database.config import engine
from sqlalchemy import text

def check_tables():
    try:
        with engine.connect() as conn:
            # Mostrar todas las tablas
            result = conn.execute(text('SHOW TABLES'))
            tables = [row[0] for row in result]
            print("Tablas en la base de datos:")
            for table in tables:
                print(f"- {table}")
            
            # Verificar si la tabla usuarios existe
            if 'usuarios' in tables:
                # Contar usuarios
                result = conn.execute(text('SELECT COUNT(*) FROM usuarios'))
                count = result.scalar()
                print(f"\nNúmero de usuarios registrados: {count}")
                
                # Mostrar usuarios
                result = conn.execute(text('SELECT id_usuarios, nombre, apellido, correo_electronico FROM usuarios'))
                users = result.fetchall()
                if users:
                    print("\nUsuarios registrados:")
                    for user in users:
                        print(f"ID: {user[0]}, Nombre: {user[1]} {user[2]}, Email: {user[3]}")
                else:
                    print("\nNo hay usuarios registrados en la tabla.")
            else:
                print("\nLa tabla 'usuarios' no existe en la base de datos.")
    except Exception as e:
        print(f"Error al conectar con la base de datos: {e}")

if __name__ == "__main__":
    check_tables()
