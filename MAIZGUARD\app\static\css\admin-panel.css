/* ===== PANEL DE ADMINISTRACIÓN - MAIZGUARD ===== */

/* === CONTENEDOR PRINCIPAL === */
.admin-container {
    margin-top: 0 !important;
    min-height: 100vh;
    padding-top: 0 !important;
}

/* === SIDEBAR === */
.admin-sidebar {
    background: linear-gradient(180deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    position: fixed;
    top: 65px;
    left: 0;
    width: 200px;
    height: calc(100vh - 65px);
    z-index: 100;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
    border-right: 3px solid var(--accent-color);
}

.sidebar-content {
    padding: 1.5rem 1rem;
}

.sidebar-link {
    color: white !important;
    padding: 0.75rem 1rem;
    margin: 0.25rem 0;
    border-radius: 8px;
    transition: all 0.3s ease;
    text-decoration: none;
    display: flex;
    align-items: center;
}

.sidebar-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--accent-color) !important;
    transform: translateX(5px);
}

.sidebar-link.active {
    background-color: var(--accent-color);
    color: white !important;
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(255, 102, 0, 0.3);
}

.sidebar-link.text-danger:hover {
    background-color: rgba(220, 53, 69, 0.2);
    color: #dc3545 !important;
}

/* === CONTENIDO PRINCIPAL === */
.admin-main-content {
    margin-left: 200px;
    padding: 1rem 1.5rem;
    min-height: calc(100vh - 65px);
    background-color: #f8f9fa;
    margin-top: 65px;
}

/* === TARJETAS DE CULTIVOS === */
.cultivo-card, .usuario-card {
    transition: all 0.3s ease;
    border: none;
    border-radius: 15px;
    overflow: hidden;
}

.cultivo-card:hover, .usuario-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.cultivo-card .card-header, .usuario-card .card-header {
    border: none;
    font-weight: 600;
}

.cultivo-card .card-body, .usuario-card .card-body {
    padding: 1.5rem;
}

.cultivo-card .card-footer, .usuario-card .card-footer {
    border: none;
    background-color: rgba(248, 249, 250, 0.8);
}

/* === MEJORA DE VISIBILIDAD DE TEXTO === */
.card-header h6 {
    color: white !important;
    margin: 0;
    font-weight: 700 !important;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.card-body .fw-bold {
    color: white !important;
    font-weight: 700 !important;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.card-body .text-muted {
    color: #f8f9fa !important;
    font-weight: 500 !important;
}

/* === CORRECCIÓN ESPECÍFICA PARA NOMBRES EN TARJETAS === */
.usuario-card .card-header h6,
.cultivo-card .card-header h6,
.card-header h6.fw-bold,
.card-header .fw-bold {
    color: white !important;
    font-weight: 700 !important;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
}

.usuario-card .card-body .text-muted,
.cultivo-card .card-body .text-muted {
    color: #6c757d !important;
    font-weight: 500 !important;
}

.usuario-card .card-body strong,
.cultivo-card .card-body strong {
    color: #212529 !important;
    font-weight: 700 !important;
}

/* === FORZAR ESTILOS PARA JAVASCRIPT GENERADO === */
.card-header .text-white {
    color: white !important;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
}

.bg-gradient-primary .text-white,
.bg-gradient-success .text-white,
.bg-gradient-warning .text-white,
.bg-gradient-info .text-white {
    color: white !important;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
}

.card-body small {
    color: #495057 !important;
    font-weight: 500 !important;
}

.badge {
    font-weight: 600 !important;
    font-size: 0.85rem !important;
    padding: 0.5rem 0.75rem !important;
}

/* === FILTROS Y BÚSQUEDA === */
.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%) !important;
}

.bg-gradient-success {
    background: linear-gradient(135deg, var(--primary-color) 0%, #20c997 100%) !important;
}

.bg-gradient-warning {
    background: linear-gradient(135deg, #ffc107 0%, var(--accent-color) 100%) !important;
}

.bg-gradient-info {
    background: linear-gradient(135deg, #17a2b8 0%, #007bff 100%) !important;
}

/* === BADGES Y ELEMENTOS === */
.badge {
    font-size: 0.85rem;
    padding: 0.5rem 0.75rem;
}

/* === BOTONES === */
.btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-success:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-warning:hover {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
}

/* === MODALES === */
.modal-header {
    border-bottom: 2px solid var(--accent-color);
}

.modal-footer {
    border-top: 1px solid rgba(0, 0, 0, 0.1);
}

/* === ALERTAS === */
.alert-success {
    background-color: rgba(57, 169, 0, 0.1);
    border-color: var(--primary-color);
    color: var(--primary-color);
}

/* === RESPONSIVE === */
@media (max-width: 991.98px) {
    .admin-sidebar {
        position: relative;
        top: 0;
        height: auto;
        width: 100%;
    }

    .admin-main-content {
        margin-left: 0;
        padding: 1rem;
        margin-top: 0;
    }

    .admin-container {
        margin-top: 65px;
    }
}

@media (max-width: 767.98px) {
    .admin-container {
        margin-top: 65px;
    }

    .sidebar-content {
        padding: 1rem 0.5rem;
    }

    .sidebar-link {
        padding: 0.5rem 0.75rem;
        font-size: 0.9rem;
    }

    .admin-main-content {
        padding: 1rem 0.5rem;
        margin-top: 0;
    }
}

/* === COLORES ESPECÍFICOS PARA TEXTOS === */
.text-white {
    color: white !important;
}

.text-light {
    color: #f8f9fa !important;
}

/* === MEJORAS VISUALES === */
.card-header h6 {
    color: white !important;
    margin: 0;
}

.feature-icon {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)) !important;
}

/* === ESTADÍSTICAS === */
.stats-card {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 1rem;
}

.stats-card h3 {
    color: white !important;
    font-weight: 700;
}

.stats-card p {
    color: rgba(255, 255, 255, 0.9) !important;
    margin: 0;
}

/* === TABLAS === */
.table th {
    background-color: var(--primary-color);
    color: white !important;
    border: none;
    font-weight: 600;
}

.table td {
    vertical-align: middle;
    border-color: rgba(57, 169, 0, 0.1);
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(57, 169, 0, 0.05);
}

/* === SPINNER DE CARGA === */
.spinner-border-primary {
    color: var(--primary-color);
}

/* === ICONOS === */
.fas, .far {
    color: inherit;
}

/* === CORRECCIÓN DE COLORES === */
.card-body .fw-bold {
    color: var(--primary-color) !important;
}

.small.text-muted {
    color: #6c757d !important;
}

.text-success {
    color: var(--primary-color) !important;
}

.text-info {
    color: #17a2b8 !important;
}

.text-warning {
    color: var(--accent-color) !important;
}

.text-danger {
    color: #dc3545 !important;
}

.text-primary {
    color: var(--primary-color) !important;
}

/* === FORZAR LAYOUT DEL PANEL DE ADMINISTRACIÓN === */
body.admin-page .admin-container {
    margin-top: 0 !important;
    padding-top: 0 !important;
}

body.admin-page .admin-sidebar {
    top: 65px !important;
    width: 200px !important;
    height: calc(100vh - 65px) !important;
}

body.admin-page .admin-main-content {
    margin-left: 200px !important;
    margin-top: 65px !important;
    padding: 1rem 1.5rem !important;
}

/* === CORRECCIÓN ESPECÍFICA PARA NAVBAR EN ADMIN === */
body.admin-page .navbar {
    min-height: 65px !important;
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
}

body.admin-page .rounded-logo {
    height: 40px !important;
    width: 40px !important;
    margin-right: 10px !important;
    left: 0 !important;
}

body.admin-page .navbar-brand {
    font-size: 1.3rem !important;
    margin-left: 0 !important;
}
