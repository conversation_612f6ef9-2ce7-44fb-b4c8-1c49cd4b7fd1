from app.database.config import engine
from sqlalchemy import text
import bcrypt

def crear_admin_simple():
    """
    Crea un usuario administrador en la base de datos de forma simple.
    """
    try:
        with engine.connect() as conn:
            # Verificar si existe el rol de administrador (id_rol = 1)
            result = conn.execute(text("SELECT id_rol FROM roles WHERE id_rol = 1"))
            if not result.fetchone():
                print("Creando rol de administrador...")
                conn.execute(text("INSERT INTO roles (id_rol, nombre, descripcion) VALUES (1, 'admin', 'Administrador del sistema')"))
                conn.commit()
            
            # Verificar si existe el estado activo (id_estados = 1)
            result = conn.execute(text("SELECT id_estados FROM estados WHERE id_estados = 1"))
            if not result.fetchone():
                print("Creando estado activo...")
                conn.execute(text("INSERT INTO estados (id_estados, descripcion) VALUES (1, 'Activo')"))
                conn.commit()
            
            # Datos del administrador
            nombre = "Admin"
            apellido = "MAIZGUARD"
            correo = "<EMAIL>"
            password = "admin123"  # Contraseña simple para pruebas
            
            # Hashear la contraseña
            hashed_password = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
            
            # Insertar el administrador directamente
            query = text("""
                INSERT INTO usuarios (
                    id_estados, 
                    id_rol, 
                    nombre, 
                    apellido, 
                    correo_electronico, 
                    contrasena, 
                    fecha_registro
                )
                VALUES (
                    1, 
                    1, 
                    :nombre, 
                    :apellido, 
                    :correo, 
                    :contrasena, 
                    NOW()
                )
                ON DUPLICATE KEY UPDATE
                    nombre = :nombre,
                    apellido = :apellido,
                    contrasena = :contrasena
            """)
            
            conn.execute(query, {
                "nombre": nombre,
                "apellido": apellido,
                "correo": correo,
                "contrasena": hashed_password
            })
            
            conn.commit()
            
            print(f"Administrador creado/actualizado exitosamente.")
            print(f"Correo: {correo}")
            print(f"Contraseña: {password}")
            
    except Exception as e:
        print(f"Error al crear administrador: {e}")

if __name__ == "__main__":
    crear_admin_simple()
