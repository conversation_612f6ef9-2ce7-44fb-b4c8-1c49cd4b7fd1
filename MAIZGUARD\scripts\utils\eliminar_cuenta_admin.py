#!/usr/bin/env python3
"""
Script para eliminar la cuenta admin de la base de datos MaizGuard
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from app.database.config import engine
from sqlalchemy import text

def eliminar_cuenta_admin():
    """Eliminar la cuenta admin de la base de datos"""
    try:
        with engine.connect() as conn:
            # Buscar la cuenta admin
            result = conn.execute(text("""
                SELECT id_usuarios, nombre, correo_electronico
                FROM usuarios
                WHERE nombre = 'Admin' OR correo_electronico = '<EMAIL>'
            """))

            admin_users = result.fetchall()

            if not admin_users:
                print("⚠️  No se encontró ninguna cuenta admin para eliminar.")
                return

            print("🔍 Cuentas admin encontradas:")
            for user in admin_users:
                print(f"  📧 ID: {user.id_usuarios}, Usuario: {user.nombre}, Email: {user.correo_electronico}")

            # Verificar si hay plagas asociadas a estas cuentas
            for user in admin_users:
                result = conn.execute(text("""
                    SELECT COUNT(*) as count FROM plagas WHERE id_usuarios = :user_id
                """), {"user_id": user.id_usuarios})

                plaga_count = result.fetchone().count

                if plaga_count > 0:
                    print(f"⚠️  El usuario {user.nombre} tiene {plaga_count} plagas asociadas.")

                    # Reasignar las plagas a otro usuario
                    result = conn.execute(text("""
                        SELECT id_usuarios FROM usuarios
                        WHERE id_usuarios != :admin_id
                        ORDER BY id_usuarios
                        LIMIT 1
                    """), {"admin_id": user.id_usuarios})

                    new_owner = result.fetchone()

                    if new_owner:
                        conn.execute(text("""
                            UPDATE plagas
                            SET id_usuarios = :new_owner_id
                            WHERE id_usuarios = :admin_id
                        """), {
                            "new_owner_id": new_owner.id_usuarios,
                            "admin_id": user.id_usuarios
                        })
                        print(f"✅ Plagas reasignadas al usuario ID: {new_owner.id_usuarios}")
                    else:
                        print("❌ No hay otros usuarios para reasignar las plagas.")
                        return

            # Eliminar las cuentas admin
            for user in admin_users:
                # Eliminar la cuenta de usuario
                result = conn.execute(text("""
                    DELETE FROM usuarios WHERE id_usuarios = :user_id
                """), {"user_id": user.id_usuarios})

                if result.rowcount > 0:
                    print(f"✅ Usuario '{user.nombre}' eliminado exitosamente.")
                else:
                    print(f"❌ Error al eliminar el usuario '{user.nombre}'.")

            conn.commit()

            # Verificar eliminación
            result = conn.execute(text("""
                SELECT COUNT(*) as count FROM usuarios
                WHERE nombre = 'Admin' OR correo_electronico = '<EMAIL>'
            """))

            remaining_count = result.fetchone().count

            if remaining_count == 0:
                print("\n🎉 Todas las cuentas admin han sido eliminadas exitosamente!")
            else:
                print(f"\n⚠️  Aún quedan {remaining_count} cuentas admin en la base de datos.")

            # Mostrar estadísticas finales
            result = conn.execute(text("SELECT COUNT(*) as count FROM usuarios"))
            total_users = result.fetchone().count

            result = conn.execute(text("SELECT COUNT(*) as count FROM plagas"))
            total_plagas = result.fetchone().count

            print(f"\n📊 Estadísticas finales:")
            print(f"  👥 Total de usuarios: {total_users}")
            print(f"  🐛 Total de plagas: {total_plagas}")

    except Exception as e:
        print(f"❌ Error al eliminar cuenta admin: {e}")

if __name__ == "__main__":
    print("🌽 Eliminando cuenta admin de MaizGuard...")
    eliminar_cuenta_admin()
