{% extends "base.html" %}

{% block title %}Consulta de Plagas - MaizGuard{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row mb-4">
        <div class="col-md-8">
            <h1 class="display-5 fw-bold text-success">Consulta de Plagas</h1>
            <p class="lead">Encuentra información sobre las plagas que afectan al maíz durante las fases de maduración y cosecha.</p>
        </div>
        <div class="col-md-4 text-end">
            <img src="/static/images/logo.png" alt="MaizGuard Logo" class="img-fluid" style="max-height: 100px;">
        </div>
    </div>

    <!-- Filtros y búsqueda -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="input-group">
                <input type="text" class="form-control" id="searchInput" placeholder="Buscar plagas..." aria-label="Buscar plagas">
                <button class="btn btn-outline-success" type="button" id="searchButton">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </div>
        <div class="col-md-3">
            <select class="form-select" id="faseFilter">
                <option value="">Todas las fases</option>
                {% for fase in fases_cultivo %}
                <option value="{{ fase.id_fase_cultivo }}" {% if filtro_fase == fase.id_fase_cultivo %}selected{% endif %}>
                    {{ fase.descripcion }}
                </option>
                {% endfor %}
            </select>
        </div>
        <div class="col-md-3">
            <select class="form-select" id="temporadaFilter">
                <option value="">Todas las temporadas</option>
                {% for temporada in temporadas %}
                <option value="{{ temporada.id_temporada }}" {% if filtro_temporada == temporada.id_temporada %}selected{% endif %}>
                    {{ temporada.nombre }}
                </option>
                {% endfor %}
            </select>
        </div>
    </div>

    <!-- Resultados de la búsqueda -->
    <div class="row" id="resultadosContainer">
        {% if plagas %}
            {% for plaga in plagas %}
            <div class="col-md-4 mb-4 plaga-card" 
                 data-fase="{{ plaga.id_fase_cultivo }}" 
                 data-temporada="{{ plaga.id_temporada }}" 
                 data-nombre="{{ plaga.nombre|lower }}">
                <div class="card h-100 shadow-sm">
                    <img src="{{ plaga.url_img }}" class="card-img-top" alt="{{ plaga.nombre }}" style="height: 200px; object-fit: cover;">
                    <div class="card-body">
                        <h5 class="card-title">{{ plaga.nombre }}</h5>
                        <h6 class="card-subtitle mb-2 text-muted"><em>{{ plaga.nombre_cientifico }}</em></h6>
                        <p class="card-text">{{ plaga.descripcion_corta }}</p>
                    </div>
                    <div class="card-footer bg-transparent">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <span class="badge bg-primary me-1">{{ plaga.fase_cultivo }}</span>
                                <span class="badge bg-info">{{ plaga.temporada }}</span>
                            </div>
                            <a href="/plaga-publica/{{ plaga.id_plaga }}" class="btn btn-sm btn-outline-success">Ver detalles</a>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        {% else %}
            <div class="col-12 text-center py-5">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i> No se encontraron plagas con los criterios seleccionados.
                </div>
            </div>
        {% endif %}
    </div>

    <!-- Mensaje de no resultados (inicialmente oculto) -->
    <div id="noResultsMessage" class="row d-none">
        <div class="col-12 text-center py-5">
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i> No se encontraron plagas que coincidan con tu búsqueda.
            </div>
        </div>
    </div>
</div>

<!-- JavaScript para filtrado en el cliente -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('searchInput');
    const searchButton = document.getElementById('searchButton');
    const faseFilter = document.getElementById('faseFilter');
    const temporadaFilter = document.getElementById('temporadaFilter');
    const plagaCards = document.querySelectorAll('.plaga-card');
    const resultadosContainer = document.getElementById('resultadosContainer');
    const noResultsMessage = document.getElementById('noResultsMessage');

    // Función para aplicar filtros
    function aplicarFiltros() {
        const searchTerm = searchInput.value.toLowerCase().trim();
        const faseSeleccionada = faseFilter.value;
        const temporadaSeleccionada = temporadaFilter.value;
        
        let contadorVisible = 0;
        
        plagaCards.forEach(card => {
            const nombre = card.dataset.nombre;
            const fase = card.dataset.fase;
            const temporada = card.dataset.temporada;
            
            // Aplicar filtros
            const coincideNombre = nombre.includes(searchTerm) || searchTerm === '';
            const coincideFase = faseSeleccionada === '' || fase === faseSeleccionada;
            const coincideTemporada = temporadaSeleccionada === '' || temporada === temporadaSeleccionada;
            
            // Mostrar u ocultar la tarjeta
            if (coincideNombre && coincideFase && coincideTemporada) {
                card.classList.remove('d-none');
                contadorVisible++;
            } else {
                card.classList.add('d-none');
            }
        });
        
        // Mostrar mensaje de no resultados si es necesario
        if (contadorVisible === 0) {
            resultadosContainer.classList.add('d-none');
            noResultsMessage.classList.remove('d-none');
        } else {
            resultadosContainer.classList.remove('d-none');
            noResultsMessage.classList.add('d-none');
        }
    }
    
    // Eventos para aplicar filtros
    searchButton.addEventListener('click', aplicarFiltros);
    searchInput.addEventListener('keyup', function(event) {
        if (event.key === 'Enter') {
            aplicarFiltros();
        }
    });
    faseFilter.addEventListener('change', aplicarFiltros);
    temporadaFilter.addEventListener('change', aplicarFiltros);
});
</script>
{% endblock %}
