from app.database.config import engine
from sqlalchemy import text

def verificar_datos():
    """
    Verifica si hay usuarios y plagas registrados en la base de datos.
    """
    try:
        with engine.connect() as conn:
            # Verificar usuarios
            result = conn.execute(text("SELECT COUNT(*) FROM usuarios"))
            num_usuarios = result.scalar()
            print(f"Usuarios registrados: {num_usuarios}")
            
            if num_usuarios > 0:
                result = conn.execute(text("SELECT id_usuarios, nombre, apellido, correo_electronico FROM usuarios LIMIT 5"))
                usuarios = result.fetchall()
                print("\nPrimeros usuarios registrados:")
                for usuario in usuarios:
                    print(f"ID: {usuario[0]}, Nombre: {usuario[1]} {usuario[2]}, Email: {usuario[3]}")
            
            # Verificar plagas
            result = conn.execute(text("SELECT COUNT(*) FROM plagas"))
            num_plagas = result.scalar()
            print(f"\nPlagas registradas: {num_plagas}")
            
            if num_plagas > 0:
                result = conn.execute(text("SELECT id_plaga, nombre, nombre_cientifico FROM plagas LIMIT 5"))
                plagas = result.fetchall()
                print("\nPrimeras plagas registradas:")
                for plaga in plagas:
                    print(f"ID: {plaga[0]}, Nombre: {plaga[1]}, Nombre científico: {plaga[2]}")
            
            # Si no hay datos, sugerir cómo crearlos
            if num_usuarios == 0:
                print("\nNo hay usuarios registrados. Debe registrar usuarios a través del formulario de registro.")
            
            if num_plagas == 0:
                print("\nNo hay plagas registradas. Debe ingresar plagas en la base de datos.")
                
    except Exception as e:
        print(f"Error al verificar datos: {e}")

if __name__ == "__main__":
    verificar_datos()
