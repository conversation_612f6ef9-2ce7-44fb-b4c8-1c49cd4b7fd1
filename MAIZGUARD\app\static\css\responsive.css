/* Media queries para responsividad */

/* Ajustes responsivos para tarjetas de plagas */
@media (max-width: 992px) {
    .plague-card {
        margin-bottom: 20px;
    }

    .plague-img {
        height: 180px;
    }

    /* Ajustes responsivos para la página Acerca de */
    .about-content {
        padding: 20px 0;
    }

    .section-title {
        font-size: 1.8rem;
    }

    .mission-card, .vision-card {
        margin-bottom: 30px;
    }

    .about-header {
        padding: 3rem 0;
    }

    .about-header .badge {
        font-size: 0.8rem;
    }
}

@media (max-width: 768px) {
    /* Ajustes para tarjetas de plagas */
    .plague-img {
        height: 160px;
    }

    .plague-card .card-title {
        font-size: 1.2rem;
        margin-bottom: 0.5rem;
    }

    .plague-card .card-text {
        font-size: 0.9rem;
    }

    /* Mejoramos el espaciado entre tarjetas */
    .col-md-4 {
        padding-left: 10px;
        padding-right: 10px;
    }

    main.container {
        padding-left: 10px;
        padding-right: 10px;
    }

    /* Ajustes para el carrusel en tablets */
    #multiImageCarousel .carousel-item .card,
    #multiImageCarousel .carousel-item:nth-child(2) .card {
        height: 200px;
    }

    #multiImageCarousel .carousel-control-prev-icon,
    #multiImageCarousel .carousel-control-next-icon {
        padding: 10px;
    }

    /* Ajustes para el menú en tablets */
    .nav-link {
        font-size: 1rem;
        padding: 0.5rem 0.8rem;
    }

    /* Ajustes para el logo en tablets */
    .navbar-logo {
        height: 45px;
    }

    .rounded-logo {
        height: 45px;
        width: 45px;
        left: -10px;
    }

    .about-page-logo {
        height: 50px;
        border-width: 2px;
        padding: 3px;
        animation-duration: 3s;
    }

    .navbar-brand {
        margin-left: -15px; /* Ajustamos la posición del logo en tablets */
    }

    /* Ajustes para el campo de búsqueda en tablets */
    .navbar form.d-flex,
    .navbar form.search-form {
        max-width: 350px;
        margin: 0 auto;
    }

    .navbar .form-control {
        font-size: 0.9rem;
        height: 40px;
    }

    .navbar .btn-light {
        height: 40px;
        width: 45px;
    }

    /* Ajustes para la página Acerca de */
    .about-header {
        padding: 2.5rem 0;
    }

    .about-header h1 {
        font-size: 2.5rem;
    }

    .section-title {
        font-size: 1.6rem;
        text-align: center;
    }

    .section-title::after {
        left: 50%;
        transform: translateX(-50%);
    }

    .about-content .lead {
        font-size: 1.1rem;
        text-align: center;
    }

    .value-card {
        padding: 15px;
        margin-bottom: 20px;
    }

    .features-list {
        margin-left: 10px;
    }

    .mission-highlights, .vision-goals {
        padding-left: 10px;
    }
}

@media (max-width: 576px) {
    /* Ajustes para tarjetas de plagas */
    .plague-img {
        height: 140px;
    }

    .plague-card {
        margin-bottom: 15px;
    }

    .plague-card .card-title {
        font-size: 1.1rem;
        margin-bottom: 0.4rem;
    }

    .plague-card .card-text {
        font-size: 0.85rem;
        line-height: 1.4;
    }

    /* Ajustamos el padding para móviles */
    .card-body {
        padding: 0.75rem;
    }

    main.container {
        margin-top: 2rem !important;
        margin-bottom: 2rem !important;
    }

    .row {
        margin-left: -5px;
        margin-right: -5px;
    }

    .col-md-4 {
        padding-left: 5px;
        padding-right: 5px;
    }

    /* Ajustes para el carrusel en móviles */
    #multiImageCarousel {
        max-width: 98%; /* Aumentamos el ancho en móviles */
        padding: 10px 0;
    }

    #multiImageCarousel .carousel-item .card,
    #multiImageCarousel .carousel-item:nth-child(2) .card {
        height: 170px;
    }

    #multiImageCarousel .carousel-control-prev-icon,
    #multiImageCarousel .carousel-control-next-icon {
        padding: 8px;
    }

    /* Ajustes para el logo en móviles */
    .navbar-logo {
        height: 40px;
    }

    .rounded-logo {
        height: 40px;
        width: 40px;
        left: -5px;
        margin-right: 5px;
    }

    .about-page-logo {
        height: 45px;
        border-width: 2px;
        padding: 2px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        animation-duration: 4s;
    }

    .navbar-brand {
        margin-left: -5px; /* Reajustamos la posición del logo en móviles */
    }

    /* Ajustes para el menú en móviles */
    .nav-link {
        font-size: 0.95rem;
        padding: 0.4rem 0.6rem;
    }

    /* Mejoras para el menú desplegable en móviles */
    .navbar-collapse {
        background-color: var(--primary-color);
        padding: 10px;
        border-radius: 0 0 8px 8px;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    }

    .navbar-nav {
        margin-top: 5px;
    }

    .navbar-toggler {
        border: none;
        padding: 0.25rem 0.5rem;
    }

    .navbar-toggler:focus {
        box-shadow: none;
        outline: none;
    }

    /* Ajustes para el campo de búsqueda en móviles */
    .navbar form.d-flex,
    .navbar form.search-form {
        max-width: 100%;
        margin: 10px 0;
        order: 1;
    }

    .navbar .form-control {
        font-size: 0.85rem;
        height: 38px;
        padding-left: 15px;
    }

    .navbar .btn-light {
        height: 38px;
        width: 40px;
        padding: 0;
    }

    /* Reorganizamos el navbar en móviles */
    .navbar .container {
        flex-wrap: nowrap;
    }

    .navbar-collapse {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        z-index: 1000;
    }

    /* Ajustes para el título del carrusel */
    .carousel-caption h2 {
        font-size: 1.5rem;
    }

    /* Ajustes para la sección de plagas */
    section.container-fluid {
        padding: 1.5rem 0;
    }

    section h2 {
        font-size: 1.5rem;
        padding-bottom: 8px;
    }

    section h2:after {
        width: 60px;
        height: 2px;
    }

    /* Ajustes para la página Acerca de */
    .about-header {
        padding: 2rem 0;
    }

    .about-header h1 {
        font-size: 2rem;
    }

    .about-header .badge {
        font-size: 0.7rem;
        margin-bottom: 0.5rem;
    }

    .about-img-container {
        margin-bottom: 1.5rem;
    }

    .feature-text, .mission-highlights span, .vision-goals span {
        font-size: 0.9rem;
    }

    .goal-item {
        padding: 0.5rem !important;
    }

    /* Ajustes para el footer en móviles */
    footer {
        padding: 1rem 0;
    }

    footer p {
        font-size: 0.9rem;
    }
}

/* Ajustes para pantallas muy pequeñas */
@media (max-width: 375px) {
    /* Ajustes para el logo en pantallas muy pequeñas */
    .rounded-logo {
        height: 36px;
        width: 36px;
        left: -3px;
        margin-right: 3px;
    }

    .navbar-brand {
        margin-left: -5px;
    }

    /* Ajustes para el campo de búsqueda en pantallas muy pequeñas */
    .navbar .form-control {
        font-size: 0.8rem;
        height: 36px;
        padding-left: 10px;
        border-radius: 20px 0 0 20px;
    }

    .navbar .btn-light {
        height: 36px;
        width: 36px;
        padding: 0;
        border-radius: 0 20px 20px 0;
    }

    /* Ajustes para el menú en pantallas muy pequeñas */
    .nav-link {
        font-size: 0.9rem;
        padding: 0.3rem 0.5rem;
    }

    /* Reducimos aún más el padding para ahorrar espacio */
    .container {
        padding-left: 10px;
        padding-right: 10px;
    }
}
