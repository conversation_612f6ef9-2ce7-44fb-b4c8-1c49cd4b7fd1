<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mis Cultivos - MaizGuard</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Estilos personalizados -->
    <link rel="stylesheet" href="/static/css/main.css">
    <link rel="stylesheet" href="/static/css/navbar.css">
    <link rel="stylesheet" href="/static/css/compact.css">
    <link rel="stylesheet" href="/static/css/super-responsive.css">
    <link rel="stylesheet" href="/static/css/small-laptop.css">
    <link rel="stylesheet" href="/static/css/admin-panel.css">

    <!-- Estilos específicos para cultivos -->
    <style>
        .focused {
            transform: scale(1.02);
            transition: transform 0.2s ease;
        }

        .form-label.fw-bold {
            color: #198754;
        }

        .border-success.border-3 {
            border-left-width: 4px !important;
        }

        .card-header.bg-success {
            background: linear-gradient(135deg, #198754 0%, #20c997 100%) !important;
        }

        .text-success.border-bottom {
            border-color: #198754 !important;
        }

        .btn-success {
            background: linear-gradient(135deg, #198754 0%, #20c997 100%);
            border: none;
            transition: all 0.3s ease;
        }

        .btn-success:hover {
            background: linear-gradient(135deg, #157347 0%, #1aa179 100%);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(25, 135, 84, 0.3);
        }

        .form-control:focus, .form-select:focus {
            border-color: #198754;
            box-shadow: 0 0 0 0.25rem rgba(25, 135, 84, 0.25);
        }

        .is-valid {
            border-color: #198754 !important;
        }

        .is-invalid {
            border-color: #dc3545 !important;
        }

        .card {
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        .badge {
            font-size: 0.8rem;
            padding: 0.5rem 0.75rem;
        }

        .form-text {
            font-size: 0.8rem;
            color: #6c757d;
        }

        @media (max-width: 768px) {
            .container {
                padding-left: 15px;
                padding-right: 15px;
            }

            .card-body {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Incluir el navbar -->
    {% include "partials/navbar.html" %}

    <!-- Espacio de separación después del navbar -->
    <div class="container-fluid py-3">
        <!-- Este div crea un espacio entre el navbar y el contenido -->
    </div>

    <!-- Mensajes de éxito y error -->
    <div class="container py-2">
        {% if request.query_params.get('success') == '1' %}
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <strong>¡Éxito!</strong> Tu cultivo ha sido registrado correctamente.
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        {% endif %}

        {% if request.query_params.get('error') == '1' %}
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>Error:</strong> Hubo un problema al registrar tu cultivo. Por favor, inténtalo de nuevo.
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        {% endif %}

        {% if request.query_params.get('error') == 'fase_invalida' %}
        <div class="alert alert-warning alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <strong>Fase inválida:</strong> Solo se permiten cultivos en fase de maduración o cosecha.
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        {% endif %}
    </div>

    <div class="container py-4">
        <!-- Encabezado -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-0 shadow-sm" style="background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);">
                    <div class="card-body text-center py-2">
                        <div class="row align-items-center">
                            <div class="col-lg-8 mx-auto">
                                <h4 class="fw-bold mb-1 text-white">
                                    <i class="fas fa-seedling me-2 text-warning"></i>Mis Cultivos de Maíz
                                </h4>
                                <p class="mb-2 text-white">
                                    Bienvenido {{ user.nombre }}, aquí puedes registrar y gestionar todos tus cultivos de maíz
                                </p>
                                <div class="row text-center">
                                    <div class="col-md-4 mb-3">
                                        <div class="bg-white bg-opacity-20 rounded-3 p-3">
                                            <h3 class="fw-bold mb-1" id="total-cultivos">0</h3>
                                            <small class="text-white-50">Cultivos Registrados</small>
                                        </div>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <div class="bg-white bg-opacity-20 rounded-3 p-3">
                                            <h3 class="fw-bold mb-1" id="cultivos-maduracion">0</h3>
                                            <small class="text-white-50">En Maduración</small>
                                        </div>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <div class="bg-white bg-opacity-20 rounded-3 p-3">
                                            <h3 class="fw-bold mb-1" id="cultivos-cosecha">0</h3>
                                            <small class="text-white-50">Para Cosecha</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Formulario de registro de cultivo -->
            <div class="col-lg-8 mx-auto">
                <div class="card shadow-sm">
                    <div class="card-header bg-success text-white">
                        <h4 class="mb-0">
                            <i class="fas fa-plus-circle me-2"></i>Registrar Nuevo Cultivo
                        </h4>
                    </div>
                    <div class="card-body">
                        <form id="formRegistrarCultivo" action="/cultivos/crear" method="POST">
                            <!-- Información básica del cultivo -->
                            <div class="row mb-4">
                                <div class="col-12">
                                    <h5 class="text-success border-bottom pb-2 mb-3">
                                        <i class="fas fa-info-circle me-2"></i>Información del Cultivo
                                    </h5>
                                </div>

                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="nombre" class="form-label fw-bold">Nombre del Cultivo *</label>
                                        <input type="text" class="form-control" id="nombre" name="nombre" required
                                               placeholder="Ej: Maíz Amarillo Criollo">
                                        <div class="form-text">Especifica el tipo y variedad de maíz</div>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="ubicacion" class="form-label fw-bold">Ubicación</label>
                                        <input type="text" class="form-control" id="ubicacion" name="ubicacion"
                                               placeholder="Ej: Parcela Norte, Lote 3">
                                        <div class="form-text">Donde está ubicado tu cultivo</div>
                                    </div>
                                </div>

                                <div class="col-12">
                                    <div class="mb-3">
                                        <label for="descripcion" class="form-label fw-bold">Descripción del Cultivo *</label>
                                        <textarea class="form-control" id="descripcion" name="descripcion" rows="3" required
                                                  placeholder="Describe las características de tu cultivo: variedad, propósito (consumo familiar, venta), condiciones especiales, etc."></textarea>
                                        <div class="form-text">Proporciona detalles sobre tu cultivo</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Cantidad y dimensiones -->
                            <div class="row mb-4">
                                <div class="col-12">
                                    <h5 class="text-success border-bottom pb-2 mb-3">
                                        <i class="fas fa-ruler-combined me-2"></i>Cantidad y Dimensiones
                                    </h5>
                                </div>

                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="cantidad" class="form-label fw-bold">Cantidad *</label>
                                        <input type="number" class="form-control" id="cantidad" name="cantidad" required min="1"
                                               placeholder="100">
                                        <div class="form-text">Número de plantas o área</div>
                                    </div>
                                </div>

                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="unidad_cantidad" class="form-label fw-bold">Unidad de Medida *</label>
                                        <select class="form-select" id="unidad_cantidad" name="unidad_cantidad" required>
                                            <option value="plantas">Plantas</option>
                                            <option value="metros_cuadrados">Metros Cuadrados (m²)</option>
                                            <option value="hectareas">Hectáreas (ha)</option>
                                        </select>
                                        <div class="form-text">Cómo mides tu cultivo</div>
                                    </div>
                                </div>

                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="fase_cultivo" class="form-label fw-bold">Fase del Cultivo *</label>
                                        <select class="form-select" id="fase_cultivo" name="fase_cultivo" required>
                                            <option value="">Selecciona la fase</option>
                                            <option value="maduracion">Maduración</option>
                                            <option value="cosecha">Cosecha</option>
                                        </select>
                                        <div class="form-text">Etapa actual de desarrollo</div>
                                    </div>
                                </div>

                                <div class="col-12">
                                    <div class="mb-3">
                                        <label for="plaga_afecta" class="form-label fw-bold">Plaga que Afecta al Cultivo</label>
                                        <select class="form-select" id="plaga_afecta" name="plaga_afecta">
                                            <option value="">Selecciona una plaga (opcional)</option>
                                            {% for plaga in plagas %}
                                            <option value="{{ plaga.id_plaga }}" data-fase="{{ plaga.fase_cultivo }}">
                                                {{ plaga.nombre }} - {{ plaga.tipo }} ({{ plaga.fase_cultivo }})
                                            </option>
                                            {% endfor %}
                                        </select>
                                        <div class="form-text">Si tu cultivo está siendo afectado por alguna plaga, selecciónala aquí</div>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="ancho" class="form-label fw-bold">Ancho del Área (metros) *</label>
                                        <input type="number" class="form-control" id="ancho" name="ancho" required step="0.01" min="0.01"
                                               placeholder="10.5">
                                        <div class="form-text">Ancho del terreno en metros</div>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="altura" class="form-label fw-bold">Altura del Área (metros) *</label>
                                        <input type="number" class="form-control" id="altura" name="altura" required step="0.01" min="0.01"
                                               placeholder="8.0">
                                        <div class="form-text">Altura del terreno en metros</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Fechas y notas adicionales -->
                            <div class="row mb-4">
                                <div class="col-12">
                                    <h5 class="text-success border-bottom pb-2 mb-3">
                                        <i class="fas fa-calendar-alt me-2"></i>Fechas y Notas Adicionales
                                    </h5>
                                </div>

                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="fecha_siembra" class="form-label fw-bold">Fecha de Siembra</label>
                                        <input type="date" class="form-control" id="fecha_siembra" name="fecha_siembra">
                                        <div class="form-text">Cuándo sembraste tu cultivo</div>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="fecha_estimada_cosecha" class="form-label fw-bold">Fecha Estimada de Cosecha</label>
                                        <input type="date" class="form-control" id="fecha_estimada_cosecha" name="fecha_estimada_cosecha">
                                        <div class="form-text">Cuándo planeas cosechar</div>
                                    </div>
                                </div>

                                <div class="col-12">
                                    <div class="mb-3">
                                        <label for="notas" class="form-label fw-bold">Notas y Observaciones</label>
                                        <textarea class="form-control" id="notas" name="notas" rows="3"
                                                  placeholder="Agrega cualquier información adicional: cuidados especiales, observaciones, recordatorios, etc."></textarea>
                                        <div class="form-text">Información adicional sobre tu cultivo</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Botones de acción -->
                            <div class="row">
                                <div class="col-12">
                                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                        <button type="reset" class="btn btn-outline-secondary me-md-2">
                                            <i class="fas fa-undo me-2"></i>Limpiar Formulario
                                        </button>
                                        <button type="submit" class="btn btn-success">
                                            <i class="fas fa-save me-2"></i>Registrar Mi Cultivo
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sección de cultivos registrados -->
        {% if cultivos %}
        <div class="row mt-5">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-header bg-light">
                        <h4 class="mb-0 text-success">
                            <i class="fas fa-list me-2"></i>Mis Cultivos Registrados (<span id="contador-lista-cultivos">0</span>)
                        </h4>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            {% for cultivo in cultivos %}
                            <div class="col-md-6 col-lg-4 mb-3">
                                <div class="card h-100 border-start border-success border-3">
                                    <div class="card-body">
                                        <h6 class="card-title text-success">{{ cultivo.nombre }}</h6>
                                        <p class="card-text small text-muted">{{ cultivo.descripcion[:80] }}...</p>
                                        <div class="mb-2">
                                            <span class="badge bg-{{ 'success' if cultivo.fase_cultivo == 'cosecha' else 'warning' if cultivo.fase_cultivo == 'maduracion' else 'info' }}">
                                                {{ cultivo.fase_cultivo|title }}
                                            </span>
                                        </div>
                                        <small class="text-muted">
                                            <i class="fas fa-seedling me-1"></i>{{ cultivo.cantidad }} {{ cultivo.unidad_cantidad }}<br>
                                            <i class="fas fa-ruler me-1"></i>{{ cultivo.ancho }} × {{ cultivo.altura }} m<br>
                                            {% if cultivo.ubicacion %}
                                            <i class="fas fa-map-marker-alt me-1"></i>{{ cultivo.ubicacion }}<br>
                                            {% endif %}
                                            {% if cultivo.fecha_siembra %}
                                            <i class="fas fa-calendar-plus me-1"></i>Siembra: {{ cultivo.fecha_siembra[:10] }}
                                            {% endif %}
                                        </small>
                                        <div class="mt-3">
                                            <div class="btn-group w-100" role="group">
                                                <button type="button" class="btn btn-outline-primary btn-sm" onclick="verDetalleCultivo({{ cultivo.id_cultivo }})" title="Ver detalles">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button type="button" class="btn btn-outline-warning btn-sm" onclick="editarCultivo({{ cultivo.id_cultivo }})" title="Editar">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button type="button" class="btn btn-outline-danger btn-sm" onclick="eliminarCultivo({{ cultivo.id_cultivo }}, '{{ cultivo.nombre }}')" title="Eliminar">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="card-footer bg-transparent">
                                        <small class="text-muted">
                                            <i class="fas fa-calendar me-1"></i>Registrado: {{ cultivo.fecha_registro[:10] if cultivo.fecha_registro else '' }}
                                        </small>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>

    <!-- Modal para ver detalles del cultivo -->
    <div class="modal fade" id="modalDetalleCultivo" tabindex="-1" aria-labelledby="modalDetalleCultivoLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-success text-white">
                    <h5 class="modal-title" id="modalDetalleCultivoLabel">
                        <i class="fas fa-seedling me-2"></i>Detalles del Cultivo
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="contenidoDetalleCultivo">
                    <!-- El contenido se cargará dinámicamente -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal para editar cultivo -->
    <div class="modal fade" id="modalEditarCultivo" tabindex="-1" aria-labelledby="modalEditarCultivoLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-warning text-dark">
                    <h5 class="modal-title" id="modalEditarCultivoLabel">
                        <i class="fas fa-edit me-2"></i>Editar Cultivo
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="formEditarCultivo" method="POST">
                    <div class="modal-body" id="contenidoEditarCultivo">
                        <!-- El contenido se cargará dinámicamente -->
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                        <button type="submit" class="btn btn-warning">
                            <i class="fas fa-save me-2"></i>Guardar Cambios
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal para confirmar eliminación -->
    <div class="modal fade" id="modalEliminarCultivo" tabindex="-1" aria-labelledby="modalEliminarCultivoLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title" id="modalEliminarCultivoLabel">
                        <i class="fas fa-exclamation-triangle me-2"></i>Confirmar Eliminación
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>¿Estás seguro de que deseas eliminar el cultivo <strong id="nombreCultivoEliminar"></strong>?</p>
                    <p class="text-muted small">Esta acción no se puede deshacer.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="button" class="btn btn-danger" id="btnConfirmarEliminar">
                        <i class="fas fa-trash me-2"></i>Eliminar
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Incluir el footer -->
    {% include "partials/footer.html" %}

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Scripts personalizados -->
    <script src="/static/js/scripts.js"></script>
    <script src="/static/js/cultivos-gestion.js"></script>

    <!-- Cargar datos de cultivos para JavaScript -->
    <script>
        // Calcular estadísticas al cargar la página
        document.addEventListener('DOMContentLoaded', function() {
            calcularEstadisticas();
        });

        function calcularEstadisticas() {
            const cultivos = {{ cultivos | tojson | safe }};

            // Contar cultivos por fase
            let maduracion = 0;
            let cosecha = 0;
            let total = cultivos.length;

            cultivos.forEach(cultivo => {
                if (cultivo.fase_cultivo === 'maduracion' || cultivo.fase_cultivo === 'maduración') {
                    maduracion++;
                } else if (cultivo.fase_cultivo === 'cosecha') {
                    cosecha++;
                }
            });

            // Actualizar contadores
            document.getElementById('total-cultivos').textContent = total;
            document.getElementById('cultivos-maduracion').textContent = maduracion;
            document.getElementById('cultivos-cosecha').textContent = cosecha;

            // Actualizar contador de la lista si existe
            const contadorLista = document.getElementById('contador-lista-cultivos');
            if (contadorLista) {
                contadorLista.textContent = total;
            }
        }

        // Cargar datos de cultivos para las funciones de gestión
        const cultivosData = {{ cultivos | tojson | safe }};
        cargarCultivos(cultivosData);
    </script>
</body>
</html>
