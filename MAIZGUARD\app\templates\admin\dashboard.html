<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Panel de Administración - MAIZGUARD</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Estilos personalizados - EXACTAMENTE IGUAL QUE INICIO -->
    <link rel="stylesheet" href="/static/css/main.css">
    <link rel="stylesheet" href="/static/css/navbar.css">
    <link rel="stylesheet" href="/static/css/compact.css">
    <link rel="stylesheet" href="/static/css/super-responsive.css">
    <link rel="stylesheet" href="/static/css/small-laptop.css">
    <link rel="stylesheet" href="/static/css/admin.css">
    <link rel="stylesheet" href="/static/css/admin-panel.css">
</head>
<body id="admin-app" class="admin-page">
    <!-- Incluir el navbar normal -->
    {% include "partials/navbar.html" %}

    <!-- Estilos específicos para navbar del admin -->
    <style>
        .navbar {
            padding-top: 0.3rem !important;
            padding-bottom: 0.3rem !important;
            min-height: 55px !important;
        }

        .rounded-logo {
            height: 35px !important;
            width: 35px !important;
            margin-right: 8px !important;
            margin-left: 15px !important;
        }

        .navbar-brand {
            font-size: 1.1rem !important;
            margin-left: 0 !important;
        }

        .nav-link {
            font-size: 0.95rem !important;
            padding: 0.4rem 0.8rem !important;
        }

        .search-form .form-control {
            padding: 0.5rem 1rem !important;
            font-size: 0.9rem !important;
        }

        .search-form .btn {
            padding: 0.5rem 1rem !important;
        }
    </style>

    <!-- Contenedor principal sin espaciado extra -->
    <div class="container-fluid admin-container">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block admin-sidebar">
                <div class="sidebar-content">
                    <div class="text-center mb-4">
                        <h5 class="text-white">Panel de Administración</h5>
                        <p class="text-light small">Bienvenido, {{ user.nombre }}</p>
                    </div>

                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="sidebar-link active" href="/admin">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="sidebar-link" href="/admin/usuarios">
                                <i class="fas fa-users me-2"></i>
                                Usuarios
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="sidebar-link" href="/admin/cultivos-usuarios">
                                <i class="fas fa-seedling me-2"></i>
                                Cultivos de Usuarios
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="sidebar-link" href="/admin/plagas">
                                <i class="fas fa-bug me-2"></i>
                                Plagas
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="sidebar-link" href="/">
                                <i class="fas fa-home me-2"></i>
                                Volver al Inicio
                            </a>
                        </li>
                        <li class="nav-item mt-3">
                            <a class="sidebar-link text-danger" href="/logout">
                                <i class="fas fa-sign-out-alt me-2"></i>
                                Cerrar Sesión
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Contenido principal -->
            <main class="col-md-9 col-lg-10 admin-main-content">
                    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                        <h1 class="h2">Dashboard de Administración</h1>
                    </div>

                    <!-- Estadísticas -->
                    <div class="row mb-4">
                        <div class="col-lg-3 col-md-6 mb-4">
                            <div class="card bg-primary text-white shadow">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <div class="text-white-50 small">Usuarios Registrados</div>
                                            <div class="h4 mb-0" id="total-usuarios">Cargando...</div>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-users fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-3 col-md-6 mb-4">
                            <div class="card bg-success text-white shadow">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <div class="text-white-50 small">Usuarios Activos</div>
                                            <div class="h4 mb-0" id="usuarios-activos">Cargando...</div>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-user-check fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-3 col-md-6 mb-4">
                            <div class="card bg-info text-white shadow">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <div class="text-white-50 small">Plagas Registradas</div>
                                            <div class="h4 mb-0" id="total-plagas">Cargando...</div>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-bug fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-3 col-md-6 mb-4">
                            <div class="card bg-warning text-white shadow">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <div class="text-white-50 small">Cultivos</div>
                                            <div class="h4 mb-0" id="total-cultivos">Cargando...</div>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-seedling fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Actividad Reciente y Usuarios Recientes -->
                    <div class="row">
                        <div class="col-lg-6 mb-4">
                            <div class="card shadow mb-4">
                                <div class="card-header py-3">
                                    <h6 class="m-0 font-weight-bold text-primary">Actividad Reciente</h6>
                                </div>
                                <div class="card-body">
                                    <div id="actividades-recientes">
                                        <div class="text-center">
                                            <i class="fas fa-spinner fa-spin"></i>
                                            Cargando actividades recientes...
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-6 mb-4">
                            <div class="card shadow mb-4">
                                <div class="card-header py-3">
                                    <h6 class="m-0 font-weight-bold text-primary">Usuarios Recientes</h6>
                                </div>
                                <div class="card-body">
                                    <div id="usuarios-recientes">
                                        <div class="text-center">
                                            <i class="fas fa-spinner fa-spin"></i>
                                            Cargando usuarios recientes...
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </main>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- JavaScript del Dashboard -->
    <script>
        // Cargar datos cuando la página esté lista
        document.addEventListener('DOMContentLoaded', function() {
            cargarEstadisticas();
            cargarActividades();
            cargarUsuariosRecientes();
        });

        // Función para cargar estadísticas
        async function cargarEstadisticas() {
            try {
                const response = await fetch('/admin/api/estadisticas');
                if (response.ok) {
                    const estadisticas = await response.json();

                    // Actualizar la interfaz
                    document.getElementById('total-usuarios').textContent = estadisticas.total_usuarios || 0;
                    document.getElementById('usuarios-activos').textContent = estadisticas.usuarios_activos || 0;
                    document.getElementById('total-plagas').textContent = estadisticas.total_plagas || 0;
                    document.getElementById('total-cultivos').textContent = estadisticas.total_cultivos || 0;
                } else {
                    console.error('Error al cargar estadísticas:', response.status);
                    mostrarErrorEstadisticas();
                }
            } catch (error) {
                console.error('Error al cargar estadísticas:', error);
                mostrarErrorEstadisticas();
            }
        }

        // Función para cargar actividades
        async function cargarActividades() {
            try {
                const response = await fetch('/admin/api/actividades-recientes');
                if (response.ok) {
                    const actividades = await response.json();
                    mostrarActividades(actividades);
                } else {
                    document.getElementById('actividades-recientes').innerHTML = '<p class="text-muted">Error al cargar actividades</p>';
                }
            } catch (error) {
                console.error('Error al cargar actividades:', error);
                document.getElementById('actividades-recientes').innerHTML = '<p class="text-muted">Error al cargar actividades</p>';
            }
        }

        // Función para cargar usuarios recientes
        async function cargarUsuariosRecientes() {
            try {
                const response = await fetch('/admin/api/usuarios-recientes');
                if (response.ok) {
                    const usuarios = await response.json();
                    mostrarUsuariosRecientes(usuarios);
                } else {
                    document.getElementById('usuarios-recientes').innerHTML = '<p class="text-muted">Error al cargar usuarios</p>';
                }
            } catch (error) {
                console.error('Error al cargar usuarios recientes:', error);
                document.getElementById('usuarios-recientes').innerHTML = '<p class="text-muted">Error al cargar usuarios</p>';
            }
        }

        // Mostrar error en estadísticas
        function mostrarErrorEstadisticas() {
            document.getElementById('total-usuarios').textContent = '0';
            document.getElementById('usuarios-activos').textContent = '0';
            document.getElementById('total-plagas').textContent = '0';
            document.getElementById('total-cultivos').textContent = '0';
        }

        // Mostrar actividades
        function mostrarActividades(actividades) {
            const container = document.getElementById('actividades-recientes');
            if (!actividades || actividades.length === 0) {
                container.innerHTML = '<p class="text-muted">No hay actividades recientes</p>';
                return;
            }

            let html = '';
            actividades.forEach(actividad => {
                html += `
                    <div class="d-flex align-items-center mb-3">
                        <div class="me-3">
                            <i class="${actividad.icono || 'fas fa-info-circle'}"></i>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="mb-1">${actividad.titulo || 'Actividad'}</h6>
                            <p class="mb-1 text-muted small">${actividad.descripcion || ''}</p>
                            <small class="text-muted">${actividad.fecha || ''}</small>
                        </div>
                    </div>
                `;
            });
            container.innerHTML = html;
        }

        // Mostrar usuarios recientes
        function mostrarUsuariosRecientes(usuarios) {
            const container = document.getElementById('usuarios-recientes');
            if (!usuarios || usuarios.length === 0) {
                container.innerHTML = '<p class="text-muted">No hay usuarios registrados</p>';
                return;
            }

            let html = '<div class="table-responsive"><table class="table table-sm"><tbody>';
            usuarios.forEach(usuario => {
                const estadoBadge = usuario.estado === 'Activo' ? 'success' : 'secondary';
                html += `
                    <tr>
                        <td>
                            <strong>${usuario.nombre || ''} ${usuario.apellido || ''}</strong><br>
                            <small class="text-muted">${usuario.correo_electronico || ''}</small>
                        </td>
                        <td class="text-end">
                            <span class="badge bg-${estadoBadge}">${usuario.estado || 'Desconocido'}</span><br>
                            <small class="text-muted">${usuario.fecha_registro || ''}</small>
                        </td>
                    </tr>
                `;
            });
            html += '</tbody></table></div>';
            container.innerHTML = html;
        }
    </script>
</body>
</html>
