#!/usr/bin/env python3
"""
Script para corregir los tipos de plagas mal clasificadas en la base de datos MaizGuard
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from app.database.config import engine
from sqlalchemy import text

def corregir_tipos_plagas():
    """Corregir los tipos de plagas mal clasificadas"""
    try:
        with engine.connect() as conn:
            # Obtener IDs de tipos de plaga
            result = conn.execute(text("SELECT id_tipo_plaga, nombre FROM tipos_plaga"))
            tipos = {row.nombre: row.id_tipo_plaga for row in result}
            
            print("🔧 Corrigiendo tipos de plagas mal clasificadas...")
            
            # HONGOS que están mal clasificados como BACTERIAS
            hongos_mal_clasificados = [
                "A<PERSON>latoxina",
                "Mancha de Alternaria", 
                "Manchas Negras",
                "Moho de la Mazorca",
                "Moho Gris",
                "Pudrición de la Raíz",
                "Pudrición de Mazorca",
                "Pudrición del Tallo",
                "Pudrición por Penicillium"
            ]
            
            # BACTERIAS que están mal clasificadas como VIRUS
            bacterias_mal_clasificadas = [
                "Bacilo Cereus",
                "Mancha Bacteriana del Maíz",
                "Podredumbre Bacteriana del Maíz",
                "Podredumbre Blanda Bacteriana",
                "Podredumbre Húmeda",
                "Pseudomonas",
                "Xanthomonas"
            ]
            
            # VIRUS que están mal clasificados como MALEZA
            virus_mal_clasificados = [
                "Virus de la Estría Rugosa del Maíz",
                "Virus del Achaparramiento del Maíz",
                "Virus del Mosaico de la Caña de Azúcar",
                "Virus del Mosaico del Maíz",
                "Virus del Rayado Clorótico del Maíz",
                "Virus del Rayado Fino del Maíz"
            ]
            
            # Corregir HONGOS
            for plaga_nombre in hongos_mal_clasificados:
                result = conn.execute(text("""
                    UPDATE plagas 
                    SET id_tipo_plaga = :tipo_correcto 
                    WHERE nombre = :nombre
                """), {
                    "tipo_correcto": tipos["Hongo"],
                    "nombre": plaga_nombre
                })
                
                if result.rowcount > 0:
                    print(f"✅ '{plaga_nombre}' corregido a HONGO")
                else:
                    print(f"⚠️  No se encontró '{plaga_nombre}'")
            
            # Corregir BACTERIAS
            for plaga_nombre in bacterias_mal_clasificadas:
                result = conn.execute(text("""
                    UPDATE plagas 
                    SET id_tipo_plaga = :tipo_correcto 
                    WHERE nombre = :nombre
                """), {
                    "tipo_correcto": tipos["Bacteria"],
                    "nombre": plaga_nombre
                })
                
                if result.rowcount > 0:
                    print(f"✅ '{plaga_nombre}' corregido a BACTERIA")
                else:
                    print(f"⚠️  No se encontró '{plaga_nombre}'")
            
            # Corregir VIRUS
            for plaga_nombre in virus_mal_clasificados:
                result = conn.execute(text("""
                    UPDATE plagas 
                    SET id_tipo_plaga = :tipo_correcto 
                    WHERE nombre = :nombre
                """), {
                    "tipo_correcto": tipos["Virus"],
                    "nombre": plaga_nombre
                })
                
                if result.rowcount > 0:
                    print(f"✅ '{plaga_nombre}' corregido a VIRUS")
                else:
                    print(f"⚠️  No se encontró '{plaga_nombre}'")
            
            conn.commit()
            print("\n🎉 Corrección de tipos completada!")
            
            # Verificar corrección
            print("\n📊 Verificando corrección...")
            result = conn.execute(text("""
                SELECT tp.nombre as tipo, COUNT(p.id_plaga) as cantidad
                FROM tipos_plaga tp
                LEFT JOIN plagas p ON tp.id_tipo_plaga = p.id_tipo_plaga
                WHERE p.id_fase_cultivo = 4 AND p.id_temporada = 1
                GROUP BY tp.id_tipo_plaga, tp.nombre
                ORDER BY tp.nombre
            """))
            
            print("Distribución corregida:")
            for row in result:
                print(f"  📊 {row.tipo}: {row.cantidad} plagas")
            
    except Exception as e:
        print(f"❌ Error al corregir tipos: {e}")

if __name__ == "__main__":
    print("🌽 Corrigiendo tipos de plagas en MaizGuard...")
    corregir_tipos_plagas()
