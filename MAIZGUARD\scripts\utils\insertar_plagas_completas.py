#!/usr/bin/env python3
"""
Script para insertar todas las plagas específicas de maíz en la base de datos MaizGuard
Configuradas para fase de maduración y temporada seca
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from app.database.config import engine
from sqlalchemy import text

def insertar_plagas_completas():
    """Insertar todas las plagas específicas de maíz"""
    try:
        with engine.connect() as conn:
            # Obtener el ID de un usuario administrador
            result = conn.execute(text("SELECT id_usuarios FROM usuarios WHERE id_rol = 1 LIMIT 1"))
            admin_user = result.fetchone()
            
            if not admin_user:
                result = conn.execute(text("SELECT id_usuarios FROM usuarios LIMIT 1"))
                admin_user = result.fetchone()
                admin_user_id = admin_user.id_usuarios if admin_user else 1
            else:
                admin_user_id = admin_user.id_usuarios

            # INSECTOS (id_tipo_plaga = 1)
            insectos = [
                {
                    "nombre": "Escarabajo del Maíz",
                    "nombre_cientifico": "Sitophilus zeamais",
                    "descripcion": "Insecto pequeño que perfora los granos almacenados, causando daño y reducción de calidad. Las larvas se desarrollan dentro de los granos, afectando su calidad y valor comercial.",
                    "recomendaciones": """
CONTROL QUÍMICO:
• Plaguicida: Fosfina (Fosfuro de aluminio)
• Cantidad: 3-5 tabletas por tonelada de grano (0.3-0.5 tabletas por 100 kg)
• Aplicación: Fumigación en silos herméticos
• Frecuencia: Una aplicación por período de almacenamiento

• Plaguicida alternativo: Deltametrina 2.5% WP
• Cantidad: 0.5-1.0 kg por tonelada (50-100 g por 100 kg)
• Aplicación: Aspersión sobre granos antes del almacenamiento
• Método: Mezclar uniformemente con el grano

CONTROL FÍSICO:
• Almacenamiento hermético en silos metálicos
• Control de humedad <14% en granos
• Temperatura de almacenamiento <15°C
• Limpieza previa de instalaciones de almacenamiento
                    """
                },
                {
                    "nombre": "Gusano de la Mazorca",
                    "nombre_cientifico": "Ostrinia nubilalis",
                    "descripcion": "Oruga que perfora las mazorcas, dañando los granos y creando vías para hongos y bacterias. Las larvas son de color crema con manchas oscuras.",
                    "recomendaciones": """
CONTROL QUÍMICO:
• Plaguicida: Clorpirifós 48% EC
• Cantidad: 1.0-1.5 litros por hectárea (100-150 ml por 100 m²)
• Aplicación: Aspersión dirigida a mazorcas en formación
• Frecuencia: Cada 10-15 días durante floración

• Plaguicida biológico: Bacillus thuringiensis var. kurstaki
• Cantidad: 1.5-2.0 kg por hectárea (150-200 g por 100 m²)
• Aplicación: Aspersión foliar en horas de la tarde
• Frecuencia: Cada 7 días

CONTROL BIOLÓGICO:
• Liberación de Trichogramma brassicae: 200,000 individuos por hectárea
• Aplicación: 3 liberaciones cada 10 días durante floración
• Uso de feromonas para monitoreo: 1 trampa cada 25 m²
                    """
                },
                {
                    "nombre": "Mosca de la Mazorca",
                    "nombre_cientifico": "Hylemya sp.",
                    "descripcion": "Larvas de mosca que dañan los granos, afectando la calidad y el rendimiento del maíz. Se desarrollan dentro de las mazorcas.",
                    "recomendaciones": """
CONTROL QUÍMICO:
• Plaguicida: Diazinón 60% EC
• Cantidad: 1.0-1.5 litros por hectárea (100-150 ml por 100 m²)
• Aplicación: Aspersión dirigida a mazorcas
• Frecuencia: Cada 15 días durante formación de mazorcas

CONTROL CULTURAL:
• Eliminación de residuos de cosecha
• Rotación con cultivos no hospederos
• Cosecha oportuna para evitar sobremaduración
• Manejo de malezas hospederas
                    """
                },
                {
                    "nombre": "Escarabajo de la Espiga",
                    "nombre_cientifico": "Tanymecus dilaticollis",
                    "descripcion": "Insecto que daña las espigas durante la formación y cosecha, reduciendo el rendimiento. Adultos de color gris-marrón.",
                    "recomendaciones": """
CONTROL QUÍMICO:
• Plaguicida: Carbaril 85% WP
• Cantidad: 1.5-2.0 kg por hectárea (150-200 g por 100 m²)
• Aplicación: Aspersión foliar dirigida a espigas
• Frecuencia: Al detectar primeros adultos

• Plaguicida sistémico: Imidacloprid 35% SC
• Cantidad: 0.3-0.5 litros por hectárea (30-50 ml por 100 m²)
• Aplicación: Aspersión foliar
• Momento: Durante emergencia de espigas
                    """
                }
            ]

            # ÁCAROS (id_tipo_plaga = 6 - necesitamos crear este tipo)
            acaros = [
                {
                    "nombre": "Ácaro del Tizón",
                    "nombre_cientifico": "Steneotarsonemus spinki",
                    "descripcion": "Ácaros diminutos de color marrón claro o amarillento. Se alimentan de las células de las hojas, provocando manchas cloróticas y decoloración.",
                    "recomendaciones": """
CONTROL QUÍMICO:
• Acaricida: Abamectina 1.8% EC
• Cantidad: 0.5-0.8 litros por hectárea (50-80 ml por 100 m²)
• Aplicación: Aspersión foliar con énfasis en el envés
• Frecuencia: Cada 10-12 días, máximo 2 aplicaciones

• Acaricida alternativo: Azufre mojable 80% WP
• Cantidad: 2.5-3.0 kg por hectárea (250-300 g por 100 m²)
• Aplicación: Aspersión foliar
• Momento: Evitar horas de alta temperatura
                    """
                },
                {
                    "nombre": "Ácaro Rojo del Maíz",
                    "nombre_cientifico": "Oligonychus pratensis",
                    "descripcion": "Ácaros de color rojo oscuro que suelen atacar el envés de las hojas. Causan clorosis, que puede llevar al secado de las hojas y afectar el rendimiento.",
                    "recomendaciones": """
CONTROL QUÍMICO:
• Acaricida: Propargite 57% EC
• Cantidad: 1.0-1.5 litros por hectárea (100-150 ml por 100 m²)
• Aplicación: Aspersión foliar dirigida al envés de hojas
• Frecuencia: Cada 15 días según infestación

CONTROL BIOLÓGICO:
• Liberación de ácaros depredadores (Phytoseiulus persimilis)
• Cantidad: 10-15 individuos por m²
• Aplicación: Distribución manual en focos
• Condiciones: Humedad relativa >60%
                    """
                },
                {
                    "nombre": "Ácaro Blanco",
                    "nombre_cientifico": "Polyphagotarsonemus latus",
                    "descripcion": "Ácaros pequeños de color blanco translúcido que afectan la parte superior de las plantas jóvenes. Deforman hojas y retrasan el crecimiento del cultivo.",
                    "recomendaciones": """
CONTROL QUÍMICO:
• Acaricida: Spiromesifen 24% SC
• Cantidad: 0.4-0.6 litros por hectárea (40-60 ml por 100 m²)
• Aplicación: Aspersión foliar con buen mojamiento
• Frecuencia: Una aplicación, repetir si es necesario a los 15 días

MANEJO CULTURAL:
• Eliminación de malezas hospederas
• Manejo de humedad relativa
• Monitoreo constante de plantas jóvenes
                    """
                }
            ]

            # Primero, verificar si existe el tipo de plaga "Ácaro"
            result = conn.execute(text("SELECT id_tipo_plaga FROM tipos_plaga WHERE nombre = 'Ácaro'"))
            acaro_tipo = result.fetchone()
            
            if not acaro_tipo:
                # Crear el tipo de plaga "Ácaro"
                conn.execute(text("""
                    INSERT INTO tipos_plaga (nombre, descripcion) 
                    VALUES ('Ácaro', 'Arácnidos microscópicos que se alimentan de plantas')
                """))
                result = conn.execute(text("SELECT LAST_INSERT_ID() as id"))
                acaro_tipo_id = result.fetchone().id
                print("✅ Tipo de plaga 'Ácaro' creado.")
            else:
                acaro_tipo_id = acaro_tipo.id_tipo_plaga

            # Insertar INSECTOS
            for plaga in insectos:
                result = conn.execute(text("""
                    SELECT COUNT(*) as count FROM plagas 
                    WHERE nombre = :nombre
                """), {"nombre": plaga["nombre"]})
                
                if result.fetchone().count == 0:
                    conn.execute(text("""
                        INSERT INTO plagas (
                            nombre, nombre_cientifico, url_img, descripcion, recomendaciones,
                            id_fase_cultivo, id_tipo_plaga, id_temporada, id_usuarios
                        ) VALUES (
                            :nombre, :nombre_cientifico, :url_img, :descripcion, :recomendaciones,
                            4, 1, 1, :id_usuarios
                        )
                    """), {
                        **plaga,
                        "url_img": f"/static/images/{plaga['nombre'].lower().replace(' ', '-')}.jpg",
                        "id_usuarios": admin_user_id
                    })
                    print(f"✅ Insecto '{plaga['nombre']}' insertado.")

            # Insertar ÁCAROS
            for plaga in acaros:
                result = conn.execute(text("""
                    SELECT COUNT(*) as count FROM plagas 
                    WHERE nombre = :nombre
                """), {"nombre": plaga["nombre"]})
                
                if result.fetchone().count == 0:
                    conn.execute(text("""
                        INSERT INTO plagas (
                            nombre, nombre_cientifico, url_img, descripcion, recomendaciones,
                            id_fase_cultivo, id_tipo_plaga, id_temporada, id_usuarios
                        ) VALUES (
                            :nombre, :nombre_cientifico, :url_img, :descripcion, :recomendaciones,
                            4, :id_tipo_plaga, 1, :id_usuarios
                        )
                    """), {
                        **plaga,
                        "url_img": f"/static/images/{plaga['nombre'].lower().replace(' ', '-')}.jpg",
                        "id_tipo_plaga": acaro_tipo_id,
                        "id_usuarios": admin_user_id
                    })
                    print(f"✅ Ácaro '{plaga['nombre']}' insertado.")

            conn.commit()
            print(f"\n🎉 Primera parte completada! Insertados insectos y ácaros.")
            
    except Exception as e:
        print(f"❌ Error al insertar plagas: {e}")

if __name__ == "__main__":
    print("🌽 Insertando plagas completas de maíz - Parte 1...")
    insertar_plagas_completas()
