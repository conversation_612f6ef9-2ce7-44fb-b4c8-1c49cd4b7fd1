from app.database.config import engine
from sqlalchemy import text

def actualizar_categorias():
    """
    Actualiza las categorías de plagas sin eliminar registros.
    """
    try:
        with engine.connect() as conn:
            # Actualizar las categorías existentes
            print("Actualizando categorías existentes...")
            
            # Fases de cultivo
            conn.execute(text("UPDATE fases_cultivo SET descripcion = 'Germinación' WHERE id_fase_cultivo = 1"))
            conn.execute(text("UPDATE fases_cultivo SET descripcion = 'Crecimiento vegetativo' WHERE id_fase_cultivo = 2"))
            conn.execute(text("UPDATE fases_cultivo SET descripcion = 'Floración' WHERE id_fase_cultivo = 3"))
            conn.execute(text("UPDATE fases_cultivo SET descripcion = 'Maduración' WHERE id_fase_cultivo = 4"))
            conn.execute(text("UPDATE fases_cultivo SET descripcion = 'Cosecha' WHERE id_fase_cultivo = 5"))
            
            # Temporadas
            conn.execute(text("UPDATE temporadas SET descripcion = 'Temporada de lluvia' WHERE id_temporada = 1"))
            conn.execute(text("UPDATE temporadas SET descripcion = 'Temporada de sequía' WHERE id_temporada = 2"))
            conn.execute(text("UPDATE temporadas SET descripcion = 'Ambas temporadas' WHERE id_temporada = 3"))
            conn.execute(text("UPDATE temporadas SET descripcion = 'Temporada de lluvia (intensa)' WHERE id_temporada = 4"))
            conn.execute(text("UPDATE temporadas SET descripcion = 'Temporada de sequía (intensa)' WHERE id_temporada = 5"))
            
            # Tipos de plaga
            conn.execute(text("UPDATE tipos_plaga SET descripcion = 'Insecto' WHERE id_tipo_plaga = 1"))
            conn.execute(text("UPDATE tipos_plaga SET descripcion = 'Hongo' WHERE id_tipo_plaga = 2"))
            conn.execute(text("UPDATE tipos_plaga SET descripcion = 'Bacteria' WHERE id_tipo_plaga = 3"))
            conn.execute(text("UPDATE tipos_plaga SET descripcion = 'Virus' WHERE id_tipo_plaga = 4"))
            conn.execute(text("UPDATE tipos_plaga SET descripcion = 'Maleza' WHERE id_tipo_plaga = 5"))
            
            # Actualizar las plagas existentes
            print("\nActualizando plagas existentes...")
            
            # Gusano cogollero: Insecto, Cosecha, Sequía
            conn.execute(text("""
                UPDATE plagas SET 
                    id_fase_cultivo = 5, 
                    id_temporada = 2, 
                    id_tipo_plaga = 1,
                    descripcion = 'El gusano cogollero es una plaga que afecta principalmente durante la fase de cosecha. Las larvas se alimentan de los granos y pueden causar daños significativos al rendimiento del cultivo. Es más común durante la temporada de sequía.',
                    recomendaciones = 'Control biológico con enemigos naturales, aplicación de insecticidas específicos en etapas tempranas, rotación de cultivos. Monitorear cuidadosamente durante la fase de cosecha y en temporadas de sequía.'
                WHERE nombre = 'Gusano cogollero'
            """))
            
            # Gusano elotero: Insecto, Cosecha, Sequía
            conn.execute(text("""
                UPDATE plagas SET 
                    id_fase_cultivo = 5, 
                    id_temporada = 2, 
                    id_tipo_plaga = 1,
                    descripcion = 'El gusano elotero ataca principalmente durante la fase de cosecha. Las larvas penetran en la mazorca y se alimentan de los granos, causando daños directos y facilitando la entrada de patógenos. Es más prevalente en temporadas de sequía.',
                    recomendaciones = 'Aplicación de insecticidas durante la emisión de estigmas, uso de variedades resistentes, eliminación de residuos de cosecha. Implementar un programa de monitoreo durante la fase de cosecha y en temporadas de sequía.'
                WHERE nombre = 'Gusano elotero'
            """))
            
            # Araña roja: Insecto, Maduración, Sequía
            conn.execute(text("""
                UPDATE plagas SET 
                    id_fase_cultivo = 4, 
                    id_temporada = 2, 
                    id_tipo_plaga = 1,
                    descripcion = 'La araña roja es un ácaro que se alimenta de la savia de las hojas durante la fase de maduración, causando manchas amarillentas y decoloración. En infestaciones severas, puede causar la muerte de las plantas. Es especialmente problemática durante temporadas de sequía.',
                    recomendaciones = 'Mantener la humedad adecuada, aplicación de acaricidas específicos, control biológico con ácaros depredadores. Aumentar el riego durante temporadas de sequía para reducir la incidencia de esta plaga.'
                WHERE nombre = 'Araña roja'
            """))
            
            # Roya del maíz: Hongo, Maduración, Lluvia
            conn.execute(text("""
                UPDATE plagas SET 
                    id_fase_cultivo = 4, 
                    id_temporada = 1, 
                    id_tipo_plaga = 2,
                    descripcion = 'La roya del maíz es una enfermedad fúngica que produce pústulas de color marrón rojizo en las hojas durante la fase de maduración. Reduce la capacidad fotosintética y puede causar pérdidas significativas en el rendimiento. Es más común durante la temporada de lluvia debido a la alta humedad.',
                    recomendaciones = 'Uso de variedades resistentes, aplicación de fungicidas, rotación de cultivos, eliminación de residuos infectados. Implementar un buen drenaje en el campo durante temporadas de lluvia para reducir la humedad.'
                WHERE nombre = 'Roya del maíz'
            """))
            
            # Tizón foliar: Hongo, Maduración, Lluvia
            conn.execute(text("""
                UPDATE plagas SET 
                    id_fase_cultivo = 4, 
                    id_temporada = 1, 
                    id_tipo_plaga = 2,
                    descripcion = 'El tizón foliar causa lesiones alargadas de color gris o marrón en las hojas durante la fase de maduración. En condiciones favorables, puede propagarse rápidamente y causar la muerte prematura de las hojas. Es especialmente problemático durante la temporada de lluvia debido a la alta humedad.',
                    recomendaciones = 'Aplicación de fungicidas, uso de variedades resistentes, rotación de cultivos, manejo adecuado de residuos. Mejorar la ventilación entre plantas y asegurar un buen drenaje durante temporadas de lluvia.'
                WHERE nombre = 'Tizón foliar'
            """))
            
            # Confirmar cambios
            conn.commit()
            
            # Mostrar categorías actualizadas
            print("\nCategorías actualizadas:")
            
            print("\nFASES DE CULTIVO:")
            result = conn.execute(text("SELECT * FROM fases_cultivo"))
            fases = result.fetchall()
            for fase in fases:
                print(f"ID: {fase[0]}, Descripción: {fase[1]}")
            
            print("\nTEMPORADAS:")
            result = conn.execute(text("SELECT * FROM temporadas"))
            temporadas = result.fetchall()
            for temporada in temporadas:
                print(f"ID: {temporada[0]}, Descripción: {temporada[1]}")
            
            print("\nTIPOS DE PLAGA:")
            result = conn.execute(text("SELECT * FROM tipos_plaga"))
            tipos = result.fetchall()
            for tipo in tipos:
                print(f"ID: {tipo[0]}, Descripción: {tipo[1]}")
            
            # Mostrar plagas actualizadas
            print("\nPLAGAS ACTUALIZADAS:")
            result = conn.execute(text("""
                SELECT p.id_plaga, p.nombre, fc.descripcion as fase, t.descripcion as temporada, tp.descripcion as tipo
                FROM plagas p
                LEFT JOIN fases_cultivo fc ON p.id_fase_cultivo = fc.id_fase_cultivo
                LEFT JOIN temporadas t ON p.id_temporada = t.id_temporada
                LEFT JOIN tipos_plaga tp ON p.id_tipo_plaga = tp.id_tipo_plaga
                ORDER BY p.id_plaga
            """))
            plagas = result.fetchall()
            for plaga in plagas:
                print(f"ID: {plaga[0]}, Nombre: {plaga[1]}, Fase: {plaga[2]}, Temporada: {plaga[3]}, Tipo: {plaga[4]}")
            
    except Exception as e:
        print(f"Error al actualizar categorías: {e}")

if __name__ == "__main__":
    actualizar_categorias()
