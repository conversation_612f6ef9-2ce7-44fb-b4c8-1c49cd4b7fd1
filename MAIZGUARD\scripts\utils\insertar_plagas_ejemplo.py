#!/usr/bin/env python3
"""
Script para insertar plagas de ejemplo en la base de datos MaizGuard
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from app.database.config import engine
from sqlalchemy import text

def insertar_plagas_ejemplo():
    """Insertar plagas de ejemplo en la base de datos"""
    try:
        with engine.connect() as conn:
            # Verificar si ya existen plagas
            result = conn.execute(text("SELECT COUNT(*) as count FROM plagas"))
            count = result.fetchone().count
            
            if count > 0:
                print(f"Ya existen {count} plagas en la base de datos.")
                respuesta = input("¿Desea agregar más plagas de ejemplo? (s/n): ")
                if respuesta.lower() != 's':
                    return
            
            # Obtener el ID de un usuario administrador para asignar las plagas
            result = conn.execute(text("SELECT id_usuarios FROM usuarios WHERE id_rol = 1 LIMIT 1"))
            admin_user = result.fetchone()
            
            if not admin_user:
                # Si no hay admin, crear uno temporal o usar el primer usuario
                result = conn.execute(text("SELECT id_usuarios FROM usuarios LIMIT 1"))
                admin_user = result.fetchone()
                
                if not admin_user:
                    print("No hay usuarios en la base de datos. Creando usuario administrador...")
                    # Crear usuario administrador temporal
                    import bcrypt
                    password = "admin123"
                    hashed_password = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
                    
                    conn.execute(text("""
                        INSERT INTO usuarios (nombre, apellido, correo_electronico, contrasena, id_estados, id_rol)
                        VALUES ('Admin', 'Sistema', '<EMAIL>', :password, 1, 1)
                    """), {"password": hashed_password})
                    
                    result = conn.execute(text("SELECT LAST_INSERT_ID() as id"))
                    admin_user_id = result.fetchone().id
                else:
                    admin_user_id = admin_user.id_usuarios
            else:
                admin_user_id = admin_user.id_usuarios
            
            # Plagas de ejemplo para maíz
            plagas_ejemplo = [
                {
                    "nombre": "Gusano Cogollero",
                    "nombre_cientifico": "Spodoptera frugiperda",
                    "url_img": "/static/images/gusano-cogollero.jpg",
                    "descripcion": "El gusano cogollero es una de las plagas más destructivas del maíz. Las larvas se alimentan del cogollo de la planta, causando daños severos que pueden reducir significativamente el rendimiento del cultivo.",
                    "recomendaciones": "Control biológico con Trichogramma pretiosum, aplicación de Bacillus thuringiensis, uso de feromonas para monitoreo, rotación de cultivos y eliminación de residuos vegetales.",
                    "id_fase_cultivo": 4,  # Maduración
                    "id_tipo_plaga": 1,    # Insecto
                    "id_temporada": 1      # Seca
                },
                {
                    "nombre": "Gallina Ciega",
                    "nombre_cientifico": "Phyllophaga spp.",
                    "url_img": "/static/images/gallina-ciega.jpg",
                    "descripcion": "Las larvas de gallina ciega atacan las raíces del maíz, debilitando la planta y reduciendo su capacidad de absorción de nutrientes y agua, lo que puede causar marchitez y muerte de la planta.",
                    "recomendaciones": "Preparación adecuada del suelo, uso de nematodos entomopatógenos, aplicación de insecticidas granulados al suelo, rotación con leguminosas y control de malezas hospederas.",
                    "id_fase_cultivo": 5,  # Cosecha
                    "id_tipo_plaga": 1,    # Insecto
                    "id_temporada": 2      # Lluviosa
                },
                {
                    "nombre": "Gusano Elotero",
                    "nombre_cientifico": "Helicoverpa zea",
                    "url_img": "/static/images/gusano-elotero.jpg",
                    "descripcion": "El gusano elotero ataca directamente la mazorca del maíz, perforando los granos y facilitando la entrada de hongos y bacterias que pueden causar pudrición.",
                    "recomendaciones": "Monitoreo con trampas de feromonas, aplicación de Bacillus thuringiensis, liberación de enemigos naturales como Trichogramma, cosecha oportuna y destrucción de residuos.",
                    "id_fase_cultivo": 5,  # Cosecha
                    "id_tipo_plaga": 1,    # Insecto
                    "id_temporada": 1      # Seca
                },
                {
                    "nombre": "Pulgón del Maíz",
                    "nombre_cientifico": "Rhopalosiphum maidis",
                    "url_img": "/static/images/pulgones.jpg",
                    "descripcion": "Los pulgones son insectos chupadores que extraen la savia de la planta, debilitándola y transmitiendo virus que pueden causar enfermedades graves en el cultivo.",
                    "recomendaciones": "Control biológico con mariquitas y crisopas, aplicación de jabón potásico, uso de aceites minerales, eliminación de malezas hospederas y monitoreo constante.",
                    "id_fase_cultivo": 4,  # Maduración
                    "id_tipo_plaga": 1,    # Insecto
                    "id_temporada": 2      # Lluviosa
                },
                {
                    "nombre": "Trips",
                    "nombre_cientifico": "Frankliniella williamsi",
                    "url_img": "/static/images/plaga-trips.jpg",
                    "descripcion": "Los trips son insectos diminutos que raspan la superficie de las hojas, causando manchas plateadas y reduciendo la capacidad fotosintética de la planta.",
                    "recomendaciones": "Uso de trampas adhesivas azules, aplicación de insecticidas sistémicos, control de malezas, manejo de la humedad y uso de variedades resistentes.",
                    "id_fase_cultivo": 4,  # Maduración
                    "id_tipo_plaga": 1,    # Insecto
                    "id_temporada": 1      # Seca
                },
                {
                    "nombre": "Araña Roja",
                    "nombre_cientifico": "Tetranychus urticae",
                    "url_img": "/static/images/araña-roja.jpg",
                    "descripcion": "La araña roja es un ácaro que succiona el contenido celular de las hojas, causando amarillamiento, bronceado y eventual muerte del tejido foliar.",
                    "recomendaciones": "Mantenimiento de humedad adecuada, uso de ácaros depredadores, aplicación de aceites minerales, eliminación de plantas hospederas y rotación de acaricidas.",
                    "id_fase_cultivo": 4,  # Maduración
                    "id_tipo_plaga": 1,    # Insecto (ácaro)
                    "id_temporada": 1      # Seca
                }
            ]
            
            # Insertar las plagas
            for plaga in plagas_ejemplo:
                # Verificar si la plaga ya existe
                result = conn.execute(text("""
                    SELECT COUNT(*) as count FROM plagas 
                    WHERE nombre = :nombre OR nombre_cientifico = :nombre_cientifico
                """), {
                    "nombre": plaga["nombre"],
                    "nombre_cientifico": plaga["nombre_cientifico"]
                })
                
                if result.fetchone().count == 0:
                    conn.execute(text("""
                        INSERT INTO plagas (
                            nombre, nombre_cientifico, url_img, descripcion, recomendaciones,
                            id_fase_cultivo, id_tipo_plaga, id_temporada, id_usuarios
                        ) VALUES (
                            :nombre, :nombre_cientifico, :url_img, :descripcion, :recomendaciones,
                            :id_fase_cultivo, :id_tipo_plaga, :id_temporada, :id_usuarios
                        )
                    """), {
                        **plaga,
                        "id_usuarios": admin_user_id
                    })
                    print(f"✅ Plaga '{plaga['nombre']}' insertada exitosamente.")
                else:
                    print(f"⚠️  La plaga '{plaga['nombre']}' ya existe en la base de datos.")
            
            conn.commit()
            print("\n🎉 Proceso completado exitosamente!")
            
            # Mostrar resumen
            result = conn.execute(text("SELECT COUNT(*) as count FROM plagas"))
            total_plagas = result.fetchone().count
            print(f"Total de plagas en la base de datos: {total_plagas}")
            
    except Exception as e:
        print(f"❌ Error al insertar plagas: {e}")

if __name__ == "__main__":
    print("🌽 Insertando plagas de ejemplo en MaizGuard...")
    insertar_plagas_ejemplo()
