<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cambiar Contraseña - MAIZGUARD</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <!-- Estilos personalizados -->
    <link rel="stylesheet" href="/static/css/main.css">
    <link rel="stylesheet" href="/static/css/compact.css">
    <link rel="stylesheet" href="/static/css/fixed-navbar.css">
    <link rel="stylesheet" href="/static/css/super-responsive.css">
    <link rel="stylesheet" href="/static/css/small-laptop.css">
    <link rel="preload" href="/static/images/Logo.jpeg" as="image">
</head>
<body class="d-flex flex-column min-vh-100">
    <!-- Incluir el navbar -->
    {% include "partials/navbar.html" %}

    <!-- Espacio de separación después del navbar -->
    <div class="container-fluid py-3">
        <!-- Este div crea un espacio entre el navbar y el contenido -->
    </div>

    <!-- Contenido principal -->
    <main class="container my-4">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card shadow">
                    <div class="card-header bg-success text-white">
                        <h3 class="mb-0"><i class="fas fa-key"></i> Cambiar contraseña</h3>
                    </div>
                    <div class="card-body">
                        {% if error %}
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            {{ error }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                        {% endif %}
                        {% if success %}
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            {{ success }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                        {% endif %}

                        <form action="/perfil/cambiar-password" method="post">
                            <div class="mb-3">
                                <label for="inputCurrentPassword" class="form-label">Contraseña actual</label>
                                <input type="password" class="form-control" id="inputCurrentPassword" name="current_password" required>
                            </div>

                            <div class="mb-3">
                                <label for="inputNewPassword" class="form-label">Nueva contraseña</label>
                                <input type="password" class="form-control" id="inputNewPassword" name="new_password" required minlength="8">
                                <div class="form-text">La contraseña debe tener al menos 8 caracteres.</div>
                            </div>

                            <div class="mb-4">
                                <label for="inputConfirmPassword" class="form-label">Confirmar nueva contraseña</label>
                                <input type="password" class="form-control" id="inputConfirmPassword" name="confirm_password" required>
                            </div>

                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a href="/perfil" class="btn btn-outline-secondary">Cancelar</a>
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-save"></i> Cambiar contraseña
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Incluir el footer -->
    {% include "partials/footer.html" %}

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js" defer></script>
    <!-- Scripts personalizados -->
    <script src="/static/js/scripts.js" defer></script>
    <script src="/static/js/script.js" defer></script>


</body>
</html>
