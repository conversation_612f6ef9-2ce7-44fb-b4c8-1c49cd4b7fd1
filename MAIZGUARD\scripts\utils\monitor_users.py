from app.database.config import engine
from sqlalchemy import text
import time

def monitor_users():
    """
    Monitorea la tabla de usuarios y muestra los nuevos registros.
    """
    print("Monitoreando la tabla de usuarios. Presiona Ctrl+C para detener.")
    print("Esperando nuevos registros...\n")
    
    last_count = 0
    
    try:
        while True:
            try:
                with engine.connect() as conn:
                    # Contar usuarios
                    result = conn.execute(text('SELECT COUNT(*) FROM usuarios'))
                    count = result.scalar()
                    
                    # Si hay nuevos usuarios, mostrarlos
                    if count > last_count:
                        print(f"¡Se han detectado {count - last_count} nuevos usuarios!")
                        
                        # Mostrar los usuarios
                        result = conn.execute(text('SELECT id_usuarios, nombre, apellido, correo_electronico, fecha_registro FROM usuarios ORDER BY id_usuarios DESC LIMIT 5'))
                        users = result.fetchall()
                        
                        print("\nÚltimos usuarios registrados:")
                        for user in users:
                            print(f"ID: {user[0]}, Nombre: {user[1]} {user[2]}, Email: {user[3]}, Fecha: {user[4]}")
                        
                        print("\nEsperando nuevos registros...\n")
                        
                        last_count = count
            except Exception as e:
                print(f"Error al conectar con la base de datos: {e}")
                time.sleep(5)
                continue
                
            # Esperar 2 segundos antes de la siguiente verificación
            time.sleep(2)
    except KeyboardInterrupt:
        print("\nMonitoreo detenido.")

if __name__ == "__main__":
    monitor_users()
