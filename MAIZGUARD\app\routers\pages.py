from fastapi import APIRouter, Request, Cookie, Query, Depends, Form, File, UploadFile, HTTPException, Path, status
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse, RedirectResponse, JSONResponse
from fastapi.staticfiles import StaticFiles
from typing import Optional, List, Dict, Any
from sqlalchemy.orm import Session
from datetime import datetime
import os
import shutil
import bcrypt
from sqlalchemy import text

from app.database.config import get_db
from app.database.models import User, HistorialBusqueda

router = APIRouter(
    prefix="",
    tags=["pages"],
    responses={404: {"description": "Not found"}},
)

templates = Jinja2Templates(directory="app/templates")

@router.get("/", response_class=HTMLResponse)
async def root(
    request: Request,
    user_id: Optional[str] = Cookie(None),
    user_role: Optional[str] = <PERSON><PERSON>(None),
    db: Session = Depends(get_db)
):
    # Obtener información del usuario si está autenticado
    user = None
    if user_id:
        user = db.query(User).filter(User.id_usuarios == int(user_id)).first()
        # Agregar el usuario al estado de la request para el navbar
        request.state.user = user

    return templates.TemplateResponse("index.html", {
        "request": request,
        "user_id": user_id,
        "user_role": user_role,
        "user": user
    })

@router.get("/acerca-de", response_class=HTMLResponse)
async def acerca_de(
    request: Request,
    user_id: Optional[str] = Cookie(None),
    user_role: Optional[str] = Cookie(None)
):
    return templates.TemplateResponse("acerca_de.html", {
        "request": request,
        "user_id": user_id,
        "user_role": user_role
    })

@router.get("/terminos-condiciones", response_class=HTMLResponse)
async def terminos_condiciones(
    request: Request,
    user_id: Optional[str] = Cookie(None),
    user_role: Optional[str] = Cookie(None)
):
    return templates.TemplateResponse("terminos_condiciones.html", {
        "request": request,
        "user_id": user_id,
        "user_role": user_role
    })

@router.get("/cultivos", response_class=HTMLResponse)
async def cultivos(
    request: Request,
    user_id: Optional[str] = Cookie(None),
    user_role: Optional[str] = Cookie(None),
    db: Session = Depends(get_db)
):
    # Verificar si el usuario está autenticado
    if not user_id:
        return templates.TemplateResponse("login_required.html", {
            "request": request,
            "mensaje": "Debes iniciar sesión para acceder a esta página",
            "redireccion": "/cultivos"
        })

    try:
        # Obtener información del usuario
        user_query = text("""
            SELECT id_usuarios, nombre, apellido, correo_electronico, fotografia
            FROM usuarios
            WHERE id_usuarios = :user_id
        """)
        result = db.execute(user_query, {"user_id": user_id})
        user = result.fetchone()

        if not user:
            return templates.TemplateResponse("error.html", {
                "request": request,
                "error": "Usuario no encontrado",
                "mensaje": "No se pudo encontrar la información del usuario."
            })

        # Agregar el usuario al estado de la request para el navbar
        request.state.user = user

        # Obtener cultivos del usuario con la nueva estructura
        cultivos_query = text("""
            SELECT
                id_cultivo, nombre, descripcion, cantidad, unidad_cantidad,
                ancho, altura, fase_cultivo, fecha_siembra, fecha_estimada_cosecha,
                ubicacion, notas, fecha_registro, fecha_actualizacion, activo
            FROM cultivos
            WHERE id_usuarios = :user_id AND activo = 1
            ORDER BY fecha_registro DESC
        """)
        result = db.execute(cultivos_query, {"user_id": user_id})
        cultivos = []

        for row in result:
            cultivo = dict(row._mapping)
            if 'fecha_registro' in cultivo and cultivo['fecha_registro']:
                cultivo['fecha_registro'] = cultivo['fecha_registro'].isoformat()
            if 'fecha_siembra' in cultivo and cultivo['fecha_siembra']:
                cultivo['fecha_siembra'] = cultivo['fecha_siembra'].isoformat()
            if 'fecha_estimada_cosecha' in cultivo and cultivo['fecha_estimada_cosecha']:
                cultivo['fecha_estimada_cosecha'] = cultivo['fecha_estimada_cosecha'].isoformat()
            cultivos.append(cultivo)

        # Obtener plagas para el selector
        plagas_query = text("""
            SELECT
                p.id_plaga, p.nombre, tp.nombre as tipo, p.descripcion,
                CASE
                    WHEN fc.descripcion IS NOT NULL THEN fc.descripcion
                    WHEN p.id_fase_cultivo = 4 THEN 'maduración'
                    WHEN p.id_fase_cultivo = 5 THEN 'cosecha'
                    ELSE 'N/A'
                END as fase_cultivo,
                t.nombre as estacion
            FROM plagas p
            LEFT JOIN tipo_plaga tp ON p.id_tipo_plaga = tp.id_tipo_plaga
            LEFT JOIN fase_cultivo fc ON p.id_fase_cultivo = fc.id_fase_cultivo
            LEFT JOIN temporada t ON p.id_temporada = t.id_temporada
            WHERE p.id_fase_cultivo IN (4, 5)
            ORDER BY p.nombre ASC
        """)

        plagas_result = db.execute(plagas_query)
        plagas = []
        for row in plagas_result:
            plaga = dict(row._mapping)
            plagas.append(plaga)

        return templates.TemplateResponse("cultivos.html", {
            "request": request,
            "user": user,
            "user_id": user_id,
            "user_role": user_role,
            "cultivos": cultivos,
            "plagas": plagas
        })

    except Exception as e:
        print(f"Error en cultivos: {e}")
        return templates.TemplateResponse("error.html", {
            "request": request,
            "error": "Error interno del servidor",
            "mensaje": "Ocurrió un error al cargar la página de cultivos."
        })

@router.post("/cultivos/editar/{cultivo_id}")
async def editar_cultivo(
    cultivo_id: int,
    request: Request,
    nombre: str = Form(...),
    descripcion: str = Form(...),
    cantidad: int = Form(...),
    unidad_cantidad: str = Form(...),
    ancho: float = Form(...),
    altura: float = Form(...),
    fase_cultivo: str = Form(...),
    ubicacion: str = Form(""),
    fecha_siembra: str = Form(""),
    fecha_estimada_cosecha: str = Form(""),
    notas: str = Form(""),
    db: Session = Depends(get_db)
):
    # Verificar autenticación
    user_id = request.cookies.get("user_id")
    if not user_id:
        return RedirectResponse(url="/login", status_code=status.HTTP_303_SEE_OTHER)

    try:
        # Verificar que el cultivo pertenece al usuario
        cultivo_query = text("""
            SELECT id_cultivo FROM cultivos
            WHERE id_cultivo = :cultivo_id AND id_usuarios = :user_id AND activo = 1
        """)
        result = db.execute(cultivo_query, {"cultivo_id": cultivo_id, "user_id": user_id})
        if not result.fetchone():
            return RedirectResponse(url="/cultivos", status_code=status.HTTP_303_SEE_OTHER)

        # Actualizar cultivo
        update_query = text("""
            UPDATE cultivos SET
                nombre = :nombre,
                descripcion = :descripcion,
                cantidad = :cantidad,
                unidad_cantidad = :unidad_cantidad,
                ancho = :ancho,
                altura = :altura,
                fase_cultivo = :fase_cultivo,
                ubicacion = :ubicacion,
                fecha_siembra = :fecha_siembra,
                fecha_estimada_cosecha = :fecha_estimada_cosecha,
                notas = :notas,
                fecha_actualizacion = NOW()
            WHERE id_cultivo = :cultivo_id AND id_usuarios = :user_id
        """)

        db.execute(update_query, {
            "cultivo_id": cultivo_id,
            "user_id": user_id,
            "nombre": nombre,
            "descripcion": descripcion,
            "cantidad": cantidad,
            "unidad_cantidad": unidad_cantidad,
            "ancho": ancho,
            "altura": altura,
            "fase_cultivo": fase_cultivo,
            "ubicacion": ubicacion if ubicacion else None,
            "fecha_siembra": fecha_siembra if fecha_siembra else None,
            "fecha_estimada_cosecha": fecha_estimada_cosecha if fecha_estimada_cosecha else None,
            "notas": notas if notas else None
        })
        db.commit()

        return RedirectResponse(url="/cultivos", status_code=status.HTTP_303_SEE_OTHER)

    except Exception as e:
        db.rollback()
        print(f"Error al editar cultivo: {e}")
        return RedirectResponse(url="/cultivos", status_code=status.HTTP_303_SEE_OTHER)

@router.delete("/cultivos/eliminar/{cultivo_id}")
async def eliminar_cultivo(
    cultivo_id: int,
    request: Request,
    db: Session = Depends(get_db)
):
    # Verificar autenticación
    user_id = request.cookies.get("user_id")
    if not user_id:
        return JSONResponse(content={"error": "No autorizado"}, status_code=401)

    try:
        # Verificar que el cultivo pertenece al usuario
        cultivo_query = text("""
            SELECT id_cultivo FROM cultivos
            WHERE id_cultivo = :cultivo_id AND id_usuarios = :user_id AND activo = 1
        """)
        result = db.execute(cultivo_query, {"cultivo_id": cultivo_id, "user_id": user_id})
        if not result.fetchone():
            return JSONResponse(content={"error": "Cultivo no encontrado"}, status_code=404)

        # Marcar como inactivo (eliminación lógica)
        delete_query = text("""
            UPDATE cultivos SET
                activo = 0,
                fecha_actualizacion = NOW()
            WHERE id_cultivo = :cultivo_id AND id_usuarios = :user_id
        """)

        db.execute(delete_query, {"cultivo_id": cultivo_id, "user_id": user_id})
        db.commit()

        return JSONResponse(content={"success": True})

    except Exception as e:
        db.rollback()
        print(f"Error al eliminar cultivo: {e}")
        return JSONResponse(content={"error": "Error interno del servidor"}, status_code=500)

    except Exception as e:
        return templates.TemplateResponse("error.html", {
            "request": request,
            "error": "Error al cargar la página de cultivos",
            "mensaje": f"Ocurrió un error: {str(e)}"
        })

@router.post("/cultivos/crear", response_class=HTMLResponse)
async def crear_cultivo(
    request: Request,
    nombre: str = Form(...),
    descripcion: str = Form(...),
    cantidad: int = Form(...),
    unidad_cantidad: str = Form("plantas"),
    ancho: float = Form(...),
    altura: float = Form(...),
    fase_cultivo: str = Form(...),
    fecha_siembra: str = Form(None),
    fecha_estimada_cosecha: str = Form(None),
    ubicacion: str = Form(None),
    notas: str = Form(None),
    user_id: Optional[str] = Cookie(None),
    user_role: Optional[str] = Cookie(None),
    db: Session = Depends(get_db)
):
    # Verificar si el usuario está autenticado
    if not user_id:
        return RedirectResponse(url="/login", status_code=303)

    # Validar que la fase del cultivo sea válida (solo maduración o cosecha)
    fases_validas = ["maduracion", "cosecha"]
    if fase_cultivo not in fases_validas:
        return RedirectResponse(url="/cultivos?error=fase_invalida", status_code=303)

    try:
        # Insertar nuevo cultivo
        query = text("""
            INSERT INTO cultivos (
                nombre, descripcion, cantidad, unidad_cantidad, ancho, altura,
                fase_cultivo, fecha_siembra, fecha_estimada_cosecha, ubicacion,
                notas, id_usuarios
            ) VALUES (
                :nombre, :descripcion, :cantidad, :unidad_cantidad, :ancho, :altura,
                :fase_cultivo, :fecha_siembra, :fecha_estimada_cosecha, :ubicacion,
                :notas, :id_usuarios
            )
        """)

        db.execute(query, {
            "nombre": nombre,
            "descripcion": descripcion,
            "cantidad": cantidad,
            "unidad_cantidad": unidad_cantidad,
            "ancho": ancho,
            "altura": altura,
            "fase_cultivo": fase_cultivo,
            "fecha_siembra": fecha_siembra if fecha_siembra else None,
            "fecha_estimada_cosecha": fecha_estimada_cosecha if fecha_estimada_cosecha else None,
            "ubicacion": ubicacion if ubicacion else None,
            "notas": notas if notas else None,
            "id_usuarios": int(user_id)
        })

        db.commit()

        # Redirigir a la página de cultivos con mensaje de éxito
        return RedirectResponse(url="/cultivos?success=1", status_code=303)

    except Exception as e:
        db.rollback()
        # Redirigir a la página de cultivos con mensaje de error
        return RedirectResponse(url="/cultivos?error=1", status_code=303)

@router.get("/plagas", response_class=HTMLResponse)
async def plagas(
    request: Request,
    fase: Optional[int] = Query(None, description="Filtrar por fase de cultivo"),
    temporada: Optional[int] = Query(None, description="Filtrar por temporada"),
    tipo: Optional[int] = Query(None, description="Filtrar por tipo de plaga"),
    user_id: Optional[str] = Cookie(None),
    user_role: Optional[str] = Cookie(None),
    db: Session = Depends(get_db)
):
    # Verificar si el usuario está autenticado
    if not user_id:
        # Redirigir a la página de consulta pública de plagas
        return RedirectResponse(url="/consulta-plagas", status_code=303)

    # Obtener categorías para los filtros
    fases_cultivo = []
    temporadas = []
    tipos_plaga = []
    plagas = []

    try:
        # Obtener fases de cultivo
        result = db.execute(text("SELECT id_fase_cultivo, descripcion FROM fases_cultivo ORDER BY id_fase_cultivo"))
        fases_cultivo = [dict(row._mapping) for row in result]

        # Obtener temporadas
        result = db.execute(text("SELECT id_temporada, descripcion FROM temporadas ORDER BY id_temporada"))
        temporadas = [dict(row._mapping) for row in result]

        # Obtener tipos de plaga
        result = db.execute(text("SELECT id_tipo_plaga, descripcion FROM tipos_plaga ORDER BY id_tipo_plaga"))
        tipos_plaga = [dict(row._mapping) for row in result]

        # Construir la consulta SQL base
        query_str = """
            SELECT p.id_plaga, p.nombre, p.nombre_cientifico, p.url_img, p.descripcion,
                   fc.descripcion as fase_cultivo, t.descripcion as temporada, tp.descripcion as tipo_plaga
            FROM plagas p
            LEFT JOIN fases_cultivo fc ON p.id_fase_cultivo = fc.id_fase_cultivo
            LEFT JOIN temporadas t ON p.id_temporada = t.id_temporada
            LEFT JOIN tipos_plaga tp ON p.id_tipo_plaga = tp.id_tipo_plaga
            WHERE 1=1
        """

        # Agregar filtros si se proporcionan
        params = {}
        if fase:
            query_str += " AND p.id_fase_cultivo = :fase"
            params["fase"] = fase

        if temporada:
            query_str += " AND p.id_temporada = :temporada"
            params["temporada"] = temporada

        if tipo:
            query_str += " AND p.id_tipo_plaga = :tipo"
            params["tipo"] = tipo

        # Ordenar resultados
        query_str += " ORDER BY p.nombre"

        # Ejecutar la consulta
        result = db.execute(text(query_str), params)
        plagas = [dict(row._mapping) for row in result]

        # Formatear descripciones para mostrar solo un extracto
        for plaga in plagas:
            if len(plaga["descripcion"]) > 150:
                plaga["descripcion_corta"] = plaga["descripcion"][:150] + "..."
            else:
                plaga["descripcion_corta"] = plaga["descripcion"]

    except Exception as e:
        print(f"Error al obtener datos para la página de plagas: {e}")

    return templates.TemplateResponse("plagas.html", {
        "request": request,
        "user_id": user_id,
        "user_role": user_role,
        "fases_cultivo": fases_cultivo,
        "temporadas": temporadas,
        "tipos_plaga": tipos_plaga,
        "plagas": plagas,
        "filtro_fase": fase,
        "filtro_temporada": temporada,
        "filtro_tipo": tipo
    })

@router.get("/buscar", response_class=HTMLResponse)
async def buscar(
    request: Request,
    q: str = Query(...),
    user_id: Optional[str] = Cookie(None),
    user_role: Optional[str] = Cookie(None),
    db: Session = Depends(get_db)
):
    """
    Función de búsqueda accesible para todos los usuarios,
    tanto los que están logueados como los que no.
    """
    # Imprimir información de depuración
    print(f"Búsqueda: '{q}', Usuario: {user_id}, Rol: {user_role}")
    # Verificar si la base de datos está disponible
    if db is None:
        return templates.TemplateResponse("busqueda.html", {
            "request": request,
            "query": q,
            "resultados": [],
            "error": "El servicio de base de datos no está disponible en este momento.",
            "user_id": user_id,
            "user_role": user_role
        })

    # Buscar plagas que coincidan con el término de búsqueda
    resultados = []
    try:
        # Consulta SQL optimizada para buscar plagas por nombre o nombre científico
        # Limitamos a solo los campos necesarios para mejorar el rendimiento
        query = text("""
            SELECT id_plaga, nombre, nombre_cientifico, url_img,
                   LEFT(descripcion, 200) as descripcion_corta
            FROM plagas
            WHERE nombre LIKE :search OR nombre_cientifico LIKE :search
            ORDER BY nombre
            LIMIT 10
        """)

        search_term = f"%{q}%"
        result = db.execute(query, {"search": search_term})
        plagas = [dict(row._mapping) for row in result]

        # Formatear los resultados para la plantilla
        for plaga in plagas:
            # Añadir puntos suspensivos si la descripción es truncada
            descripcion = plaga["descripcion_corta"]
            if len(descripcion) >= 200:
                descripcion = descripcion + "..."

            # Asignar imágenes locales basadas en el nombre de la plaga
            imagen_url = plaga["url_img"]
            nombre_plaga = plaga["nombre"].lower()

            # Mapear nombres de plagas a archivos de imagen existentes
            if "gusano cogollero" in nombre_plaga:
                imagen_url = "/static/images/gusano-cogollero.jpg"
            elif "gusano elotero" in nombre_plaga:
                imagen_url = "/static/images/gusano-elotero.jpg"
            elif "araña roja" in nombre_plaga:
                imagen_url = "/static/images/araña-roja.jpg"
            elif "gallina ciega" in nombre_plaga:
                imagen_url = "/static/images/gallina-ciega.jpg"
            elif "pulgón" in nombre_plaga or "pulgones" in nombre_plaga:
                imagen_url = "/static/images/pulgones.jpg"
            elif "trips" in nombre_plaga:
                imagen_url = "/static/images/plaga-trips.jpg"
            elif not imagen_url or not imagen_url.startswith('/static/'):
                # Si no hay coincidencia, usar una imagen genérica
                imagen_url = "/static/images/cultivo.jpg"

            # Usar URL diferente según si el usuario está logueado o no
            if user_id:
                url_detalle = f"/plaga-publica/{plaga['id_plaga']}"
            else:
                url_detalle = f"/login?redirect=/plaga-publica/{plaga['id_plaga']}"

            resultados.append({
                "id": plaga["id_plaga"],
                "titulo": plaga["nombre"],
                "subtitulo": plaga["nombre_cientifico"],
                "imagen": imagen_url,
                "descripcion": descripcion,
                "url": url_detalle
            })

        # Si el usuario está autenticado, registrar la búsqueda en el historial
        if user_id:
            try:
                # Crear un nuevo registro en la tabla historial_busqueda
                nuevo_historial = HistorialBusqueda(
                    id_usuario=int(user_id),
                    termino_busqueda=q
                )
                db.add(nuevo_historial)
                db.commit()
                print(f"Se registró la búsqueda de '{q}' para el usuario {user_id} en el historial")
            except Exception as e:
                print(f"Error al registrar búsqueda en el historial: {e}")
                db.rollback()  # Hacemos rollback para evitar transacciones incompletas

    except Exception as e:
        print(f"Error al buscar plagas: {e}")

    return templates.TemplateResponse("busqueda.html", {
        "request": request,
        "query": q,
        "resultados": resultados,
        "user_id": user_id,
        "user_role": user_role
    })

@router.get("/perfil", response_class=HTMLResponse)
async def perfil(
    request: Request,
    user_id: Optional[str] = Cookie(None),
    user_role: Optional[str] = Cookie(None),
    db: Session = Depends(get_db)
):
    # Verificar si el usuario está autenticado
    if not user_id:
        return RedirectResponse(url="/login", status_code=303)

    # Obtener información del usuario
    user = db.query(User).filter(User.id_usuarios == int(user_id)).first()
    if not user:
        response = RedirectResponse(url="/logout", status_code=303)
        return response

    # Obtener historial de búsquedas del usuario
    historial_busquedas = []
    try:
        # Imprimir información de depuración
        print(f"Obteniendo historial de búsquedas para el usuario {user_id}")

        # Obtener el historial de búsquedas del usuario
        historial_busquedas = db.query(HistorialBusqueda).filter(
            HistorialBusqueda.id_usuario == int(user_id)
        ).order_by(HistorialBusqueda.fecha_busqueda.desc()).limit(10).all()

        # Imprimir información de depuración
        print(f"Se encontraron {len(historial_busquedas)} búsquedas para el usuario {user_id}")
        for busqueda in historial_busquedas:
            print(f"Búsqueda: {busqueda.termino_busqueda} - {busqueda.fecha_busqueda}")
    except Exception as e:
        print(f"Error al obtener historial de búsquedas: {e}")

    return templates.TemplateResponse("perfil.html", {
        "request": request,
        "user": user,
        "historial_busquedas": historial_busquedas,
        "user_id": user_id,
        "user_role": user_role
    })

@router.get("/perfil/editar", response_class=HTMLResponse)
async def editar_perfil_get(
    request: Request,
    user_id: Optional[str] = Cookie(None),
    user_role: Optional[str] = Cookie(None),
    db: Session = Depends(get_db)
):
    # Verificar si el usuario está autenticado
    if not user_id:
        return RedirectResponse(url="/login", status_code=303)

    # Obtener información del usuario
    user = db.query(User).filter(User.id_usuarios == int(user_id)).first()
    if not user:
        response = RedirectResponse(url="/logout", status_code=303)
        return response

    return templates.TemplateResponse("editar_perfil.html", {
        "request": request,
        "user": user,
        "user_id": user_id,
        "user_role": user_role
    })

@router.post("/perfil/editar", response_class=HTMLResponse)
async def editar_perfil_post(
    request: Request,
    nombre: str = Form(...),
    apellido: str = Form(...),
    email: str = Form(...),
    foto: UploadFile = File(None),
    user_id: Optional[str] = Cookie(None),
    user_role: Optional[str] = Cookie(None),
    db: Session = Depends(get_db)
):
    # Verificar si el usuario está autenticado
    if not user_id:
        return RedirectResponse(url="/login", status_code=303)

    # Obtener información del usuario
    user = db.query(User).filter(User.id_usuarios == int(user_id)).first()
    if not user:
        response = RedirectResponse(url="/logout", status_code=303)
        return response

    try:
        # Actualizar información básica
        user.nombre = nombre
        user.apellido = apellido

        # Verificar si el correo electrónico ya está en uso por otro usuario
        if email != user.correo_electronico:
            existing_user = db.query(User).filter(User.correo_electronico == email, User.id_usuarios != int(user_id)).first()
            if existing_user:
                return templates.TemplateResponse("editar_perfil.html", {
                    "request": request,
                    "user": user,
                    "error": "Este correo electrónico ya está en uso por otro usuario.",
                    "user_id": user_id,
                    "user_role": user_role
                })
            user.correo_electronico = email

        # Procesar la foto si se proporcionó una
        if foto and foto.filename:
            # Crear directorio si no existe
            upload_dir = os.path.join("app", "static", "uploads", "profile_pics")
            os.makedirs(upload_dir, exist_ok=True)

            # Generar nombre de archivo único
            file_extension = os.path.splitext(foto.filename)[1]
            file_name = f"user_{user_id}_{datetime.now().strftime('%Y%m%d%H%M%S')}{file_extension}"
            file_path = os.path.join(upload_dir, file_name)

            # Guardar archivo
            with open(file_path, "wb") as buffer:
                shutil.copyfileobj(foto.file, buffer)

            # Actualizar ruta en la base de datos
            user.fotografia = f"/static/uploads/profile_pics/{file_name}"

        # Guardar cambios
        db.commit()

        return templates.TemplateResponse("editar_perfil.html", {
            "request": request,
            "user": user,
            "success": "Perfil actualizado correctamente.",
            "user_id": user_id,
            "user_role": user_role
        })

    except Exception as e:
        db.rollback()
        return templates.TemplateResponse("editar_perfil.html", {
            "request": request,
            "user": user,
            "error": f"Error al actualizar el perfil: {str(e)}",
            "user_id": user_id,
            "user_role": user_role
        })

@router.get("/consulta-plagas", response_class=HTMLResponse)
async def consulta_plagas(
    request: Request,
    fase: Optional[int] = Query(None, description="Filtrar por fase de cultivo"),
    temporada: Optional[int] = Query(None, description="Filtrar por temporada"),
    user_id: Optional[str] = Cookie(None),
    user_role: Optional[str] = Cookie(None),
    db: Session = Depends(get_db)
):
    """
    Página de consulta de plagas accesible para todos los usuarios,
    tanto los que están logueados como los que no.
    """
    # Obtener categorías para los filtros
    fases_cultivo = []
    temporadas = []
    plagas = []

    try:
        # Obtener solo las fases de cultivo de maduración y cosecha
        result = db.execute(text("""
            SELECT id_fase_cultivo, descripcion
            FROM fases_cultivo
            WHERE id_fase_cultivo IN (4, 5)
            ORDER BY id_fase_cultivo
        """))
        fases_cultivo = [dict(row._mapping) for row in result]

        # Obtener temporadas
        result = db.execute(text("SELECT id_temporada, nombre FROM temporadas ORDER BY id_temporada"))
        temporadas = [dict(row._mapping) for row in result]

        # Construir la consulta SQL base
        query_str = """
            SELECT p.id_plaga, p.nombre, p.nombre_cientifico, p.url_img, p.descripcion,
                   fc.descripcion as fase_cultivo, t.nombre as temporada, tp.nombre as tipo_plaga,
                   p.id_fase_cultivo, p.id_temporada, p.id_tipo_plaga
            FROM plagas p
            LEFT JOIN fases_cultivo fc ON p.id_fase_cultivo = fc.id_fase_cultivo
            LEFT JOIN temporadas t ON p.id_temporada = t.id_temporada
            LEFT JOIN tipos_plaga tp ON p.id_tipo_plaga = tp.id_tipo_plaga
            WHERE p.id_fase_cultivo IN (4, 5)  -- Solo maduración y cosecha
        """

        # Agregar filtros si se proporcionan
        params = {}
        if fase:
            query_str += " AND p.id_fase_cultivo = :fase"
            params["fase"] = fase

        if temporada:
            query_str += " AND p.id_temporada = :temporada"
            params["temporada"] = temporada

        # Ordenar resultados
        query_str += " ORDER BY p.nombre"

        # Ejecutar la consulta
        result = db.execute(text(query_str), params)
        plagas = [dict(row._mapping) for row in result]

        # Formatear descripciones para mostrar solo un extracto
        for plaga in plagas:
            if len(plaga["descripcion"]) > 150:
                plaga["descripcion_corta"] = plaga["descripcion"][:150] + "..."
            else:
                plaga["descripcion_corta"] = plaga["descripcion"]

            # Asignar imágenes locales basadas en el nombre de la plaga
            nombre_plaga = plaga["nombre"].lower()

            # Mapear nombres de plagas a archivos de imagen existentes
            if "gusano cogollero" in nombre_plaga:
                plaga["url_img"] = "/static/images/gusano-cogollero.jpg"
            elif "gusano elotero" in nombre_plaga:
                plaga["url_img"] = "/static/images/gusano-elotero.jpg"
            elif "araña roja" in nombre_plaga:
                plaga["url_img"] = "/static/images/arana-roja.jpg"
            elif "gallina ciega" in nombre_plaga:
                plaga["url_img"] = "/static/images/gallina-ciega.jpg"
            elif "pulgón" in nombre_plaga or "pulgones" in nombre_plaga:
                plaga["url_img"] = "/static/images/pulgones.jpg"
            elif "trips" in nombre_plaga:
                plaga["url_img"] = "/static/images/trips.jpg"
            elif not plaga["url_img"] or not plaga["url_img"].startswith('/static/'):
                # Si no hay coincidencia, usar una imagen genérica
                plaga["url_img"] = "/static/images/cultivo.jpg"

    except Exception as e:
        print(f"Error al obtener datos para la página de consulta de plagas: {e}")

    return templates.TemplateResponse("consulta_plagas.html", {
        "request": request,
        "user_id": user_id,
        "user_role": user_role,
        "fases_cultivo": fases_cultivo,
        "temporadas": temporadas,
        "plagas": plagas,
        "filtro_fase": fase,
        "filtro_temporada": temporada
    })

@router.get("/plaga-publica/{id_plaga}", response_class=HTMLResponse)
async def ver_plaga_publica(
    request: Request,
    id_plaga: int = Path(...),
    user_id: Optional[str] = Cookie(None),
    user_role: Optional[str] = Cookie(None),
    db: Session = Depends(get_db)
):
    """
    Página de detalle de plaga que requiere autenticación para ver los detalles completos.
    """
    # Verificar si el usuario está autenticado
    if not user_id:
        return templates.TemplateResponse("login_required.html", {
            "request": request,
            "message": "Debe estar logueado para ver los detalles de las plagas.",
            "redirect_url": f"/plaga-publica/{id_plaga}",
            "user_id": user_id,
            "user_role": user_role
        })
    # Verificar si la base de datos está disponible
    if db is None:
        return templates.TemplateResponse("error.html", {
            "request": request,
            "error": "El servicio de base de datos no está disponible en este momento.",
            "user_id": user_id,
            "user_role": user_role
        })

    try:
        # Obtener información básica de la plaga
        query = text("""
            SELECT p.id_plaga, p.nombre, p.nombre_cientifico, p.url_img, p.descripcion, p.recomendaciones,
                   p.id_fase_cultivo, p.id_tipo_plaga, p.id_temporada
            FROM plagas p
            WHERE p.id_plaga = :id_plaga
        """)

        result = db.execute(query, {"id_plaga": id_plaga})
        plaga = result.fetchone()

        if not plaga:
            return templates.TemplateResponse("error.html", {
                "request": request,
                "error": "La plaga solicitada no existe.",
                "user_id": user_id,
                "user_role": user_role
            })

        # Convertir el resultado a un diccionario para la plantilla
        plaga_dict = dict(plaga._mapping)

        # Obtener toda la información adicional en una sola consulta
        info_query = text("""
            SELECT
                fc.descripcion as fase_cultivo,
                tp.nombre as tipo_plaga,
                t.nombre as temporada
            FROM fases_cultivo fc, tipos_plaga tp, temporadas t
            WHERE fc.id_fase_cultivo = :id_fase
            AND tp.id_tipo_plaga = :id_tipo
            AND t.id_temporada = :id_temp
        """)

        info_result = db.execute(info_query, {
            "id_fase": plaga_dict["id_fase_cultivo"],
            "id_tipo": plaga_dict["id_tipo_plaga"],
            "id_temp": plaga_dict["id_temporada"]
        })

        info = info_result.fetchone()
        if info:
            info_dict = dict(info._mapping)
            plaga_dict["fase_cultivo"] = info_dict["fase_cultivo"] if info_dict["fase_cultivo"] else "No especificada"
            plaga_dict["tipo_plaga"] = info_dict["tipo_plaga"] if info_dict["tipo_plaga"] else "No especificado"
            plaga_dict["temporada"] = info_dict["temporada"] if info_dict["temporada"] else "No especificada"
        else:
            plaga_dict["fase_cultivo"] = "No especificada"
            plaga_dict["tipo_plaga"] = "No especificado"
            plaga_dict["temporada"] = "No especificada"

        # Asignar imágenes locales basadas en el nombre de la plaga
        nombre_plaga = plaga_dict["nombre"].lower()

        # Mapear nombres de plagas a archivos de imagen existentes
        if "gusano cogollero" in nombre_plaga:
            plaga_dict["url_img"] = "/static/images/gusano-cogollero.jpg"
        elif "gusano elotero" in nombre_plaga:
            plaga_dict["url_img"] = "/static/images/gusano-elotero.jpg"
        elif "araña roja" in nombre_plaga:
            plaga_dict["url_img"] = "/static/images/arana-roja.jpg"
        elif "gallina ciega" in nombre_plaga:
            plaga_dict["url_img"] = "/static/images/gallina-ciega.jpg"
        elif "pulgón" in nombre_plaga or "pulgones" in nombre_plaga:
            plaga_dict["url_img"] = "/static/images/pulgones.jpg"
        elif "trips" in nombre_plaga:
            plaga_dict["url_img"] = "/static/images/trips.jpg"
        elif not plaga_dict["url_img"] or not plaga_dict["url_img"].startswith('/static/'):
            # Si no hay coincidencia, usar una imagen genérica
            plaga_dict["url_img"] = "/static/images/cultivo.jpg"

        # Ya no registramos consultas aquí, solo en la búsqueda desde el navbar

        return templates.TemplateResponse("plaga_detalle_publica.html", {
            "request": request,
            "plaga": plaga_dict,
            "user_id": user_id,
            "user_role": user_role
        })

    except Exception as e:
        print(f"Error al obtener información de la plaga: {e}")
        return templates.TemplateResponse("error.html", {
            "request": request,
            "error": f"Error al obtener información de la plaga: {str(e)}",
            "user_id": user_id,
            "user_role": user_role
        })

@router.get("/perfil/cambiar-password", response_class=HTMLResponse)
async def cambiar_password_get(
    request: Request,
    user_id: Optional[str] = Cookie(None),
    user_role: Optional[str] = Cookie(None),
    db: Session = Depends(get_db)
):
    # Verificar si el usuario está autenticado
    if not user_id:
        return RedirectResponse(url="/login", status_code=303)

    # Obtener información del usuario
    user = db.query(User).filter(User.id_usuarios == int(user_id)).first()
    if not user:
        response = RedirectResponse(url="/logout", status_code=303)
        return response

    return templates.TemplateResponse("cambiar_password.html", {
        "request": request,
        "user_id": user_id,
        "user_role": user_role
    })

@router.post("/perfil/cambiar-password", response_class=HTMLResponse)
async def cambiar_password_post(
    request: Request,
    current_password: str = Form(...),
    new_password: str = Form(...),
    confirm_password: str = Form(...),
    user_id: Optional[str] = Cookie(None),
    user_role: Optional[str] = Cookie(None),
    db: Session = Depends(get_db)
):
    # Verificar si el usuario está autenticado
    if not user_id:
        return RedirectResponse(url="/login", status_code=303)

    # Obtener información del usuario
    user = db.query(User).filter(User.id_usuarios == int(user_id)).first()
    if not user:
        response = RedirectResponse(url="/logout", status_code=303)
        return response

    # Verificar contraseña actual
    if not bcrypt.checkpw(current_password.encode('utf-8'), user.contrasena.encode('utf-8')):
        return templates.TemplateResponse("cambiar_password.html", {
            "request": request,
            "error": "La contraseña actual es incorrecta.",
            "user_id": user_id,
            "user_role": user_role
        })

    # Verificar que las nuevas contraseñas coincidan
    if new_password != confirm_password:
        return templates.TemplateResponse("cambiar_password.html", {
            "request": request,
            "error": "Las nuevas contraseñas no coinciden.",
            "user_id": user_id,
            "user_role": user_role
        })

    # Verificar longitud mínima
    if len(new_password) < 8:
        return templates.TemplateResponse("cambiar_password.html", {
            "request": request,
            "error": "La nueva contraseña debe tener al menos 8 caracteres.",
            "user_id": user_id,
            "user_role": user_role
        })

    try:
        # Hashear y actualizar la contraseña
        hashed_password = bcrypt.hashpw(new_password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
        user.contrasena = hashed_password
        db.commit()

        return templates.TemplateResponse("cambiar_password.html", {
            "request": request,
            "success": "Contraseña actualizada correctamente.",
            "user_id": user_id,
            "user_role": user_role
        })

    except Exception as e:
        db.rollback()
        return templates.TemplateResponse("cambiar_password.html", {
            "request": request,
            "error": f"Error al actualizar la contraseña: {str(e)}",
            "user_id": user_id,
            "user_role": user_role
        })

@router.get("/plaga/{id_plaga}", response_class=HTMLResponse)
async def ver_plaga(
    request: Request,
    id_plaga: int = Path(...),
    user_id: Optional[str] = Cookie(None),
    user_role: Optional[str] = Cookie(None),
    db: Session = Depends(get_db)
):
    # Verificar si el usuario está autenticado
    if not user_id:
        # Redirigir a la versión pública de la página de detalles
        return RedirectResponse(url=f"/plaga-publica/{id_plaga}", status_code=303)

    # Verificar si la base de datos está disponible
    if db is None:
        return templates.TemplateResponse("error.html", {
            "request": request,
            "error": "El servicio de base de datos no está disponible en este momento.",
            "user_id": user_id,
            "user_role": user_role
        })

    try:
        # Obtener información básica de la plaga (consulta más rápida)
        query = text("""
            SELECT p.id_plaga, p.nombre, p.nombre_cientifico, p.url_img, p.descripcion, p.recomendaciones,
                   p.id_fase_cultivo, p.id_tipo_plaga, p.id_temporada
            FROM plaga p
            WHERE p.id_plaga = :id_plaga
        """)

        result = db.execute(query, {"id_plaga": id_plaga})
        plaga = result.fetchone()

        if not plaga:
            return templates.TemplateResponse("error.html", {
                "request": request,
                "error": "La plaga solicitada no existe.",
                "user_id": user_id,
                "user_role": user_role
            })

        # Convertir el resultado a un diccionario para la plantilla
        plaga_dict = dict(plaga._mapping)

        # Obtener toda la información adicional en una sola consulta (más eficiente)
        info_query = text("""
            SELECT
                fc.descripcion as fase_cultivo,
                tp.nombre as tipo_plaga,
                t.nombre as temporada
            FROM fase_cultivo fc, tipo_plaga tp, temporada t
            WHERE fc.id_fase_cultivo = :id_fase
            AND tp.id_tipo_plaga = :id_tipo
            AND t.id_temporada = :id_temp
        """)

        info_result = db.execute(info_query, {
            "id_fase": plaga_dict["id_fase_cultivo"],
            "id_tipo": plaga_dict["id_tipo_plaga"],
            "id_temp": plaga_dict["id_temporada"]
        })

        info = info_result.fetchone()
        if info:
            info_dict = dict(info._mapping)
            plaga_dict["fase_cultivo"] = info_dict["fase_cultivo"] if info_dict["fase_cultivo"] else "No especificada"
            plaga_dict["tipo_plaga"] = info_dict["tipo_plaga"] if info_dict["tipo_plaga"] else "No especificado"
            plaga_dict["temporada"] = info_dict["temporada"] if info_dict["temporada"] else "No especificada"
        else:
            plaga_dict["fase_cultivo"] = "No especificada"
            plaga_dict["tipo_plaga"] = "No especificado"
            plaga_dict["temporada"] = "No especificada"

        # Asignar imágenes locales basadas en el nombre de la plaga
        nombre_plaga = plaga_dict["nombre"].lower()

        # Mapear nombres de plagas a archivos de imagen existentes
        if "gusano cogollero" in nombre_plaga:
            plaga_dict["url_img"] = "/static/images/gusano-cogollero.jpg"
        elif "gusano elotero" in nombre_plaga:
            plaga_dict["url_img"] = "/static/images/gusano-elotero.jpg"
        elif "araña roja" in nombre_plaga:
            plaga_dict["url_img"] = "/static/images/araña-roja.jpg"
        elif "gallina ciega" in nombre_plaga:
            plaga_dict["url_img"] = "/static/images/gallina-ciega.jpg"
        elif "pulgón" in nombre_plaga or "pulgones" in nombre_plaga:
            plaga_dict["url_img"] = "/static/images/pulgones.jpg"
        elif "trips" in nombre_plaga:
            plaga_dict["url_img"] = "/static/images/plaga-trips.jpg"
        elif not plaga_dict["url_img"] or not plaga_dict["url_img"].startswith('/static/'):
            # Si no hay coincidencia, usar una imagen genérica
            plaga_dict["url_img"] = "/static/images/cultivo.jpg"

        # Registrar la consulta en segundo plano (sin esperar a que termine)
        if user_id:
            try:
                # Insertar directamente sin verificar consultas previas (para mayor velocidad)
                # Usamos ON DUPLICATE KEY UPDATE para evitar errores si ya existe
                insert_query = text("""
                    INSERT INTO consultas (id_plaga, id_usuarios, fecha_consulta)
                    VALUES (:id_plaga, :id_usuario, NOW())
                """)

                db.execute(insert_query, {
                    "id_plaga": id_plaga,
                    "id_usuario": int(user_id)
                })
                db.commit()
            except Exception as e:
                print(f"Error al registrar consulta: {e}")
                # No hacemos rollback para no afectar el rendimiento

        return templates.TemplateResponse("plaga_detalle.html", {
            "request": request,
            "plaga": plaga_dict,
            "user_id": user_id,
            "user_role": user_role
        })

    except Exception as e:
        print(f"Error al obtener información de la plaga: {e}")
        return templates.TemplateResponse("error.html", {
            "request": request,
            "error": f"Error al obtener información de la plaga: {str(e)}",
            "user_id": user_id,
            "user_role": user_role
        })
