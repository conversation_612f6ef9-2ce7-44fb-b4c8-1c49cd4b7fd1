from fastapi import APIRouter, HTTPException
from typing import List
from pydantic import BaseModel
import aiomysql
from app.routers.fase_cultivo import conectar_db  # Reutilizar la función de conexión a la base de datos

router = APIRouter()

# Función para conectar a la base de datos
async def conectar_db():
    return await aiomysql.connect(
        host="localhost",
        port=3306,
        user="root",
        password="",
        db="MAIZGUARD"
    )
    

# Modelo para la respuesta de temporada
class Temporada(BaseModel):
    id_temporada: int
    nombre: str

# Endpoint para listar todas las temporadas
@router.get("/ListarTemporadas", response_model=List[Temporada])
async def listar_temporadas():
    conexion = None
    try:
        conexion = await conectar_db()
        async with conexion.cursor(aiomysql.DictCursor) as cursor:
            # Consultar todas las temporadas
            query = "SELECT id_temporada, nombre FROM temporada"
            await cursor.execute(query)
            temporadas = await cursor.fetchall()

        return temporadas

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error al listar las temporadas: {str(e)}")
    finally:
        if conexion:
            conexion.close()

# Endpoint para obtener una temporada por ID
@router.get("/Temporada/{id_temporada}", response_model=Temporada)
async def obtener_temporada(id_temporada: int):
    conexion = None
    try:
        conexion = await conectar_db()
        async with conexion.cursor(aiomysql.DictCursor) as cursor:
            # Consultar la temporada por ID
            query = "SELECT id_temporada, nombre FROM temporada WHERE id_temporada = %s"
            await cursor.execute(query, (id_temporada,))
            temporada = await cursor.fetchone()

            if not temporada:
                raise HTTPException(status_code=404, detail="Temporada no encontrada")

        return temporada

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error al obtener la temporada: {str(e)}")
    finally:
        if conexion:
            conexion.close()