from app.database.config import engine
from sqlalchemy import text

def actualizar_imagenes_plagas():
    """
    Actualiza las URLs de las imágenes de las plagas para usar las imágenes locales.
    """
    try:
        with engine.connect() as conn:
            print("Actualizando imágenes de plagas...")
            
            # Actualizar las imágenes de las plagas
            conn.execute(text("""
                UPDATE plagas 
                SET url_img = '/static/images/gusano-cogollero.jpg' 
                WHERE nombre LIKE '%Gusano cogollero%'
            """))
            
            conn.execute(text("""
                UPDATE plagas 
                SET url_img = '/static/images/gusano-elotero.jpg' 
                WHERE nombre LIKE '%Gusano elotero%'
            """))
            
            conn.execute(text("""
                UPDATE plagas 
                SET url_img = '/static/images/araña-roja.jpg' 
                WHERE nombre LIKE '%Araña roja%'
            """))
            
            conn.execute(text("""
                UPDATE plagas 
                SET url_img = '/static/images/gallina-ciega.jpg' 
                WHERE nombre LIKE '%Gallina ciega%'
            """))
            
            conn.execute(text("""
                UPDATE plagas 
                SET url_img = '/static/images/pulgones.jpg' 
                WHERE nombre LIKE '%Pulgón%' OR nombre LIKE '%Pulgones%'
            """))
            
            conn.execute(text("""
                UPDATE plagas 
                SET url_img = '/static/images/plaga-trips.jpg' 
                WHERE nombre LIKE '%Trips%'
            """))
            
            # Actualizar las plagas que no tienen imagen asignada
            conn.execute(text("""
                UPDATE plagas 
                SET url_img = '/static/images/cultivo.jpg' 
                WHERE url_img IS NULL OR url_img = ''
            """))
            
            # Confirmar cambios
            conn.commit()
            
            # Verificar las actualizaciones
            result = conn.execute(text("""
                SELECT id_plaga, nombre, url_img 
                FROM plagas 
                ORDER BY id_plaga
            """))
            
            plagas = result.fetchall()
            print("\nPlagas actualizadas:")
            for plaga in plagas:
                print(f"ID: {plaga[0]}, Nombre: {plaga[1]}, Imagen: {plaga[2]}")
            
            print("\nActualización completada exitosamente.")
            
    except Exception as e:
        print(f"Error al actualizar imágenes de plagas: {e}")

if __name__ == "__main__":
    actualizar_imagenes_plagas()
