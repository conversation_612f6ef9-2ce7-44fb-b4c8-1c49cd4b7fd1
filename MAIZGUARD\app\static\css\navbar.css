/* ===== NAVBAR STYLES - MAIZGUARD ===== */

/* === LOGO STYLES === */
.rounded-logo {
    height: 40px;
    width: 40px;
    margin-right: 10px;
    display: block;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid white;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
    transition: transform 0.3s ease;
    background-color: transparent;
    position: relative;
    left: 0;
}

.navbar-brand {
    display: flex;
    align-items: center;
    font-weight: bold;
    font-size: 1.3rem;
    margin-left: 0;
    position: relative;
    z-index: 10;
    padding-left: 0;
}

.navbar-brand:hover .rounded-logo {
    transform: scale(1.1);
    box-shadow: 0 0 8px rgba(255, 255, 255, 0.5);
}

/* === NAVBAR CONTAINER === */
.navbar {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1030;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    border-bottom: 3px solid var(--accent-color);
    min-height: 65px;
}

.navbar > .container {
    padding-left: 0;
    margin-left: 0;
}

/* Ajustar el contenido principal para que no quede debajo del navbar */
body {
    padding-top: 90px;
}

/* === NAVIGATION LINKS === */
.navbar-nav .nav-item {
    margin: 0 4px;
}

.nav-link {
    font-weight: 600;
    transition: all 0.3s ease;
    font-size: 1.1rem;
    padding: 0.6rem 1rem;
    letter-spacing: 0.02rem;
    color: white !important;
    border-radius: 8px;
    position: relative;
}

.nav-link:hover {
    color: var(--accent-color) !important;
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.nav-link.active {
    font-weight: 700;
    background-color: var(--accent-color);
    color: white !important;
    box-shadow: 0 2px 8px rgba(255, 102, 0, 0.3);
}

/* === SEARCH FORM === */
.navbar form.d-flex,
.navbar form.search-form {
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
    transition: all 0.3s ease;
}

.navbar .input-group {
    width: 100%;
}

.navbar .form-control {
    border-radius: 30px 0 0 30px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    padding-left: 20px;
    height: 48px;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
    font-size: 1.05rem;
    background-color: rgba(255, 255, 255, 0.95);
}

.navbar .form-control:focus {
    box-shadow: 0 4px 12px rgba(255, 102, 0, 0.3);
    border-color: var(--accent-color);
    background-color: white;
}

.navbar .btn-light {
    border-radius: 0 30px 30px 0;
    border: 2px solid rgba(255, 255, 255, 0.3);
    background: linear-gradient(135deg, var(--accent-color) 0%, var(--accent-light) 100%);
    color: white;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
    height: 48px;
    width: 55px;
    font-size: 1.1rem;
}

.navbar .btn-light:hover {
    background: linear-gradient(135deg, var(--accent-light) 0%, var(--accent-color) 100%);
    color: white;
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(255, 102, 0, 0.4);
}

/* === USER PROFILE === */
.profile-image {
    width: 40px;
    height: 40px;
    object-fit: cover;
    border: 3px solid white;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.profile-image:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 15px rgba(255, 102, 0, 0.4);
    border-color: var(--accent-color);
}

.navbar-username {
    color: white;
    font-weight: 600;
    font-size: 1rem;
    white-space: nowrap;
    max-width: 140px;
    overflow: hidden;
    text-overflow: ellipsis;
    transition: color 0.3s ease;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.navbar-username:hover {
    color: var(--accent-color);
}

/* === DROPDOWN MENU === */
.dropdown-menu {
    border-radius: 10px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border: none;
    margin-top: 8px;
}

.dropdown-item {
    padding: 10px 20px;
    transition: all 0.3s ease;
    border-radius: 5px;
    margin: 2px 5px;
}

.dropdown-item:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateX(5px);
}

.dropdown-item i {
    width: 20px;
    margin-right: 10px;
    text-align: center;
}

.dropdown-header {
    color: var(--primary-color) !important;
    font-weight: 600;
    font-size: 0.9rem;
    padding: 10px 20px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 200px;
}

.dropdown-header i {
    color: var(--accent-color);
}

/* === AVATAR OPTIONS === */
.avatar-option {
    width: 50px;
    height: 50px;
    object-fit: cover;
    cursor: pointer;
    transition: all 0.3s ease;
}

.avatar-option:hover {
    transform: scale(1.1);
    border-color: var(--primary-color) !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.avatar-option.selected {
    border-color: var(--primary-color) !important;
    border-width: 3px !important;
    transform: scale(1.05);
    box-shadow: 0 0 0 2px rgba(40, 167, 69, 0.3);
}

/* === RESPONSIVE DESIGN === */
/* Tablets y pantallas medianas */
@media (max-width: 991.98px) {
    .navbar-username {
        display: none;
    }

    .profile-image {
        margin-right: 0 !important;
    }

    .navbar form.search-form {
        max-width: 300px;
    }

    .rounded-logo {
        height: 40px;
        width: 40px;
        margin-right: 10px;
        left: -10px;
    }

    /* Ajustar el menú desplegable */
    .navbar-collapse {
        background-color: var(--primary-color);
        padding: 10px;
        box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
        border-radius: 0 0 10px 10px;
    }
}

/* Móviles */
@media (max-width: 767.98px) {
    body {
        padding-top: 80px;
    }

    .navbar {
        padding-top: 0.5rem;
        padding-bottom: 0.5rem;
        min-height: 70px;
    }

    .navbar form.search-form {
        max-width: 250px;
        margin: 0.5rem 0;
    }

    .navbar .form-control {
        height: 40px;
        font-size: 0.95rem;
    }

    .navbar .btn-light {
        height: 40px;
        width: 48px;
    }

    .rounded-logo {
        height: 40px;
        width: 40px;
        margin-right: 8px;
        left: -5px;
    }

    .nav-link {
        font-size: 1rem;
        padding: 0.4rem 0.7rem;
    }

    .navbar-brand {
        margin-left: -15px;
    }

    .navbar-toggler {
        padding: 0.3rem 0.5rem;
        font-size: 1rem;
    }

    .profile-image {
        width: 35px;
        height: 35px;
    }
}

/* Pantallas muy pequeñas */
@media (max-width: 575.98px) {
    body {
        padding-top: 75px;
    }

    .navbar {
        padding-top: 0.4rem;
        padding-bottom: 0.4rem;
        min-height: 65px;
    }

    .navbar form.search-form {
        max-width: 200px;
    }

    .navbar .container {
        padding-left: 0.5rem;
        padding-right: 0.5rem;
    }

    .navbar .form-control {
        height: 38px;
        font-size: 0.9rem;
    }

    .navbar .btn-light {
        height: 38px;
        width: 42px;
    }

    .rounded-logo {
        height: 35px;
        width: 35px;
        margin-right: 5px;
        left: 0;
    }

    .navbar-brand {
        margin-left: -10px;
    }

    .nav-link {
        font-size: 0.95rem;
        padding: 0.3rem 0.6rem;
    }

    .navbar-toggler {
        padding: 0.2rem 0.4rem;
        font-size: 0.9rem;
    }

    .profile-image {
        width: 32px !important;
        height: 32px !important;
    }
}

/* Pantallas extremadamente pequeñas */
@media (max-width: 375px) {
    body {
        padding-top: 50px;
    }

    .navbar form.search-form {
        max-width: 150px;
    }

    .navbar .form-control {
        height: 32px;
        font-size: 0.8rem;
        padding-left: 10px;
    }

    .navbar .btn-light {
        height: 32px;
        width: 35px;
    }

    .rounded-logo {
        height: 25px;
        width: 25px;
        margin-right: 3px;
        left: 0;
        border-width: 1px;
    }

    .nav-link {
        font-size: 0.8rem;
        padding: 0.2rem 0.4rem;
    }

    .navbar-toggler {
        padding: 0.1rem 0.25rem;
        font-size: 0.8rem;
    }

    .profile-image {
        width: 25px !important;
        height: 25px !important;
    }
}
