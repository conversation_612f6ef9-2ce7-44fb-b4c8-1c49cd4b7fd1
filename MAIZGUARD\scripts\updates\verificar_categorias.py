from app.database.config import engine
from sqlalchemy import text

def verificar_categorias():
    """
    Verifica las categorías de plagas existentes en la base de datos.
    """
    try:
        with engine.connect() as conn:
            # Verificar fases de cultivo
            print("FASES DE CULTIVO:")
            try:
                result = conn.execute(text("SELECT * FROM fases_cultivo"))
                fases = result.fetchall()
                if fases:
                    for fase in fases:
                        print(f"ID: {fase[0]}, Descripción: {fase[1]}")
                else:
                    print("No hay fases de cultivo registradas.")
            except Exception as e:
                print(f"Error al consultar fases_cultivo: {e}")
            
            print("\nTEMPORADAS:")
            try:
                result = conn.execute(text("SELECT * FROM temporadas"))
                temporadas = result.fetchall()
                if temporadas:
                    for temporada in temporadas:
                        print(f"ID: {temporada[0]}, Descripción: {temporada[1]}")
                else:
                    print("No hay temporadas registradas.")
            except Exception as e:
                print(f"Error al consultar temporadas: {e}")
            
            print("\nTIPOS DE PLAGA:")
            try:
                result = conn.execute(text("SELECT * FROM tipos_plaga"))
                tipos = result.fetchall()
                if tipos:
                    for tipo in tipos:
                        print(f"ID: {tipo[0]}, Descripción: {tipo[1]}")
                else:
                    print("No hay tipos de plaga registrados.")
            except Exception as e:
                print(f"Error al consultar tipos_plaga: {e}")
            
            print("\nPLAGAS POR CATEGORÍA:")
            try:
                result = conn.execute(text("""
                    SELECT p.id_plaga, p.nombre, fc.descripcion as fase, t.descripcion as temporada, tp.descripcion as tipo
                    FROM plagas p
                    LEFT JOIN fases_cultivo fc ON p.id_fase_cultivo = fc.id_fase_cultivo
                    LEFT JOIN temporadas t ON p.id_temporada = t.id_temporada
                    LEFT JOIN tipos_plaga tp ON p.id_tipo_plaga = tp.id_tipo_plaga
                    ORDER BY fc.descripcion, t.descripcion, tp.descripcion
                """))
                plagas = result.fetchall()
                if plagas:
                    for plaga in plagas:
                        print(f"ID: {plaga[0]}, Nombre: {plaga[1]}, Fase: {plaga[2]}, Temporada: {plaga[3]}, Tipo: {plaga[4]}")
                else:
                    print("No hay plagas registradas.")
            except Exception as e:
                print(f"Error al consultar plagas por categoría: {e}")
                
    except Exception as e:
        print(f"Error general: {e}")

if __name__ == "__main__":
    verificar_categorias()
