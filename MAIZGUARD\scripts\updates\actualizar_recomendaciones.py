from app.database.config import engine
from sqlalchemy import text

def actualizar_recomendaciones():
    """
    Actualiza las recomendaciones de las plagas para incluir tipos de plaguicidas y cantidades específicas.
    """
    try:
        with engine.connect() as conn:
            print("Actualizando recomendaciones de plagas...")
            
            # Gusano cogollero
            conn.execute(text("""
                UPDATE plagas 
                SET recomendaciones = 'Control químico: Aplicar Cipermetrina (25-30 ml por 20 litros de agua) o Clorpirifos (40-50 ml por 20 litros de agua). Aplicar en las primeras horas de la mañana o al atardecer, cubriendo bien el cogollo de la planta. Tratar un área de 1 hectárea con 200-300 litros de mezcla.

Control biológico: Liberación de Trichogramma (50,000-100,000 avispas por hectárea) o Bacillus thuringiensis (0.5-1 kg por hectárea).

Control cultural: Rotación de cultivos, eliminación de residuos de cosecha y control de malezas en un radio de 5-10 metros alrededor del cultivo.'
                WHERE nombre LIKE '%Gusano cogollero%'
            """))
            
            # Gusano elotero
            conn.execute(text("""
                UPDATE plagas 
                SET recomendaciones = 'Control químico: Aplicar Spinosad (10-15 ml por 20 litros de agua) o Lambda-cihalotrina (15-20 ml por 20 litros de agua) durante la emisión de estigmas. Realizar 2-3 aplicaciones con intervalos de 7 días. Tratar un área de 1 hectárea con 250-350 litros de mezcla.

Control biológico: Liberación de Trichogramma pretiosum (75,000-100,000 avispas por hectárea) en intervalos de 5-7 días durante el periodo de oviposición.

Control cultural: Siembra temprana para evitar altas poblaciones, eliminación de rastrojos después de la cosecha en un área de al menos 100 metros cuadrados alrededor del cultivo.'
                WHERE nombre LIKE '%Gusano elotero%'
            """))
            
            # Araña roja
            conn.execute(text("""
                UPDATE plagas 
                SET recomendaciones = 'Control químico: Aplicar Abamectina (10-15 ml por 20 litros de agua) o Azufre mojable (50-60 g por 20 litros de agua). Realizar 2 aplicaciones con intervalo de 7 días. Asegurar buena cobertura del envés de las hojas. Tratar un área de 1 hectárea con 300-400 litros de mezcla.

Control biológico: Liberación de ácaros depredadores como Phytoseiulus persimilis (10-15 individuos por m²) o Amblyseius californicus (25-50 individuos por m²).

Control cultural: Mantener niveles adecuados de humedad (riego por aspersión de 5-10 mm), evitar estrés hídrico y eliminar plantas hospederas en un radio de 10 metros.'
                WHERE nombre LIKE '%Araña roja%'
            """))
            
            # Roya del maíz
            conn.execute(text("""
                UPDATE plagas 
                SET recomendaciones = 'Control químico: Aplicar Tebuconazol (20-25 ml por 20 litros de agua) o Azoxistrobina (10-15 ml por 20 litros de agua) al primer síntoma. Realizar 2-3 aplicaciones con intervalos de 14 días. Tratar un área de 1 hectárea con 200-300 litros de mezcla.

Control cultural: Utilizar variedades resistentes, rotación de cultivos con especies no susceptibles en ciclos de 2-3 años, y mantener una densidad de siembra adecuada (70,000-80,000 plantas por hectárea).

Manejo de residuos: Eliminar y destruir residuos infectados en un área de al menos 500 metros cuadrados alrededor del cultivo afectado.'
                WHERE nombre LIKE '%Roya del maíz%'
            """))
            
            # Tizón foliar
            conn.execute(text("""
                UPDATE plagas 
                SET recomendaciones = 'Control químico: Aplicar Mancozeb (40-50 g por 20 litros de agua) o Clorotalonil (30-40 ml por 20 litros de agua) de manera preventiva. En caso de infección, usar Difenoconazol (15-20 ml por 20 litros de agua). Realizar 3-4 aplicaciones con intervalos de 10-14 días. Tratar un área de 1 hectárea con 250-350 litros de mezcla.

Control cultural: Aumentar la distancia entre plantas (80-90 cm entre surcos), mejorar el drenaje del terreno y evitar el riego por aspersión.

Manejo integrado: Rotación de cultivos en ciclos de 2-3 años y eliminación de residuos de cosecha en un radio de 15-20 metros alrededor del cultivo.'
                WHERE nombre LIKE '%Tizón foliar%'
            """))
            
            # Confirmar cambios
            conn.commit()
            
            # Verificar las actualizaciones
            result = conn.execute(text("""
                SELECT id_plaga, nombre, recomendaciones 
                FROM plagas 
                ORDER BY id_plaga
            """))
            
            plagas = result.fetchall()
            print("\nRecomendaciones actualizadas:")
            for plaga in plagas:
                print(f"ID: {plaga[0]}, Nombre: {plaga[1]}")
                print(f"Recomendaciones: {plaga[2][:100]}...")
                print()
            
            print("Actualización completada exitosamente.")
            
    except Exception as e:
        print(f"Error al actualizar recomendaciones de plagas: {e}")

if __name__ == "__main__":
    actualizar_recomendaciones()
