#!/usr/bin/env python3
"""
Script para verificar y crear las tablas necesarias para MaizGuard
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from app.database.config import engine
from sqlalchemy import text

def verificar_y_crear_tablas():
    """Verificar y crear las tablas necesarias"""
    try:
        with engine.connect() as conn:
            # Verificar qué tablas existen
            result = conn.execute(text("SHOW TABLES"))
            tablas_existentes = [row[0] for row in result.fetchall()]
            
            print("Tablas existentes en la base de datos:")
            for tabla in tablas_existentes:
                print(f"  - {tabla}")
            
            # Tablas necesarias
            tablas_necesarias = [
                'estados', 'roles', 'usuarios', 'fases_cultivo', 
                'tipos_plaga', 'temporadas', 'plagas'
            ]
            
            # Verificar qué tablas faltan
            tablas_faltantes = [tabla for tabla in tablas_necesarias if tabla not in tablas_existentes]
            
            if tablas_faltantes:
                print(f"\nTablas faltantes: {', '.join(tablas_faltantes)}")
                print("Creando tablas faltantes...")
                
                # Crear tabla estados si no existe
                if 'estados' in tablas_faltantes:
                    conn.execute(text("""
                        CREATE TABLE estados (
                            id_estados INT AUTO_INCREMENT PRIMARY KEY,
                            descripcion VARCHAR(50) NOT NULL
                        )
                    """))
                    # Insertar estados básicos
                    conn.execute(text("""
                        INSERT INTO estados (id_estados, descripcion) VALUES
                        (1, 'Activo'),
                        (2, 'Inactivo')
                        ON DUPLICATE KEY UPDATE descripcion = VALUES(descripcion)
                    """))
                    print("✅ Tabla 'estados' creada.")
                
                # Crear tabla roles si no existe
                if 'roles' in tablas_faltantes:
                    conn.execute(text("""
                        CREATE TABLE roles (
                            id_rol INT AUTO_INCREMENT PRIMARY KEY,
                            nombre VARCHAR(50) NOT NULL,
                            descripcion VARCHAR(255)
                        )
                    """))
                    # Insertar roles básicos
                    conn.execute(text("""
                        INSERT INTO roles (id_rol, nombre, descripcion) VALUES
                        (1, 'Administrador', 'Usuario con permisos completos'),
                        (2, 'Usuario', 'Usuario regular del sistema')
                        ON DUPLICATE KEY UPDATE nombre = VALUES(nombre), descripcion = VALUES(descripcion)
                    """))
                    print("✅ Tabla 'roles' creada.")
                
                # Crear tabla fases_cultivo si no existe
                if 'fases_cultivo' in tablas_faltantes:
                    conn.execute(text("""
                        CREATE TABLE fases_cultivo (
                            id_fase_cultivo INT AUTO_INCREMENT PRIMARY KEY,
                            descripcion VARCHAR(100) NOT NULL
                        )
                    """))
                    # Insertar fases básicas
                    conn.execute(text("""
                        INSERT INTO fases_cultivo (id_fase_cultivo, descripcion) VALUES
                        (1, 'Germinación'),
                        (2, 'Crecimiento vegetativo'),
                        (3, 'Floración'),
                        (4, 'Maduración'),
                        (5, 'Cosecha')
                        ON DUPLICATE KEY UPDATE descripcion = VALUES(descripcion)
                    """))
                    print("✅ Tabla 'fases_cultivo' creada.")
                
                # Crear tabla tipos_plaga si no existe
                if 'tipos_plaga' in tablas_faltantes:
                    conn.execute(text("""
                        CREATE TABLE tipos_plaga (
                            id_tipo_plaga INT AUTO_INCREMENT PRIMARY KEY,
                            nombre VARCHAR(100) NOT NULL,
                            descripcion VARCHAR(255)
                        )
                    """))
                    # Insertar tipos básicos
                    conn.execute(text("""
                        INSERT INTO tipos_plaga (id_tipo_plaga, nombre, descripcion) VALUES
                        (1, 'Insecto', 'Plagas de tipo insecto'),
                        (2, 'Hongo', 'Enfermedades fúngicas'),
                        (3, 'Bacteria', 'Enfermedades bacterianas'),
                        (4, 'Virus', 'Enfermedades virales'),
                        (5, 'Maleza', 'Plantas no deseadas')
                        ON DUPLICATE KEY UPDATE nombre = VALUES(nombre), descripcion = VALUES(descripcion)
                    """))
                    print("✅ Tabla 'tipos_plaga' creada.")
                
                # Crear tabla temporadas si no existe
                if 'temporadas' in tablas_faltantes:
                    conn.execute(text("""
                        CREATE TABLE temporadas (
                            id_temporada INT AUTO_INCREMENT PRIMARY KEY,
                            nombre VARCHAR(50) NOT NULL,
                            descripcion VARCHAR(255)
                        )
                    """))
                    # Insertar temporadas básicas
                    conn.execute(text("""
                        INSERT INTO temporadas (id_temporada, nombre, descripcion) VALUES
                        (1, 'Seca', 'Temporada seca del año'),
                        (2, 'Lluviosa', 'Temporada lluviosa del año')
                        ON DUPLICATE KEY UPDATE nombre = VALUES(nombre), descripcion = VALUES(descripcion)
                    """))
                    print("✅ Tabla 'temporadas' creada.")
                
                # Crear tabla plagas si no existe
                if 'plagas' in tablas_faltantes:
                    conn.execute(text("""
                        CREATE TABLE plagas (
                            id_plaga INT AUTO_INCREMENT PRIMARY KEY,
                            nombre VARCHAR(100) NOT NULL,
                            nombre_cientifico VARCHAR(100),
                            url_img VARCHAR(255),
                            descripcion TEXT NOT NULL,
                            recomendaciones TEXT,
                            id_fase_cultivo INT NOT NULL,
                            id_tipo_plaga INT NOT NULL,
                            id_temporada INT NOT NULL,
                            id_usuarios INT NOT NULL,
                            fecha_registro DATETIME DEFAULT CURRENT_TIMESTAMP,
                            FOREIGN KEY (id_fase_cultivo) REFERENCES fases_cultivo(id_fase_cultivo),
                            FOREIGN KEY (id_tipo_plaga) REFERENCES tipos_plaga(id_tipo_plaga),
                            FOREIGN KEY (id_temporada) REFERENCES temporadas(id_temporada),
                            FOREIGN KEY (id_usuarios) REFERENCES usuarios(id_usuarios)
                        )
                    """))
                    print("✅ Tabla 'plagas' creada.")
                
                conn.commit()
                print("\n🎉 Todas las tablas han sido creadas exitosamente!")
            else:
                print("\n✅ Todas las tablas necesarias ya existen.")
                
    except Exception as e:
        print(f"❌ Error al verificar/crear tablas: {e}")

if __name__ == "__main__":
    print("🔍 Verificando estructura de la base de datos...")
    verificar_y_crear_tablas()
