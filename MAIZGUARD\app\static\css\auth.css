/* Estilos para las páginas de autenticación (login y registro) */

/* Estilos para las tarjetas de formulario */
.card {
    border: none;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1) !important;
}

.card-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)) !important;
    border-bottom: none;
}

.card-header h3 {
    font-size: 1.5rem;
    letter-spacing: 0.5px;
}

.card-body {
    padding: 2rem !important;
}

.card-footer {
    background-color: #f8f9fa;
    border-top: none;
}

/* Estilos para los inputs flotantes */
.form-floating > .form-control {
    padding: 1rem 0.75rem;
}

.form-floating > .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(57, 169, 0, 0.25);
}

.form-floating > label {
    padding: 1rem 0.75rem;
}

/* Estilos para validación de formularios */
.form-control.is-invalid {
    border-color: #dc3545;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.form-control.is-invalid:focus {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.25);
}

.invalid-feedback {
    display: none;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875em;
    color: #dc3545;
}

.form-control.is-invalid ~ .invalid-feedback {
    display: block;
}

.form-text {
    margin-top: 0.25rem;
    font-size: 0.875em;
    color: #6c757d;
}

/* Estilos para los botones */
.btn-success {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    transition: all 0.3s ease;
}

.btn-success:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Estilos para los enlaces */
a.text-success {
    color: var(--primary-color) !important;
    text-decoration: none;
    transition: all 0.3s ease;
}

a.text-success:hover {
    color: var(--secondary-color) !important;
    text-decoration: underline;
}

/* Estilos para los checkboxes */
.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.form-check-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(40, 167, 69, 0.25);
}

/* Estilos responsivos */
@media (max-width: 768px) {
    .card-body {
        padding: 1.5rem !important;
    }

    .card-header h3 {
        font-size: 1.3rem;
    }
}
