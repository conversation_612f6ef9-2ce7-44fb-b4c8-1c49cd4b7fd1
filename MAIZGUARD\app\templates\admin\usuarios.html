<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestión de Usuarios - MAIZGUARD</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Estilos personalizados - EXACTAMENTE IGUAL QUE INICIO -->
    <link rel="stylesheet" href="/static/css/main.css">
    <link rel="stylesheet" href="/static/css/navbar.css">
    <link rel="stylesheet" href="/static/css/compact.css">
    <link rel="stylesheet" href="/static/css/super-responsive.css">
    <link rel="stylesheet" href="/static/css/small-laptop.css">
    <link rel="stylesheet" href="/static/css/admin.css">
    <link rel="stylesheet" href="/static/css/admin-panel.css">
</head>
<body id="admin-app">
    <!-- Incluir el navbar normal -->
    {% include "partials/navbar.html" %}

    <!-- Contenedor principal sin espaciado extra -->
    <div class="container-fluid admin-container">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block admin-sidebar">
                <div class="sidebar-content">
                    <div class="text-center mb-4">
                        <h5 class="text-white">Panel de Administración</h5>
                        <p class="text-light small">Bienvenido, {{ user.nombre }}</p>
                    </div>

                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="sidebar-link" href="/admin">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="sidebar-link active" href="/admin/usuarios">
                                <i class="fas fa-users me-2"></i>
                                Usuarios
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="sidebar-link" href="/admin/cultivos-usuarios">
                                <i class="fas fa-seedling me-2"></i>
                                Cultivos de Usuarios
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="sidebar-link" href="/admin/plagas">
                                <i class="fas fa-bug me-2"></i>
                                Plagas
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="sidebar-link" href="/">
                                <i class="fas fa-home me-2"></i>
                                Volver al Inicio
                            </a>
                        </li>
                        <li class="nav-item mt-3">
                            <a class="sidebar-link text-danger" href="/logout">
                                <i class="fas fa-sign-out-alt me-2"></i>
                                Cerrar Sesión
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Contenido principal -->
            <main class="col-md-9 col-lg-10 admin-main-content">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Gestión de Usuarios</h1>
                </div>

                <!-- Estadísticas de usuarios -->
                <div class="row mb-4">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <div class="text-white-50 small">Total Usuarios</div>
                                        <div class="h4 mb-0" id="total-usuarios">Cargando...</div>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-users fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <div class="text-white-50 small">Usuarios Activos</div>
                                        <div class="h4 mb-0" id="usuarios-activos">Cargando...</div>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-user-check fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <div class="text-white-50 small">Administradores</div>
                                        <div class="h4 mb-0" id="total-admins">Cargando...</div>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-user-shield fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <div class="text-white-50 small">Usuarios Normales</div>
                                        <div class="h4 mb-0" id="usuarios-normales">Cargando...</div>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-user fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filtros y búsqueda de usuarios -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3 bg-gradient-primary">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <h6 class="m-0 font-weight-bold text-white">
                                    <i class="fas fa-users me-2"></i>Usuarios Registrados
                                </h6>
                            </div>
                            <div class="col-md-6">
                                <div class="input-group">
                                    <input type="text" class="form-control" id="buscarUsuario" placeholder="Buscar usuario...">
                                    <button class="btn btn-outline-light" type="button" onclick="filtrarUsuarios()">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <!-- Filtros adicionales -->
                        <div class="row p-3 bg-light border-bottom">
                            <div class="col-md-3">
                                <select class="form-select form-select-sm" id="filtroRol" onchange="filtrarUsuarios()">
                                    <option value="">Todos los roles</option>
                                    <option value="1">Administradores</option>
                                    <option value="2">Usuarios</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select form-select-sm" id="filtroOrdenUsuarios" onchange="ordenarUsuarios()">
                                    <option value="fecha_desc">Más recientes</option>
                                    <option value="fecha_asc">Más antiguos</option>
                                    <option value="nombre_asc">Nombre A-Z</option>
                                    <option value="email_asc">Email A-Z</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <button type="button" class="btn btn-outline-success btn-sm" onclick="exportarUsuarios()">
                                    <i class="fas fa-download me-1"></i>Exportar
                                </button>
                            </div>
                            <div class="col-md-3 text-end">
                                <span class="badge bg-info" id="contadorUsuarios">0 usuarios</span>
                            </div>
                        </div>

                        <!-- Grid de usuarios mejorado -->
                        <div class="p-3">
                            <div class="row" id="usuarios-grid">
                                <div class="col-12 text-center py-5">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Cargando...</span>
                                    </div>
                                    <p class="mt-2 text-muted">Cargando usuarios...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- JavaScript para usuarios -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            cargarUsuarios();
            cargarEstadisticasUsuarios();
        });

        async function cargarUsuarios() {
            try {
                const response = await fetch('/admin/api/usuarios');
                if (response.ok) {
                    const data = await response.json();
                    mostrarUsuarios(data.usuarios);
                } else {
                    document.getElementById('usuarios-tbody').innerHTML = '<tr><td colspan="7" class="text-center text-danger">Error al cargar usuarios</td></tr>';
                }
            } catch (error) {
                console.error('Error:', error);
                document.getElementById('usuarios-tbody').innerHTML = '<tr><td colspan="7" class="text-center text-danger">Error de conexión</td></tr>';
            }
        }

        async function cargarEstadisticasUsuarios() {
            try {
                const response = await fetch('/admin/api/estadisticas');
                if (response.ok) {
                    const stats = await response.json();
                    document.getElementById('total-usuarios').textContent = stats.total_usuarios || 0;
                    document.getElementById('usuarios-activos').textContent = stats.usuarios_activos || 0;
                    
                    // Calcular admins y usuarios normales
                    const responseUsuarios = await fetch('/admin/api/usuarios');
                    if (responseUsuarios.ok) {
                        const data = await responseUsuarios.json();
                        const admins = data.usuarios.filter(u => u.id_rol === 1).length;
                        const normales = data.usuarios.filter(u => u.id_rol === 2).length;
                        
                        document.getElementById('total-admins').textContent = admins;
                        document.getElementById('usuarios-normales').textContent = normales;
                    }
                }
            } catch (error) {
                console.error('Error al cargar estadísticas:', error);
            }
        }

        // Variables globales para filtrado de usuarios
        let usuariosOriginales = [];
        let usuariosFiltrados = [];

        function mostrarUsuarios(usuarios) {
            usuariosOriginales = usuarios;
            usuariosFiltrados = [...usuarios];

            const grid = document.getElementById('usuarios-grid');
            const contador = document.getElementById('contadorUsuarios');

            if (!usuarios || usuarios.length === 0) {
                grid.innerHTML = `
                    <div class="col-12 text-center py-5">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No hay usuarios registrados</h5>
                        <p class="text-muted">Aún no se han registrado usuarios en el sistema.</p>
                    </div>
                `;
                contador.textContent = '0 usuarios';
                return;
            }

            contador.textContent = `${usuarios.length} usuario${usuarios.length !== 1 ? 's' : ''}`;

            let html = '';
            usuarios.forEach(usuario => {
                const rolBadge = usuario.id_rol === 1 ? 'danger' : 'primary';
                const rolTexto = usuario.id_rol === 1 ? 'Administrador' : 'Usuario';
                const estadoBadge = usuario.id_estados === 1 ? 'success' : 'secondary';
                const estadoTexto = usuario.id_estados === 1 ? 'Activo' : 'Inactivo';
                const fecha = usuario.fecha_registro ? new Date(usuario.fecha_registro).toLocaleDateString('es-ES') : 'N/A';
                const avatarUrl = usuario.fotografia || '/static/images/perfil icono.jpg';

                html += `
                    <div class="col-lg-6 col-xl-4 mb-4">
                        <div class="card h-100 shadow-sm border-0 usuario-card">
                            <div class="card-header bg-gradient-${rolBadge === 'danger' ? 'warning' : 'primary'} text-white">
                                <div class="row align-items-center">
                                    <div class="col">
                                        <h6 class="mb-0 fw-bold text-white">
                                            <i class="fas fa-user me-2"></i>${usuario.nombre} ${usuario.apellido || ''}
                                        </h6>
                                    </div>
                                    <div class="col-auto">
                                        <span class="badge bg-light text-dark">#${usuario.id_usuarios}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="row mb-3">
                                    <div class="col-4">
                                        <div class="text-center">
                                            <img src="${avatarUrl}" alt="Avatar" class="rounded-circle" style="width: 60px; height: 60px; object-fit: cover;">
                                        </div>
                                    </div>
                                    <div class="col-8">
                                        <div class="mb-2">
                                            <i class="fas fa-envelope text-primary me-2"></i>
                                            <small class="text-muted">${usuario.correo_electronico}</small>
                                        </div>
                                        <div class="mb-2">
                                            <span class="badge bg-${rolBadge} fs-6 px-3 py-2">
                                                <i class="fas fa-${usuario.id_rol === 1 ? 'user-shield' : 'user'} me-1"></i>${rolTexto}
                                            </span>
                                        </div>
                                        <div>
                                            <span class="badge bg-${estadoBadge}">
                                                <i class="fas fa-${usuario.id_estados === 1 ? 'check-circle' : 'times-circle'} me-1"></i>${estadoTexto}
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                <div class="row small text-muted mb-3">
                                    <div class="col-12">
                                        <i class="fas fa-calendar-check me-1"></i>
                                        <strong>Registro:</strong> ${fecha}
                                    </div>
                                </div>
                            </div>
                            <div class="card-footer bg-light">
                                <div class="btn-group w-100" role="group">
                                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="cambiarRol(${usuario.id_usuarios}, ${usuario.id_rol})" title="Cambiar rol">
                                        <i class="fas fa-user-cog me-1"></i>Rol
                                    </button>
                                    <button type="button" class="btn btn-outline-warning btn-sm" onclick="cambiarEstado(${usuario.id_usuarios}, ${usuario.id_estados})" title="Cambiar estado">
                                        <i class="fas fa-toggle-on me-1"></i>Estado
                                    </button>
                                    <button type="button" class="btn btn-outline-info btn-sm" onclick="contactarUsuario('${usuario.correo_electronico}')" title="Contactar">
                                        <i class="fas fa-envelope me-1"></i>Email
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });

            grid.innerHTML = html;
        }

        // Función para filtrar usuarios
        function filtrarUsuarios() {
            const busqueda = document.getElementById('buscarUsuario').value.toLowerCase();
            const filtroRol = document.getElementById('filtroRol').value;

            usuariosFiltrados = usuariosOriginales.filter(usuario => {
                const coincideBusqueda = !busqueda ||
                    (usuario.nombre && usuario.nombre.toLowerCase().includes(busqueda)) ||
                    (usuario.apellido && usuario.apellido.toLowerCase().includes(busqueda)) ||
                    (usuario.correo_electronico && usuario.correo_electronico.toLowerCase().includes(busqueda));

                const coincideRol = !filtroRol || usuario.id_rol.toString() === filtroRol;

                return coincideBusqueda && coincideRol;
            });

            mostrarUsuarios(usuariosFiltrados);
        }

        // Función para ordenar usuarios
        function ordenarUsuarios() {
            const orden = document.getElementById('filtroOrdenUsuarios').value;

            usuariosFiltrados.sort((a, b) => {
                switch(orden) {
                    case 'fecha_desc':
                        return new Date(b.fecha_registro) - new Date(a.fecha_registro);
                    case 'fecha_asc':
                        return new Date(a.fecha_registro) - new Date(b.fecha_registro);
                    case 'nombre_asc':
                        return (a.nombre || '').localeCompare(b.nombre || '');
                    case 'email_asc':
                        return (a.correo_electronico || '').localeCompare(b.correo_electronico || '');
                    default:
                        return 0;
                }
            });

            mostrarUsuarios(usuariosFiltrados);
        }

        // Función para exportar usuarios
        function exportarUsuarios() {
            if (usuariosFiltrados.length === 0) {
                alert('No hay usuarios para exportar');
                return;
            }

            const csv = [
                ['ID', 'Nombre', 'Apellido', 'Email', 'Rol', 'Estado', 'Fecha Registro'].join(','),
                ...usuariosFiltrados.map(u => [
                    u.id_usuarios,
                    `"${u.nombre || ''}"`,
                    `"${u.apellido || ''}"`,
                    u.correo_electronico || '',
                    u.id_rol === 1 ? 'Administrador' : 'Usuario',
                    u.id_estados === 1 ? 'Activo' : 'Inactivo',
                    u.fecha_registro ? new Date(u.fecha_registro).toLocaleDateString('es-ES') : ''
                ].join(','))
            ].join('\n');

            const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = `usuarios_${new Date().toISOString().split('T')[0]}.csv`;
            link.click();
        }

        // Función para contactar usuario
        function contactarUsuario(email) {
            if (email) {
                window.location.href = `mailto:${email}?subject=Contacto desde MaizGuard Admin`;
            } else {
                alert('Este usuario no tiene email registrado');
            }
        }

        async function cambiarRol(idUsuario, rolActual) {
            const nuevoRol = rolActual === 1 ? 2 : 1;
            const rolTexto = nuevoRol === 1 ? 'Administrador' : 'Usuario';

            if (confirm(`¿Cambiar rol a ${rolTexto}?`)) {
                try {
                    const response = await fetch(`/admin/api/usuarios/${idUsuario}/rol`, {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ id_rol: nuevoRol })
                    });

                    if (response.ok) {
                        alert('Rol actualizado exitosamente');
                        cargarUsuarios();
                        cargarEstadisticasUsuarios();
                    } else {
                        const error = await response.json();
                        alert('Error: ' + error.detail);
                    }
                } catch (error) {
                    alert('Error de conexión');
                }
            }
        }

        async function cambiarEstado(idUsuario, estadoActual) {
            const nuevoEstado = estadoActual === 1 ? 2 : 1;
            const estadoTexto = nuevoEstado === 1 ? 'Activo' : 'Inactivo';
            
            if (confirm(`¿Cambiar estado a ${estadoTexto}?`)) {
                try {
                    const response = await fetch(`/admin/api/usuarios/${idUsuario}/estado`, {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ id_estados: nuevoEstado })
                    });
                    
                    if (response.ok) {
                        alert('Estado actualizado exitosamente');
                        cargarUsuarios();
                        cargarEstadisticasUsuarios();
                    } else {
                        const error = await response.json();
                        alert('Error: ' + error.detail);
                    }
                } catch (error) {
                    alert('Error de conexión');
                }
            }
        }
    </script>
</body>
</html>
