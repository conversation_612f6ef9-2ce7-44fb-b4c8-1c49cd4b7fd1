<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Restablecer Contraseña - MAIZGUARD</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <!-- Estilos personalizados -->
    <link rel="stylesheet" href="/static/css/main.css">
    <link rel="stylesheet" href="/static/css/compact.css">
    <link rel="stylesheet" href="/static/css/fixed-navbar.css">
    <link rel="stylesheet" href="/static/css/super-responsive.css">
    <link rel="stylesheet" href="/static/css/small-laptop.css">
    <link rel="stylesheet" href="/static/css/navbar.css">
    <link rel="preload" href="/static/images/Logo m.png" as="image">
</head>
<body class="d-flex flex-column min-vh-100">
    <!-- Incluir el navbar -->
    {% include "partials/navbar.html" %}

    <!-- Espacio de separación después del navbar -->
    <div class="container-fluid py-2">
        <!-- Este div crea un espacio entre el navbar y el contenido -->
    </div>

    <!-- Formulario de Restablecimiento de Contraseña -->
    <section class="py-5">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-8 col-lg-5">
                    <div class="card shadow-lg border-0 rounded-lg">
                        <div class="card-header bg-success text-white text-center py-4">
                            <h3 class="mb-0 fw-bold">Restablecer Contraseña</h3>
                        </div>
                        {% if error %}
                        <div class="alert alert-danger alert-dismissible fade show m-3" role="alert">
                            {{ error }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                        {% endif %}
                        <div class="card-body p-4">
                            <form action="/reset-password/{{ token }}" method="post" id="resetPasswordForm">
                                <div class="form-floating mb-3">
                                    <input class="form-control" id="inputPassword" type="password" name="password" placeholder="Nueva contraseña" required minlength="8" />
                                    <label for="inputPassword">Nueva contraseña</label>
                                    <div class="form-text">La contraseña debe tener al menos 8 caracteres.</div>
                                </div>
                                <div class="form-floating mb-3">
                                    <input class="form-control" id="inputPasswordConfirm" type="password" name="passwordConfirm" placeholder="Confirmar contraseña" required />
                                    <label for="inputPasswordConfirm">Confirmar contraseña</label>
                                    <div class="invalid-feedback">Las contraseñas no coinciden.</div>
                                </div>
                                <div class="d-grid">
                                    <button class="btn btn-success btn-lg" type="submit">Restablecer contraseña</button>
                                </div>
                            </form>
                        </div>
                        <div class="card-footer text-center py-3">
                            <div class="small">
                                <a href="/login" class="text-success"><i class="fas fa-arrow-left me-1"></i> Volver a Iniciar Sesión</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Incluir el footer -->
    {% include "partials/footer.html" %}

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js" defer></script>
    <!-- Scripts personalizados -->
    <script src="/static/js/scripts.js" defer></script>
    <script src="/static/js/script.js" defer></script>
    <script src="/static/js/validation.js" defer></script>
    <script>
        // Validación específica para el formulario de restablecimiento de contraseña
        document.addEventListener('DOMContentLoaded', function() {
            const resetForm = document.getElementById('resetPasswordForm');

            if (resetForm) {
                const password = document.getElementById('inputPassword');
                const confirmPassword = document.getElementById('inputPasswordConfirm');

                // Validar que las contraseñas coincidan
                function validatePassword() {
                    if (password.value !== confirmPassword.value) {
                        confirmPassword.setCustomValidity('Las contraseñas no coinciden');
                        confirmPassword.classList.add('is-invalid');
                    } else {
                        confirmPassword.setCustomValidity('');
                        confirmPassword.classList.remove('is-invalid');
                    }
                }

                // Eventos para validación en tiempo real
                password.addEventListener('input', validatePassword);
                confirmPassword.addEventListener('input', validatePassword);

                // Validación al enviar el formulario
                resetForm.addEventListener('submit', function(event) {
                    validatePassword();

                    if (password.value !== confirmPassword.value) {
                        event.preventDefault();
                    }
                });
            }
        });
    </script>
</body>
</html>
