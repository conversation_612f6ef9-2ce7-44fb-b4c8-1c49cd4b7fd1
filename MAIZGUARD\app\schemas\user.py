from pydantic import BaseModel, EmailStr, Field, validator
from datetime import date, datetime
from typing import Optional
import re

class UserCreate(BaseModel):
    name: str = Field(..., min_length=2, max_length=100)
    email: EmailStr
    password: str = Field(..., min_length=8)
    birthdate: date
    
    @validator('birthdate')
    def validate_birthdate(cls, v):
        today = date.today()
        age = today.year - v.year - ((today.month, today.day) < (v.month, v.day))
        if age < 18:
            raise ValueError('Debes ser mayor de 18 años')
        return v
    
    @validator('password')
    def validate_password(cls, v):
        if len(v) < 8:
            raise ValueError('La contraseña debe tener al menos 8 caracteres')
        return v

class UserResponse(BaseModel):
    id: int
    name: str
    email: str
    birthdate: date
    created_at: datetime
    
    class Config:
        orm_mode = True
