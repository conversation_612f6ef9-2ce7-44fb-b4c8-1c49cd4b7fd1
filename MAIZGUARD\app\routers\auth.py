from fastapi import APIRouter, Request, Depends, Form, HTTPException, status, Cookie
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse, RedirectResponse
from typing import List, Optional
from sqlalchemy.orm import Session
from datetime import datetime, timedelta
import bcrypt
import re

from app.database.config import get_db
from app.database.models import User
from app.utils.security import hash_password, verify_password, generate_reset_token, send_reset_email
from sqlalchemy import text

router = APIRouter(
    prefix="",
    tags=["auth"],
    responses={404: {"description": "Not found"}},
)

# Lista de cookies que se establecen al iniciar sesión
AUTH_COOKIES = ["user_id", "user_role"]

templates = Jinja2Templates(directory="app/templates")

@router.get("/logout")
async def logout(request: Request):
    """
    Cierra la sesión del usuario eliminando todas las cookies de autenticación.
    """
    response = RedirectResponse(url="/", status_code=status.HTTP_303_SEE_OTHER)

    # Eliminar todas las cookies de autenticación
    for cookie_name in AUTH_COOKIES:
        response.delete_cookie(key=cookie_name)

    return response

@router.get("/actualizar-sesion")
async def actualizar_sesion(request: Request, db: Session = Depends(get_db)):
    """
    Actualizar las cookies de sesión con la información actual de la base de datos.
    Útil cuando se cambia el rol de un usuario y necesita ver los cambios sin cerrar sesión.
    """
    try:
        # Obtener el ID del usuario de las cookies actuales
        user_id = request.cookies.get("user_id")

        if not user_id:
            return RedirectResponse(url="/login", status_code=303)

        # Obtener información actualizada del usuario desde la base de datos
        query = text("""
            SELECT u.id_usuarios, u.nombre, u.apellido, u.id_rol, u.id_estados
            FROM usuarios u
            WHERE u.id_usuarios = :user_id AND u.id_estados = 1
        """)

        result = db.execute(query, {"user_id": int(user_id)})
        user = result.fetchone()

        if not user:
            # Usuario no encontrado o inactivo, cerrar sesión
            response = RedirectResponse(url="/login", status_code=303)
            for cookie_name in AUTH_COOKIES:
                response.delete_cookie(key=cookie_name)
            return response

        # Actualizar las cookies con la información actual
        response = RedirectResponse(url="/", status_code=303)
        response.set_cookie("user_id", str(user.id_usuarios))
        response.set_cookie("user_role", str(user.id_rol))

        return response

    except Exception as e:
        print(f"Error al actualizar sesión: {e}")
        return RedirectResponse(url="/login", status_code=303)

@router.get("/login", response_class=HTMLResponse)
async def login(
    request: Request,
    user_id: Optional[str] = Cookie(None),
    user_role: Optional[str] = Cookie(None)
):
    # Si el usuario ya está autenticado, redirigir a la página principal
    if user_id:
        return RedirectResponse(url="/", status_code=status.HTTP_303_SEE_OTHER)

    return templates.TemplateResponse("login.html", {
        "request": request,
        "errors": {},
        "user_id": user_id,
        "user_role": user_role
    })

@router.post("/login")
async def process_login(
    request: Request,
    email: str = Form(...),
    password: str = Form(...),
    db: Optional[Session] = Depends(get_db)
):
    # Verificar si la base de datos está disponible
    if db is None:
        return templates.TemplateResponse(
            "login.html",
            {"request": request, "error": "El servicio de base de datos no está disponible en este momento. Por favor, inténtelo más tarde."}
        )

    # Buscar el usuario por email
    user = db.query(User).filter(User.correo_electronico == email).first()

    # Verificar si el usuario existe
    if not user:
        return templates.TemplateResponse(
            "login.html",
            {"request": request, "error": "Correo electrónico o contraseña incorrectos"}
        )

    # Verificar si la contraseña es correcta usando bcrypt
    if not bcrypt.checkpw(password.encode('utf-8'), user.contrasena.encode('utf-8')):
        return templates.TemplateResponse(
            "login.html",
            {"request": request, "error": "Correo electrónico o contraseña incorrectos"}
        )

    # Verificar si el usuario está activo
    if user.id_estados != 1:
        return templates.TemplateResponse(
            "login.html",
            {"request": request, "error": "Tu cuenta está desactivada. Contacta al administrador."}
        )

    # Crear una cookie de sesión
    response = RedirectResponse(url="/", status_code=status.HTTP_303_SEE_OTHER)
    response.set_cookie(key="user_id", value=str(user.id_usuarios), httponly=True)
    response.set_cookie(key="user_role", value=str(user.id_rol), httponly=True)

    return response

@router.get("/recuperar-password", response_class=HTMLResponse)
async def recuperar_password(request: Request):
    return templates.TemplateResponse("recuperar_password.html", {"request": request})

@router.post("/recuperar-password")
async def process_recuperar_password(
    request: Request,
    email: str = Form(...),
    db: Optional[Session] = Depends(get_db)
):
    # Verificar si la base de datos está disponible
    if db is None:
        return templates.TemplateResponse(
            "recuperar_password.html",
            {"request": request, "error": "El servicio de base de datos no está disponible en este momento. Por favor, inténtelo más tarde."}
        )

    # Buscar el usuario por email
    user = db.query(User).filter(User.correo_electronico == email).first()

    # Si el usuario no existe, mostramos el mismo mensaje para evitar revelar información
    if not user:
        return templates.TemplateResponse(
            "recuperar_password.html",
            {"request": request, "success": "Si tu correo está registrado, recibirás instrucciones para restablecer tu contraseña."}
        )

    # Generar token de restablecimiento
    token = generate_reset_token()

    # Establecer fecha de expiración (1 hora)
    expiry = datetime.now() + timedelta(hours=1)

    # Guardar token en la base de datos
    user.token_recuperacion = token
    user.fecha_expiracion_token = expiry
    db.commit()

    # Enviar correo con el enlace de restablecimiento
    base_url = str(request.base_url).rstrip('/')
    send_reset_email(user.correo_electronico, token, base_url)

    return templates.TemplateResponse(
        "recuperar_password.html",
        {"request": request, "success": "Se han enviado instrucciones a tu correo electrónico para restablecer tu contraseña."}
    )

@router.get("/reset-password/{token}", response_class=HTMLResponse)
async def reset_password(request: Request, token: str, db: Optional[Session] = Depends(get_db)):
    # Verificar si la base de datos está disponible
    if db is None:
        return templates.TemplateResponse(
            "recuperar_password.html",
            {"request": request, "error": "El servicio de base de datos no está disponible en este momento. Por favor, inténtelo más tarde."}
        )

    # Buscar usuario con este token
    user = db.query(User).filter(User.token_recuperacion == token).first()

    # Verificar si el token es válido y no ha expirado
    if not user or not user.fecha_expiracion_token or user.fecha_expiracion_token < datetime.now():
        return templates.TemplateResponse(
            "recuperar_password.html",
            {"request": request, "error": "El enlace para restablecer la contraseña es inválido o ha expirado."}
        )

    return templates.TemplateResponse("reset_password.html", {"request": request, "token": token})

@router.post("/reset-password/{token}")
async def process_reset_password(
    request: Request,
    token: str,
    password: str = Form(...),
    passwordConfirm: str = Form(...),
    db: Optional[Session] = Depends(get_db)
):
    # Verificar si la base de datos está disponible
    if db is None:
        return templates.TemplateResponse(
            "recuperar_password.html",
            {"request": request, "error": "El servicio de base de datos no está disponible en este momento. Por favor, inténtelo más tarde."}
        )

    # Verificar si las contraseñas coinciden
    if password != passwordConfirm:
        return templates.TemplateResponse(
            "reset_password.html",
            {"request": request, "token": token, "error": "Las contraseñas no coinciden"}
        )

    # Buscar usuario con este token
    user = db.query(User).filter(User.token_recuperacion == token).first()

    # Verificar si el token es válido y no ha expirado
    if not user or not user.fecha_expiracion_token or user.fecha_expiracion_token < datetime.now():
        return templates.TemplateResponse(
            "recuperar_password.html",
            {"request": request, "error": "El enlace para restablecer la contraseña es inválido o ha expirado."}
        )

    # Hashear la contraseña con bcrypt
    hashed_password = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')

    # Actualizar contraseña
    user.contrasena = hashed_password
    user.token_recuperacion = None
    user.fecha_expiracion_token = None
    db.commit()

    # Redirigir a la página de inicio de sesión con mensaje de éxito
    return templates.TemplateResponse(
        "login.html",
        {"request": request, "success": "Tu contraseña ha sido restablecida correctamente. Ya puedes iniciar sesión."}
    )

@router.get("/registro", response_class=HTMLResponse)
async def registro(
    request: Request,
    user_id: Optional[str] = Cookie(None),
    user_role: Optional[str] = Cookie(None)
):
    # Si el usuario ya está autenticado, redirigir a la página principal
    if user_id:
        return RedirectResponse(url="/", status_code=status.HTTP_303_SEE_OTHER)

    return templates.TemplateResponse("registro.html", {
        "request": request,
        "errors": {},
        "user_id": user_id,
        "user_role": user_role
    })

@router.post("/registro")
async def process_registro(
    request: Request,
    name: str = Form(...),
    email: str = Form(...),
    password: str = Form(...),
    passwordConfirm: str = Form(...),
    birthdate: str = Form(...),
    terms: str = Form(...),
    db: Optional[Session] = Depends(get_db)
):
    # Verificar si la base de datos está disponible
    if db is None:
        return templates.TemplateResponse(
            "registro.html",
            {"request": request, "error": "El servicio de base de datos no está disponible en este momento. Por favor, inténtelo más tarde."}
        )

    # Validar el nombre (no debe estar vacío)
    if not name.strip():
        return templates.TemplateResponse(
            "registro.html",
            {"request": request, "error": "Por favor, ingresa tu nombre completo."}
        )

    # Validar el formato del correo electrónico
    email_pattern = re.compile(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
    if not email_pattern.match(email):
        return templates.TemplateResponse(
            "registro.html",
            {"request": request, "error": "Por favor, ingresa un correo electrónico válido."}
        )

    # Verificar si el correo ya está registrado
    existing_user = db.query(User).filter(User.correo_electronico == email).first()
    if existing_user:
        return templates.TemplateResponse(
            "registro.html",
            {"request": request, "error": "Este correo electrónico ya está registrado."}
        )

    # Validar la longitud mínima de la contraseña
    if len(password) < 8:
        return templates.TemplateResponse(
            "registro.html",
            {"request": request, "error": "La contraseña debe tener al menos 8 caracteres."}
        )

    # Verificar si las contraseñas coinciden
    if password != passwordConfirm:
        return templates.TemplateResponse(
            "registro.html",
            {"request": request, "error": "Las contraseñas no coinciden."}
        )

    try:
        # Validar la fecha de nacimiento
        try:
            birth_date = datetime.fromisoformat(birthdate).date()
        except ValueError:
            return templates.TemplateResponse(
                "registro.html",
                {"request": request, "error": "Por favor, ingresa una fecha de nacimiento válida."}
            )

        # Verificar la edad (debe ser mayor de 18 años)
        today = datetime.now().date()
        age = today.year - birth_date.year - ((today.month, today.day) < (birth_date.month, birth_date.day))
        if age < 18:
            return templates.TemplateResponse(
                "registro.html",
                {"request": request, "error": "Debes ser mayor de 18 años para registrarte."}
            )

        # Verificar que se aceptaron los términos y condiciones
        if terms != "on":
            return templates.TemplateResponse(
                "registro.html",
                {"request": request, "error": "Debes aceptar los términos y condiciones para registrarte."}
            )

        # Hashear la contraseña con bcrypt
        hashed_password = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')

        # Separar el nombre completo en nombre y apellido
        nombre_completo = name.split()
        if len(nombre_completo) > 1:
            nombre = nombre_completo[0]
            apellido = ' '.join(nombre_completo[1:])
        else:
            nombre = name
            apellido = ""

        # Asignar avatar por defecto para nuevos usuarios
        fotografia_url = "/static/images/perfil icono.jpg"

        # Crear el nuevo usuario
        new_user = User(
            nombre=nombre,
            apellido=apellido,
            correo_electronico=email,
            contrasena=hashed_password,
            fotografia=fotografia_url,
            id_estados=1,  # Activo
            id_rol=2       # Usuario regular
        )

        # Guardar en la base de datos
        db.add(new_user)
        db.commit()
        db.refresh(new_user)

        # Establecer cookies de sesión automáticamente después del registro
        response = RedirectResponse(url="/", status_code=status.HTTP_303_SEE_OTHER)
        response.set_cookie(key="user_id", value=str(new_user.id_usuarios), httponly=True)
        response.set_cookie(key="user_role", value=str(new_user.id_rol), httponly=True)

        return response

    except Exception as e:
        if db:
            db.rollback()
        return templates.TemplateResponse(
            "registro.html",
            {"request": request, "error": f"Error al registrar usuario: {str(e)}"}
        )
