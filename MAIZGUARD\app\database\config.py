from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
import time

# Configuración de la conexión a MySQL
DATABASE_URL = "mysql+pymysql://root:@localhost:3306/maizguard"

# Crear el motor de SQLAlchemy con timeout reducido
engine = create_engine(
    DATABASE_URL,
    pool_pre_ping=True,
    connect_args={"connect_timeout": 1}
)

# Crear una sesión local
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Base para los modelos
Base = declarative_base()

# Variable para controlar si la base de datos está disponible
db_available = False

# Función para obtener la sesión de la base de datos
def get_db():
    global db_available

    # Si la base de datos no está disponible, devolvemos None
    if not db_available:
        try:
            # Intentar conectar con un timeout muy corto
            conn = engine.connect()
            conn.close()
            db_available = True
        except Exception:
            # Si falla, devolvemos None
            return None

    # Si llegamos aquí, la base de datos está disponible
    db = SessionLocal()
    try:
        yield db
    except Exception:
        # Si falla durante el uso, marcamos la base de datos como no disponible
        db_available = False
    finally:
        db.close()
