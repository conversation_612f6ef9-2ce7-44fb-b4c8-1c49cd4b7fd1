from databases import Database
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from sqlalchemy import MetaData

DATABASE_URL = "mysql+aiomysql://root:@127.0.0.1/MAIZGUARD"

# Conexión asíncrona con SQLAlchemy
engine = create_async_engine(DATABASE_URL, echo=True)
metadata = MetaData()

# Sesión asíncrona
async_session = sessionmaker(
    bind=engine,
    class_=AsyncSession,
    expire_on_commit=False
)

# Base de datos con 'databases'
database = Database(DATABASE_URL)

# 🔹 Agregar función para inicializar la BD
async def init_db():
    """Conectar a la base de datos al iniciar"""
    await database.connect()
    print("✅ Base de datos conectada correctamente")

async def close_db():
    """Cerrar conexión con la base de datos"""
    await database.disconnect()
    print("🔴 Conexión cerrada correctamente")
