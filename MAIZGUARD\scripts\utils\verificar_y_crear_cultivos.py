#!/usr/bin/env python3
"""
Script para verificar tablas existentes y crear la tabla de cultivos
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from app.database.config import engine
from sqlalchemy import text

def verificar_y_crear_cultivos():
    """Verificar tablas existentes y crear la tabla de cultivos"""
    try:
        with engine.connect() as conn:
            # Verificar qué tablas existen
            result = conn.execute(text("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'maizguard'
                ORDER BY table_name
            """))
            
            tablas_existentes = [row.table_name for row in result]
            
            print("📋 Tablas existentes en la base de datos:")
            for tabla in tablas_existentes:
                print(f"  📝 {tabla}")
            
            # Verificar si las tablas necesarias para las claves foráneas existen
            tablas_necesarias = ['regiones', 'temporadas', 'usuarios']
            tablas_faltantes = [tabla for tabla in tablas_necesarias if tabla not in tablas_existentes]
            
            if tablas_faltantes:
                print(f"\n⚠️  Faltan las siguientes tablas: {', '.join(tablas_faltantes)}")
                print("🔧 Creando tablas faltantes...")
                
                # Crear tabla regiones si no existe
                if 'regiones' in tablas_faltantes:
                    conn.execute(text("""
                        CREATE TABLE regiones (
                            id_region INT AUTO_INCREMENT PRIMARY KEY,
                            nombre VARCHAR(50) NOT NULL,
                            descripcion VARCHAR(255)
                        )
                    """))
                    
                    # Insertar regiones básicas
                    conn.execute(text("""
                        INSERT INTO regiones (id_region, nombre, descripcion) VALUES
                        (1, 'Norte', 'Región norte del país'),
                        (2, 'Centro', 'Región central del país'),
                        (3, 'Sur', 'Región sur del país'),
                        (4, 'Este', 'Región este del país'),
                        (5, 'Oeste', 'Región oeste del país')
                        ON DUPLICATE KEY UPDATE nombre = VALUES(nombre), descripcion = VALUES(descripcion)
                    """))
                    print("✅ Tabla 'regiones' creada.")
                
                # Crear tabla temporadas si no existe
                if 'temporadas' in tablas_faltantes:
                    conn.execute(text("""
                        CREATE TABLE temporadas (
                            id_temporada INT AUTO_INCREMENT PRIMARY KEY,
                            nombre VARCHAR(50) NOT NULL,
                            descripcion VARCHAR(255)
                        )
                    """))
                    
                    # Insertar temporadas básicas
                    conn.execute(text("""
                        INSERT INTO temporadas (id_temporada, nombre, descripcion) VALUES
                        (1, 'Seca', 'Temporada seca del año'),
                        (2, 'Lluviosa', 'Temporada lluviosa del año')
                        ON DUPLICATE KEY UPDATE nombre = VALUES(nombre), descripcion = VALUES(descripcion)
                    """))
                    print("✅ Tabla 'temporadas' creada.")
            
            # Verificar si la tabla cultivos existe
            if 'cultivos' not in tablas_existentes:
                print("\n🔧 Creando tabla 'cultivos'...")
                
                # Crear tabla cultivos
                conn.execute(text("""
                    CREATE TABLE cultivos (
                        id_cultivo INT AUTO_INCREMENT PRIMARY KEY,
                        nombre VARCHAR(100) NOT NULL,
                        variedad VARCHAR(100),
                        url_img VARCHAR(255),
                        descripcion TEXT NOT NULL,
                        cuidados TEXT,
                        id_region INT NOT NULL,
                        id_temporada INT NOT NULL,
                        id_usuarios INT NOT NULL,
                        fecha_registro DATETIME DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (id_region) REFERENCES regiones(id_region),
                        FOREIGN KEY (id_temporada) REFERENCES temporadas(id_temporada),
                        FOREIGN KEY (id_usuarios) REFERENCES usuarios(id_usuarios)
                    )
                """))
                
                print("✅ Tabla 'cultivos' creada exitosamente.")
            else:
                print("\n✅ La tabla 'cultivos' ya existe.")
            
            conn.commit()
            
            # Verificar estructura final
            result = conn.execute(text("DESCRIBE cultivos"))
            campos = result.fetchall()
            
            print("\n📋 Estructura de la tabla 'cultivos':")
            for campo in campos:
                print(f"  📝 {campo.Field}: {campo.Type}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    print("🌽 Verificando y creando estructura para cultivos...")
    verificar_y_crear_cultivos()
