#!/usr/bin/env python3
"""
Script simple para iniciar MAIZGUARD
"""

if __name__ == "__main__":
    print("🚀 INICIANDO MAIZGUARD...")
    
    try:
        import uvicorn
        print("✅ Uvicorn cargado")
        
        print("🌐 Servidor iniciando en http://127.0.0.1:8001")
        print("📋 Panel de admin: http://127.0.0.1:8001/admin")
        print("🔑 Login: http://127.0.0.1:8001/login")
        print("\nPresiona Ctrl+C para detener")
        print("=" * 50)
        
        uvicorn.run(
            "main:app",
            host="127.0.0.1",
            port=8001,
            reload=True
        )
        
    except KeyboardInterrupt:
        print("\n⏹️ Servidor detenido")
    except Exception as e:
        print(f"❌ Error: {e}")
