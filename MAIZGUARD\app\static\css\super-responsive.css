/* Estilos para mejorar la responsividad general del proyecto */

/* Ajustes generales para todos los tamaños de pantalla */
* {
    box-sizing: border-box;
}

body {
    overflow-x: hidden;
}

img {
    max-width: 100%;
    height: auto;
}

/* Ajustes para contenedores */
.container {
    width: 100%;
    max-width: 100%;
    padding-left: 5px;
    padding-right: 5px;
}

/* Ajustes para filas y columnas */
.row {
    margin-left: -5px;
    margin-right: -5px;
}

.col, .col-1, .col-2, .col-3, .col-4, .col-5, .col-6, 
.col-7, .col-8, .col-9, .col-10, .col-11, .col-12, 
.col-sm-1, .col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6, 
.col-sm-7, .col-sm-8, .col-sm-9, .col-sm-10, .col-sm-11, .col-sm-12, 
.col-md-1, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6, 
.col-md-7, .col-md-8, .col-md-9, .col-md-10, .col-md-11, .col-md-12, 
.col-lg-1, .col-lg-2, .col-lg-3, .col-lg-4, .col-lg-5, .col-lg-6, 
.col-lg-7, .col-lg-8, .col-lg-9, .col-lg-10, .col-lg-11, .col-lg-12, 
.col-xl-1, .col-xl-2, .col-xl-3, .col-xl-4, .col-xl-5, .col-xl-6, 
.col-xl-7, .col-xl-8, .col-xl-9, .col-xl-10, .col-xl-11, .col-xl-12 {
    padding-left: 5px;
    padding-right: 5px;
}

/* Ajustes para tarjetas */
.card {
    margin-bottom: 10px;
}

.card-body {
    padding: 0.5rem;
}

.card-title {
    font-size: 1.1rem;
    margin-bottom: 0.3rem;
}

.card-subtitle {
    font-size: 0.9rem;
    margin-bottom: 0.3rem;
}

.card-text {
    font-size: 0.85rem;
    margin-bottom: 0.5rem;
}

/* Ajustes para botones */
.btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.85rem;
}

/* Ajustes para badges */
.badge {
    font-size: 0.7rem;
    padding: 0.2em 0.5em;
}

/* Ajustes para alertas */
.alert {
    padding: 0.5rem;
    margin-bottom: 0.5rem;
    font-size: 0.85rem;
}

/* Ajustes para formularios */
.form-control {
    padding: 0.25rem 0.5rem;
    font-size: 0.85rem;
}

.form-label {
    margin-bottom: 0.25rem;
    font-size: 0.85rem;
}

/* Ajustes para pantallas medianas */
@media (max-width: 992px) {
    .card-title {
        font-size: 1rem;
    }
    
    .card-subtitle {
        font-size: 0.85rem;
    }
    
    .card-text {
        font-size: 0.8rem;
    }
    
    .btn {
        padding: 0.2rem 0.4rem;
        font-size: 0.8rem;
    }
}

/* Ajustes para pantallas pequeñas */
@media (max-width: 768px) {
    .container {
        padding-left: 3px;
        padding-right: 3px;
    }
    
    .row {
        margin-left: -3px;
        margin-right: -3px;
    }
    
    .col, .col-1, .col-2, .col-3, .col-4, .col-5, .col-6, 
    .col-7, .col-8, .col-9, .col-10, .col-11, .col-12, 
    .col-sm-1, .col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6, 
    .col-sm-7, .col-sm-8, .col-sm-9, .col-sm-10, .col-sm-11, .col-sm-12, 
    .col-md-1, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6, 
    .col-md-7, .col-md-8, .col-md-9, .col-md-10, .col-md-11, .col-md-12, 
    .col-lg-1, .col-lg-2, .col-lg-3, .col-lg-4, .col-lg-5, .col-lg-6, 
    .col-lg-7, .col-lg-8, .col-lg-9, .col-lg-10, .col-lg-11, .col-lg-12, 
    .col-xl-1, .col-xl-2, .col-xl-3, .col-xl-4, .col-xl-5, .col-xl-6, 
    .col-xl-7, .col-xl-8, .col-xl-9, .col-xl-10, .col-xl-11, .col-xl-12 {
        padding-left: 3px;
        padding-right: 3px;
    }
    
    .card-body {
        padding: 0.4rem;
    }
    
    .card-title {
        font-size: 0.95rem;
    }
    
    .card-subtitle {
        font-size: 0.8rem;
    }
    
    .card-text {
        font-size: 0.75rem;
    }
    
    .btn {
        padding: 0.15rem 0.3rem;
        font-size: 0.75rem;
    }
    
    .badge {
        font-size: 0.65rem;
        padding: 0.15em 0.4em;
    }
    
    .alert {
        padding: 0.4rem;
        font-size: 0.8rem;
    }
    
    .form-control {
        padding: 0.2rem 0.4rem;
        font-size: 0.8rem;
    }
}

/* Ajustes para pantallas muy pequeñas */
@media (max-width: 576px) {
    .container {
        padding-left: 2px;
        padding-right: 2px;
    }
    
    .row {
        margin-left: -2px;
        margin-right: -2px;
    }
    
    .col, .col-1, .col-2, .col-3, .col-4, .col-5, .col-6, 
    .col-7, .col-8, .col-9, .col-10, .col-11, .col-12, 
    .col-sm-1, .col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6, 
    .col-sm-7, .col-sm-8, .col-sm-9, .col-sm-10, .col-sm-11, .col-sm-12, 
    .col-md-1, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6, 
    .col-md-7, .col-md-8, .col-md-9, .col-md-10, .col-md-11, .col-md-12, 
    .col-lg-1, .col-lg-2, .col-lg-3, .col-lg-4, .col-lg-5, .col-lg-6, 
    .col-lg-7, .col-lg-8, .col-lg-9, .col-lg-10, .col-lg-11, .col-lg-12, 
    .col-xl-1, .col-xl-2, .col-xl-3, .col-xl-4, .col-xl-5, .col-xl-6, 
    .col-xl-7, .col-xl-8, .col-xl-9, .col-xl-10, .col-xl-11, .col-xl-12 {
        padding-left: 2px;
        padding-right: 2px;
    }
    
    .card-body {
        padding: 0.3rem;
    }
    
    .card-title {
        font-size: 0.9rem;
    }
    
    .card-subtitle {
        font-size: 0.75rem;
    }
    
    .card-text {
        font-size: 0.7rem;
    }
    
    .btn {
        padding: 0.1rem 0.25rem;
        font-size: 0.7rem;
    }
    
    .badge {
        font-size: 0.6rem;
        padding: 0.1em 0.3em;
    }
    
    .alert {
        padding: 0.3rem;
        font-size: 0.75rem;
    }
    
    .form-control {
        padding: 0.15rem 0.3rem;
        font-size: 0.75rem;
    }
}

/* Ajustes para pantallas extremadamente pequeñas */
@media (max-width: 375px) {
    .card-title {
        font-size: 0.85rem;
    }
    
    .card-subtitle {
        font-size: 0.7rem;
    }
    
    .card-text {
        font-size: 0.65rem;
    }
    
    .btn {
        padding: 0.05rem 0.2rem;
        font-size: 0.65rem;
    }
    
    .badge {
        font-size: 0.55rem;
        padding: 0.05em 0.25em;
    }
}
