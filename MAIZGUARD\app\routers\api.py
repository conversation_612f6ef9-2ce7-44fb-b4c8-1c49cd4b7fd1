from fastapi import APIRouter, Depends, HTTPException, status, Cookie, File, UploadFile, Form, Path
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
from sqlalchemy import text
from typing import List, Optional, Dict, Any
import os
import shutil
from datetime import datetime

from app.database.config import get_db
from app.database.models import User
from app.schemas.user import UserResponse

router = APIRouter(
    prefix="/api",
    tags=["api"],
    responses={404: {"description": "Not found"}},
)

@router.get("/")
async def api_root():
    return {"message": "¡Bienvenido a la API de MaizGuard!"}

@router.get("/users", response_model=List[UserResponse])
async def get_users(db: Session = Depends(get_db)):
    users = db.query(User).all()
    return users

@router.get("/users/{user_id}", response_model=UserResponse)
async def get_user(user_id: int, db: Session = Depends(get_db)):
    user = db.query(User).filter(User.id_usuarios == user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Usuario no encontrado"
        )
    return user

@router.post("/perfil/foto")
async def upload_profile_photo(
    foto: UploadFile = File(...),
    user_id: Optional[str] = Cookie(None),
    db: Session = Depends(get_db)
):
    # Verificar si el usuario está autenticado
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="No autenticado"
        )

    # Obtener información del usuario
    user = db.query(User).filter(User.id_usuarios == int(user_id)).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Usuario no encontrado"
        )

    try:
        # Validar el tipo de archivo
        if not foto.content_type.startswith("image/"):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="El archivo debe ser una imagen"
            )

        # Crear directorio si no existe
        upload_dir = os.path.join("app", "static", "uploads", "profile_pics")
        os.makedirs(upload_dir, exist_ok=True)

        # Eliminar foto anterior si existe
        if user.fotografia and os.path.exists(os.path.join("app", user.fotografia.lstrip("/"))):
            os.remove(os.path.join("app", user.fotografia.lstrip("/")))

        # Generar nombre de archivo único
        file_extension = os.path.splitext(foto.filename)[1]
        file_name = f"user_{user_id}_{datetime.now().strftime('%Y%m%d%H%M%S')}{file_extension}"
        file_path = os.path.join(upload_dir, file_name)

        # Guardar archivo
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(foto.file, buffer)

        # Actualizar ruta en la base de datos
        foto_url = f"/static/uploads/profile_pics/{file_name}"
        user.fotografia = foto_url
        db.commit()

        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={"message": "Foto de perfil actualizada correctamente", "foto_url": foto_url}
        )

    except Exception as e:
        if db:
            db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error al subir la foto: {str(e)}"
        )

@router.get("/plaga/{id_plaga}")
async def get_plaga_details(
    id_plaga: int = Path(..., title="ID de la plaga"),
    user_id: Optional[str] = Cookie(None),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    Obtiene los detalles de una plaga específica.
    Esta API es utilizada para cargar los detalles de una plaga en el modal del historial.
    """
    try:
        # Verificar si el usuario está autenticado
        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="No autenticado"
            )

        # Obtener información de la plaga
        query = text("""
            SELECT p.id_plaga, p.nombre, p.nombre_cientifico, p.url_img, p.descripcion, p.recomendaciones,
                   fc.descripcion as fase_cultivo, tp.descripcion as tipo_plaga, t.descripcion as temporada
            FROM plagas p
            LEFT JOIN fases_cultivo fc ON p.id_fase_cultivo = fc.id_fase_cultivo
            LEFT JOIN tipos_plaga tp ON p.id_tipo_plaga = tp.id_tipo_plaga
            LEFT JOIN temporadas t ON p.id_temporada = t.id_temporada
            WHERE p.id_plaga = :id_plaga
        """)

        result = db.execute(query, {"id_plaga": id_plaga})
        plaga = result.fetchone()

        if not plaga:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Plaga no encontrada"
            )

        # Convertir el resultado a un diccionario
        plaga_dict = dict(plaga._mapping)

        return {
            "success": True,
            "data": plaga_dict
        }

    except HTTPException as e:
        # Re-lanzar excepciones HTTP
        raise e
    except Exception as e:
        # Capturar cualquier otra excepción
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error al obtener información de la plaga: {str(e)}"
        )
