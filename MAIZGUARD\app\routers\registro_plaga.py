from fastapi import APIRouter, HTTPException, Depends
from typing import List, Optional
from pydantic import BaseModel
import aiomysql
from app.routers.fase_cultivo import conectar_db  # Reutilizar la función de conexión a la base de datos

router = APIRouter()

# Función para conectar a la base de datos
async def conectar_db():
    return await aiomysql.connect(
        host="localhost",
        port=3306,
        user="root",
        password="",
        db="MAIZGUARD"
    )


# Modelo para crear/actualizar un registro de plaga
class RegistroPlagaCreate(BaseModel):
    id_usuarios: int
    id_registro_cultivo: int
    id_plaga: int
    url_img: str
    descripcion: str

# Modelo para la respuesta de un registro de plaga
class RegistroPlaga(BaseModel):
    id_registro_plaga: int
    id_usuarios: int
    id_registro_cultivo: int
    id_plaga: int
    url_img: str
    descripcion: str
    fecha_hora: str

# Endpoint para registrar un nuevo registro de plaga
@router.post("/RegistrarRegistroPlaga", response_model=RegistroPlaga)
async def registrar_registro_plaga(registro: RegistroPlagaCreate):
    conexion = None
    try:
        conexion = await conectar_db()
        async with conexion.cursor() as cursor:
            # Insertar el nuevo registro de plaga
            query = """
                INSERT INTO registro_plaga (id_usuarios, id_registro_cultivo, id_plaga, url_img, descripcion)
                VALUES (%s, %s, %s, %s, %s)
            """
            valores = (
                registro.id_usuarios,
                registro.id_registro_cultivo,
                registro.id_plaga,
                registro.url_img,
                registro.descripcion,
            )
            await cursor.execute(query, valores)
            await conexion.commit()

            # Obtener el ID generado
            id_registro_plaga = cursor.lastrowid

        return RegistroPlaga(id_registro_plaga=id_registro_plaga, **registro.dict(), fecha_hora="")

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error al registrar el registro de plaga: {str(e)}")
    finally:
        if conexion:
            conexion.close()

# Endpoint para listar todos los registros de plagas
@router.get("/ListarRegistrosPlaga", response_model=List[RegistroPlaga])
async def listar_registros_plaga():
    conexion = None
    try:
        conexion = await conectar_db()
        async with conexion.cursor(aiomysql.DictCursor) as cursor:
            # Consultar todos los registros de plagas
            query = "SELECT * FROM registro_plaga"
            await cursor.execute(query)
            registros = await cursor.fetchall()

            # Convertir fecha_hora a string
            for registro in registros:
                registro["fecha_hora"] = str(registro["fecha_hora"])

        return registros

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error al listar los registros de plagas: {str(e)}")
    finally:
        if conexion:
            conexion.close()

# Endpoint para obtener un registro de plaga por ID
@router.get("/RegistroPlaga/{id_registro_plaga}", response_model=RegistroPlaga)
async def obtener_registro_plaga(id_registro_plaga: int):
    conexion = None
    try:
        conexion = await conectar_db()
        async with conexion.cursor(aiomysql.DictCursor) as cursor:
            # Consultar el registro de plaga por ID
            query = "SELECT * FROM registro_plaga WHERE id_registro_plaga = %s"
            await cursor.execute(query, (id_registro_plaga,))
            registro = await cursor.fetchone()

            if not registro:
                raise HTTPException(status_code=404, detail="Registro de plaga no encontrado")

            # Convertir fecha_hora a string
            registro["fecha_hora"] = str(registro["fecha_hora"])

        return registro

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error al obtener el registro de plaga: {str(e)}")
    finally:
        if conexion:
            conexion.close()