#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script para verificar la estructura de la tabla plaga.
"""

import os
import sys

# Añadir el directorio raíz al path para poder importar los módulos de la aplicación
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from app.database.config import engine
from sqlalchemy import text

def verificar_tabla_plaga():
    """
    Verifica la estructura de la tabla plaga.
    """
    try:
        with engine.connect() as conn:
            print("Verificando estructura de la tabla plaga...")
            
            # Obtener la estructura de la tabla
            result = conn.execute(text("DESCRIBE plaga"))
            
            print("\nEstructura de la tabla plaga:")
            for row in result:
                print(f"Campo: {row[0]}, Tipo: {row[1]}, Nulo: {row[2]}, <PERSON><PERSON><PERSON>: {row[3]}, Default: {row[4]}, Extra: {row[5]}")
            
            # Obtener los datos actuales
            result = conn.execute(text("SELECT * FROM plaga"))
            
            print("\nDatos actuales en la tabla plaga:")
            plagas = result.fetchall()
            if not plagas:
                print("No hay datos en la tabla plaga.")
            else:
                for plaga in plagas:
                    print(f"ID: {plaga[0]}, Nombre: {plaga[1]}")
            
    except Exception as e:
        print(f"Error al verificar la tabla plaga: {e}")

if __name__ == "__main__":
    verificar_tabla_plaga()
