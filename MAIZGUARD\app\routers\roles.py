from fastapi import APIRouter, HTTPException
from typing import List
from pydantic import BaseModel
import aiomysql
from app.routers.fase_cultivo import conectar_db  # Reutilizar la función de conexión a la base de datos

router = APIRouter()

# Función para conectar a la base de datos
async def conectar_db():
    return await aiomysql.connect(
        host="localhost",
        port=3306,
        user="root",
        password="",
        db="MAIZGUARD"
    )

# Modelo para la respuesta de roles
class Rol(BaseModel):
    id_rol: int
    nombre_rol: str

# Endpoint para listar todos los roles
@router.get("/ListarRoles", response_model=List[Rol])
async def listar_roles():
    conexion = None
    try:
        conexion = await conectar_db()
        async with conexion.cursor(aiomysql.DictCursor) as cursor:
            # Consultar todos los roles
            query = "SELECT id_rol, nombre_rol FROM roles"
            await cursor.execute(query)
            roles = await cursor.fetchall()

        return roles

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error al listar los roles: {str(e)}")
    finally:
        if conexion:
            conexion.close()

# Endpoint para obtener un rol por ID
@router.get("/Rol/{id_rol}", response_model=Rol)
async def obtener_rol(id_rol: int):
    conexion = None
    try:
        conexion = await conectar_db()
        async with conexion.cursor(aiomysql.DictCursor) as cursor:
            # Consultar el rol por ID
            query = "SELECT id_rol, nombre_rol FROM roles WHERE id_rol = %s"
            await cursor.execute(query, (id_rol,))
            rol = await cursor.fetchone()

            if not rol:
                raise HTTPException(status_code=404, detail="Rol no encontrado")

        return rol

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error al obtener el rol: {str(e)}")
    finally:
        if conexion:
            conexion.close()