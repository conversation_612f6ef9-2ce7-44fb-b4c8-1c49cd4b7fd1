#!/usr/bin/env python3
"""
Script para verificar la estructura de la tabla usuarios
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from app.database.config import engine
from sqlalchemy import text

def verificar_estructura():
    """Verificar la estructura de la tabla usuarios"""
    try:
        with engine.connect() as conn:
            # Mostrar estructura de la tabla usuarios
            result = conn.execute(text("DESCRIBE usuarios"))
            
            print("📋 Estructura de la tabla usuarios:")
            print("-" * 50)
            for row in result:
                print(f"  📝 {row.Field}: {row.Type}")
            
            print("\n👥 Usuarios existentes:")
            print("-" * 50)
            result = conn.execute(text("SELECT * FROM usuarios LIMIT 5"))
            
            for row in result:
                print(f"  🆔 ID: {row.id_usuarios}")
                for column in row._mapping.keys():
                    if column != 'id_usuarios':
                        print(f"     {column}: {getattr(row, column)}")
                print()
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    verificar_estructura()
