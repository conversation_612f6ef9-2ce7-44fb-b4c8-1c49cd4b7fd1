# app/main.py
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles

# Importaciones de nuestros módulos
from app.database.config import engine
from app.database.models import Base
from app.routers import auth, pages, api, admin, cultivos

# Crear las tablas en la base de datos solo si se puede conectar a la base de datos
try:
    Base.metadata.create_all(bind=engine)
    print("Conexión a la base de datos exitosa")
except Exception as e:
    print(f"Error al conectar a la base de datos: {e}")
    print("La aplicación funcionará sin conexión a la base de datos")

app = FastAPI(
    title="MaizGuard",
    description="Sistema para la gestión de cultivos de maíz",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Configuración de CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Agrega los dominios de tu frontend
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE"],
    allow_headers=["*"],
)

# Montar archivos estáticos
app.mount("/static", StaticFiles(directory="app/static"), name="static")

# Middleware para obtener información del usuario
from fastapi import Request
from fastapi.responses import Response
from sqlalchemy.orm import Session
from app.database.config import get_db
from app.database.models import User

@app.middleware("http")
async def add_user_info(request: Request, call_next):
    # Obtener cookies
    user_id = request.cookies.get("user_id")

    # Agregar información del usuario a la solicitud si está autenticado
    if user_id:
        try:
            db_gen = get_db()
            if db_gen is not None:
                db = next(db_gen)
                if db is not None:
                    user = db.query(User).filter(User.id_usuarios == int(user_id)).first()
                    request.state.user = user
                else:
                    request.state.user = None
            else:
                request.state.user = None
        except Exception as e:
            request.state.user = None
    else:
        request.state.user = None

    # Continuar con la solicitud
    response = await call_next(request)
    return response

# Incluir routers
app.include_router(pages.router)
app.include_router(auth.router)
app.include_router(api.router)
app.include_router(admin.router)
app.include_router(cultivos.router)
