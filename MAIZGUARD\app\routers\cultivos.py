from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Form, Cookie
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
from sqlalchemy import text
from typing import List, Optional
import os
import shutil
from datetime import datetime
import uuid

from app.database.config import get_db

# Función de dependencia para validar autenticación
async def get_current_user(user_id: Optional[str] = <PERSON><PERSON>(None)):
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Debes iniciar sesión para acceder a esta funcionalidad"
        )
    try:
        return int(user_id)
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="ID de usuario inválido"
        )

router = APIRouter(
    prefix="/api/cultivos",
    tags=["cultivos"],
    responses={404: {"description": "Not found"}},
)

# Modelo para cultivo
class Cultivo:
    def __init__(self, id_cultivo, nombre, variedad, url_img, descripcion, cuidados,
                 id_region, id_temporada, id_usuarios, fecha_registro,
                 region_nombre=None, temporada_nombre=None):
        self.id_cultivo = id_cultivo
        self.nombre = nombre
        self.variedad = variedad
        self.url_img = url_img
        self.descripcion = descripcion
        self.cuidados = cuidados
        self.id_region = id_region
        self.id_temporada = id_temporada
        self.id_usuarios = id_usuarios
        self.fecha_registro = fecha_registro
        self.region_nombre = region_nombre
        self.temporada_nombre = temporada_nombre

# Endpoint para obtener todos los cultivos de un usuario
@router.get("/usuario")
async def obtener_cultivos_usuario(
    current_user_id: int = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    try:
        # Consultar cultivos del usuario con la nueva estructura
        query = text("""
            SELECT
                id_cultivo, nombre, descripcion, cantidad, unidad_cantidad,
                ancho, altura, fase_cultivo, fecha_siembra, fecha_estimada_cosecha,
                ubicacion, notas, fecha_registro, fecha_actualizacion, activo
            FROM cultivos
            WHERE id_usuarios = :user_id AND activo = 1
            ORDER BY fecha_registro DESC
        """)

        result = db.execute(query, {"user_id": current_user_id})
        cultivos = []

        for row in result:
            cultivo = {
                "id_cultivo": row.id_cultivo,
                "nombre": row.nombre,
                "descripcion": row.descripcion,
                "cantidad": row.cantidad,
                "unidad_cantidad": row.unidad_cantidad,
                "ancho": float(row.ancho),
                "altura": float(row.altura),
                "fase_cultivo": row.fase_cultivo,
                "fecha_siembra": row.fecha_siembra.isoformat() if row.fecha_siembra else None,
                "fecha_estimada_cosecha": row.fecha_estimada_cosecha.isoformat() if row.fecha_estimada_cosecha else None,
                "ubicacion": row.ubicacion,
                "notas": row.notas,
                "fecha_registro": row.fecha_registro.isoformat() if row.fecha_registro else None,
                "fecha_actualizacion": row.fecha_actualizacion.isoformat() if row.fecha_actualizacion else None,
                "activo": row.activo
            }
            cultivos.append(cultivo)

        return cultivos

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error al obtener cultivos: {str(e)}"
        )

# Endpoint para obtener un cultivo específico
@router.get("/{id_cultivo}")
async def obtener_cultivo(
    id_cultivo: int,
    current_user_id: int = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    try:
        # Consultar cultivo con la nueva estructura y verificar que pertenece al usuario
        query = text("""
            SELECT
                id_cultivo, nombre, descripcion, cantidad, unidad_cantidad,
                ancho, altura, fase_cultivo, fecha_siembra, fecha_estimada_cosecha,
                ubicacion, notas, fecha_registro, fecha_actualizacion, activo
            FROM cultivos
            WHERE id_cultivo = :id_cultivo AND id_usuarios = :user_id AND activo = 1
        """)

        result = db.execute(query, {"id_cultivo": id_cultivo, "user_id": current_user_id})
        row = result.fetchone()

        if not row:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Cultivo no encontrado"
            )

        cultivo = {
            "id_cultivo": row.id_cultivo,
            "nombre": row.nombre,
            "descripcion": row.descripcion,
            "cantidad": row.cantidad,
            "unidad_cantidad": row.unidad_cantidad,
            "ancho": float(row.ancho),
            "altura": float(row.altura),
            "fase_cultivo": row.fase_cultivo,
            "fecha_siembra": row.fecha_siembra.isoformat() if row.fecha_siembra else None,
            "fecha_estimada_cosecha": row.fecha_estimada_cosecha.isoformat() if row.fecha_estimada_cosecha else None,
            "ubicacion": row.ubicacion,
            "notas": row.notas,
            "fecha_registro": row.fecha_registro.isoformat() if row.fecha_registro else None,
            "fecha_actualizacion": row.fecha_actualizacion.isoformat() if row.fecha_actualizacion else None,
            "activo": row.activo
        }

        return cultivo

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error al obtener cultivo: {str(e)}"
        )

# Endpoint para crear un nuevo cultivo
@router.post("/")
async def crear_cultivo(
    nombre: str = Form(...),
    descripcion: str = Form(...),
    cantidad: int = Form(...),
    unidad_cantidad: str = Form("plantas"),
    ancho: float = Form(...),
    altura: float = Form(...),
    fase_cultivo: str = Form(...),
    fecha_siembra: str = Form(None),
    fecha_estimada_cosecha: str = Form(None),
    ubicacion: str = Form(None),
    notas: str = Form(None),
    id_usuarios: int = Form(...),
    db: Session = Depends(get_db)
):
    try:
        # Insertar nuevo cultivo con la nueva estructura
        query = text("""
            INSERT INTO cultivos (
                nombre, descripcion, cantidad, unidad_cantidad, ancho, altura,
                fase_cultivo, fecha_siembra, fecha_estimada_cosecha, ubicacion,
                notas, id_usuarios
            ) VALUES (
                :nombre, :descripcion, :cantidad, :unidad_cantidad, :ancho, :altura,
                :fase_cultivo, :fecha_siembra, :fecha_estimada_cosecha, :ubicacion,
                :notas, :id_usuarios
            )
        """)

        db.execute(query, {
            "nombre": nombre,
            "descripcion": descripcion,
            "cantidad": cantidad,
            "unidad_cantidad": unidad_cantidad,
            "ancho": ancho,
            "altura": altura,
            "fase_cultivo": fase_cultivo,
            "fecha_siembra": fecha_siembra if fecha_siembra else None,
            "fecha_estimada_cosecha": fecha_estimada_cosecha if fecha_estimada_cosecha else None,
            "ubicacion": ubicacion if ubicacion else None,
            "notas": notas if notas else None,
            "id_usuarios": id_usuarios
        })

        db.commit()

        # Obtener el ID del cultivo recién creado
        last_id_query = text("SELECT LAST_INSERT_ID() as id")
        last_id_result = db.execute(last_id_query)
        id_cultivo = last_id_result.fetchone().id

        return {"mensaje": "Cultivo creado exitosamente", "id_cultivo": id_cultivo}

    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error al crear cultivo: {str(e)}"
        )

# Endpoint para actualizar un cultivo
@router.put("/{id_cultivo}")
async def actualizar_cultivo(
    id_cultivo: int,
    nombre: str,
    variedad: str,
    url_img: str,
    descripcion: str,
    cuidados: str,
    id_region: int,
    id_temporada: int,
    id_usuarios: int,
    db: Session = Depends(get_db)
):
    try:
        # Verificar si el cultivo existe
        verificar_query = text("SELECT id_cultivo FROM cultivos WHERE id_cultivo = :id_cultivo")
        result = db.execute(verificar_query, {"id_cultivo": id_cultivo})
        if not result.fetchone():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Cultivo no encontrado"
            )

        # Actualizar cultivo
        query = text("""
            UPDATE cultivos
            SET nombre = :nombre, variedad = :variedad, url_img = :url_img,
                descripcion = :descripcion, cuidados = :cuidados,
                id_region = :id_region, id_temporada = :id_temporada
            WHERE id_cultivo = :id_cultivo AND id_usuarios = :id_usuarios
        """)

        result = db.execute(query, {
            "nombre": nombre,
            "variedad": variedad,
            "url_img": url_img,
            "descripcion": descripcion,
            "cuidados": cuidados,
            "id_region": id_region,
            "id_temporada": id_temporada,
            "id_cultivo": id_cultivo,
            "id_usuarios": id_usuarios
        })

        db.commit()

        if result.rowcount == 0:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="No tienes permiso para actualizar este cultivo"
            )

        return {"mensaje": "Cultivo actualizado exitosamente"}

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error al actualizar cultivo: {str(e)}"
        )

# Endpoint para eliminar un cultivo
@router.delete("/{id_cultivo}")
async def eliminar_cultivo(id_cultivo: int, user_id: int, db: Session = Depends(get_db)):
    try:
        # Verificar si el cultivo existe y pertenece al usuario
        verificar_query = text("""
            SELECT id_cultivo FROM cultivos
            WHERE id_cultivo = :id_cultivo AND id_usuarios = :user_id
        """)
        result = db.execute(verificar_query, {"id_cultivo": id_cultivo, "user_id": user_id})
        if not result.fetchone():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Cultivo no encontrado o no tienes permiso para eliminarlo"
            )

        # Eliminar cultivo
        query = text("DELETE FROM cultivos WHERE id_cultivo = :id_cultivo")
        db.execute(query, {"id_cultivo": id_cultivo})
        db.commit()

        return {"mensaje": "Cultivo eliminado exitosamente"}

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error al eliminar cultivo: {str(e)}"
        )

# Endpoint para subir imagen de cultivo
@router.post("/upload")
async def upload_imagen_cultivo(imagen: UploadFile = File(...)):
    try:
        # Crear directorio si no existe
        upload_dir = "app/static/uploads/cultivos"
        os.makedirs(upload_dir, exist_ok=True)

        # Generar nombre único para la imagen
        extension = os.path.splitext(imagen.filename)[1]
        nuevo_nombre = f"{uuid.uuid4()}{extension}"
        ruta_archivo = os.path.join(upload_dir, nuevo_nombre)

        # Guardar imagen
        with open(ruta_archivo, "wb") as buffer:
            shutil.copyfileobj(imagen.file, buffer)

        # Devolver URL de la imagen
        url_imagen = f"/static/uploads/cultivos/{nuevo_nombre}"
        return {"url": url_imagen}

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error al subir imagen: {str(e)}"
        )
