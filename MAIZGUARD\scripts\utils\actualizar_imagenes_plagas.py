#!/usr/bin/env python3
"""
Script para actualizar las URLs de las imágenes de las plagas en la base de datos MaizGuard
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from app.database.config import engine
from sqlalchemy import text

def actualizar_imagenes_plagas():
    """Actualizar las URLs de las imágenes de las plagas"""
    try:
        with engine.connect() as conn:
            # Mapeo de nombres de plagas a archivos de imagen específicos
            mapeo_imagenes = {
                # INSECTOS
                "Escarabajo del Maíz": "/static/images/Escarabajo de maiz.png",
                "Gusano de la Mazorca": "/static/images/Gusano de la mazorca.png",
                "Mosca de la Mazorca": "/static/images/Mosca de la mazorca.jpg",
                "Escarabajo de la Espiga": "/static/images/Escarabajo espiga.jpeg",
                "Chinche Apestosa del Maíz": "/static/images/Chinche apestosa del maiz.jpg",
                "Picudo del Maíz": "/static/images/Picudo de maiz.jpeg",
                "Gorgojo de los Granos Almacenados": "/static/images/gorgojo de los granos almacenados.jpg",
                "Trips del Maíz": "/static/images/Trips del maiz.jpeg",
                "Cuca del Maíz": "/static/images/cuca del maiz(Chinche del maiz).jpg",
                "Gusano Trozador": "/static/images/Gusano trozador.jpg",
                "Araña Roja": "/static/images/araña-roja.jpg",
                "Gusano Cogollero": "/static/images/gusano-cogollero.jpg",
                "Trips": "/static/images/plaga-trips.jpg",
                
                # ÁCAROS
                "Ácaro del Tizón": "/static/images/acaro del tizon.jpg",
                "Ácaro Rojo del Maíz": "/static/images/Acaro rojo del maiz.jpeg",
                "Ácaro Blanco": "/static/images/Acaro blanco.jpg",
                "Ácaro de las Raíces": "/static/images/Acaro de las raices.jpeg",
                "Ácaro del Tetránico": "/static/images/acaros del tetranico.jpg",
                "Ácaro de las Espigas": "/static/images/Acaro de las espigas.jpg",
                
                # HONGOS
                "Moho de la Mazorca": "/static/images/Moho de la mazorca.jpg",
                "Aflatoxina": "/static/images/Aflatoxina.jpeg",
                "Pudrición del Tallo": "/static/images/Pudricion del tallo.jpeg",
                "Pudrición por Penicillium": "/static/images/Pudricion por penicillium.jpg",
                "Mancha de Alternaria": "/static/images/Mancha de alternaria.jpeg",
                "Pudrición de Mazorca": "/static/images/Pudricion de mazorca.png",
                "Pudrición de la Raíz": "/static/images/Pudricion de la raiz.png",
                "Manchas Negras": "/static/images/Manchas negras.png",
                "Moho Gris": "/static/images/Moho gris.jpg",
                
                # BACTERIAS
                "Mancha Bacteriana del Maíz": "/static/images/Mancha bacteriana del maiz.jpeg",
                "Podredumbre Blanda Bacteriana": "/static/images/Podredumbre blanda bacteriana.jpg",
                "Podredumbre Bacteriana del Maíz": "/static/images/Podredumbre bacteriana del maiz.jpeg",
                "Bacilo Cereus": "/static/images/Bacilo cereus.jpg",
                "Pseudomonas": "/static/images/Pseudomonas.jpg",
                "Xanthomonas": "/static/images/Xanthomonas.jpg",
                "Podredumbre Húmeda": "/static/images/Podredumbre húmeda.jpg",
                
                # VIRUS
                "Virus del Mosaico del Maíz": "/static/images/Virus del mosaico del maiz.jpg",
                "Virus del Rayado Fino del Maíz": "/static/images/Virus del rayado fino de maiz.jpg",
                "Virus del Mosaico de la Caña de Azúcar": "/static/images/virus mosaico caña azucar.jpg",
                "Virus de la Estría Rugosa del Maíz": "/static/images/Virus de la estria rugosa de maiz.jpg",
                "Virus del Rayado Clorótico del Maíz": "/static/images/Virus del Rayado Clorótico del Maíz.jpg",
                "Virus del Achaparramiento del Maíz": "/static/images/Virus del Achaparramiento del Maíz.jpg"
            }
            
            print("🖼️ Actualizando URLs de imágenes de plagas...")
            
            actualizadas = 0
            no_encontradas = 0
            
            for nombre_plaga, url_imagen in mapeo_imagenes.items():
                # Verificar si la plaga existe
                result = conn.execute(text("""
                    SELECT id_plaga FROM plagas WHERE nombre = :nombre
                """), {"nombre": nombre_plaga})
                
                plaga = result.fetchone()
                
                if plaga:
                    # Actualizar la URL de la imagen
                    conn.execute(text("""
                        UPDATE plagas 
                        SET url_img = :url_imagen 
                        WHERE nombre = :nombre
                    """), {
                        "url_imagen": url_imagen,
                        "nombre": nombre_plaga
                    })
                    
                    print(f"✅ '{nombre_plaga}' → {url_imagen}")
                    actualizadas += 1
                else:
                    print(f"⚠️  Plaga '{nombre_plaga}' no encontrada en la base de datos")
                    no_encontradas += 1
            
            conn.commit()
            
            print(f"\n📊 Resumen de actualización:")
            print(f"  ✅ Imágenes actualizadas: {actualizadas}")
            print(f"  ⚠️  Plagas no encontradas: {no_encontradas}")
            print(f"  🎯 Total procesadas: {len(mapeo_imagenes)}")
            
            # Verificar plagas sin imagen
            result = conn.execute(text("""
                SELECT nombre FROM plagas 
                WHERE (url_img IS NULL OR url_img = '' OR url_img LIKE '%placeholder%')
                AND id_fase_cultivo = 4 AND id_temporada = 1
                ORDER BY nombre
            """))
            
            plagas_sin_imagen = result.fetchall()
            
            if plagas_sin_imagen:
                print(f"\n🔍 Plagas que aún necesitan imagen ({len(plagas_sin_imagen)}):")
                for plaga in plagas_sin_imagen:
                    print(f"  📷 {plaga.nombre}")
            else:
                print(f"\n🎉 ¡Todas las plagas tienen imagen asignada!")
            
    except Exception as e:
        print(f"❌ Error al actualizar imágenes: {e}")

if __name__ == "__main__":
    print("🌽 Actualizando imágenes de plagas en MaizGuard...")
    actualizar_imagenes_plagas()
