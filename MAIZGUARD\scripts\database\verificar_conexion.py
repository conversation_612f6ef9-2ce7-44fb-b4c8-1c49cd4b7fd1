#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script para verificar la conexión a la base de datos y crear las tablas necesarias si no existen.
"""

import os
import sys
import pymysql
from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError
import time

# Añadir el directorio raíz al path para poder importar los módulos de la aplicación
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

# Importar la configuración de la base de datos
from app.database.config import DATABASE_URL, Base, engine

def verificar_conexion():
    """
    Verifica la conexión a la base de datos.
    """
    print("Verificando conexión a la base de datos...")
    try:
        # Intentar conectar con SQLAlchemy
        conn = engine.connect()
        conn.close()
        print("✅ Conexión exitosa a la base de datos usando SQLAlchemy.")
        return True
    except SQLAlchemyError as e:
        print(f"❌ Error al conectar a la base de datos usando SQLAlchemy: {e}")

        # Intentar conectar directamente con PyMySQL
        try:
            # Extraer los parámetros de conexión de DATABASE_URL
            # Formato: mysql+pymysql://usuario:contraseña@host:puerto/nombre_db
            parts = DATABASE_URL.replace('mysql+pymysql://', '').split('@')
            user_pass = parts[0].split(':')
            host_db = parts[1].split('/')

            user = user_pass[0]
            password = user_pass[1] if len(user_pass) > 1 else ''
            host = host_db[0].split(':')[0]
            port = int(host_db[0].split(':')[1]) if ':' in host_db[0] else 3306

            # Intentar conectar sin especificar la base de datos
            conn = pymysql.connect(
                host=host,
                user=user,
                password=password,
                port=port
            )
            conn.close()
            print("✅ Conexión exitosa al servidor MySQL usando PyMySQL.")

            # Verificar si la base de datos existe
            verificar_base_datos()
            return True
        except Exception as e:
            print(f"❌ Error al conectar directamente al servidor MySQL: {e}")
            print("\nPosibles soluciones:")
            print("1. Asegúrate de que el servicio MySQL de XAMPP esté en ejecución.")
            print("2. Verifica que las credenciales en app/database/config.py sean correctas.")
            print("3. Comprueba que el puerto 3306 esté disponible y no bloqueado por un firewall.")
            return False

def verificar_base_datos():
    """
    Verifica si la base de datos existe y la crea si no existe.
    """
    # Extraer el nombre de la base de datos de DATABASE_URL
    db_name = DATABASE_URL.split('/')[-1]

    try:
        # Conectar al servidor MySQL sin especificar la base de datos
        parts = DATABASE_URL.replace('mysql+pymysql://', '').split('@')
        user_pass = parts[0].split(':')
        host_db = parts[1].split('/')

        user = user_pass[0]
        password = user_pass[1] if len(user_pass) > 1 else ''
        host = host_db[0].split(':')[0]
        port = int(host_db[0].split(':')[1]) if ':' in host_db[0] else 3306

        conn = pymysql.connect(
            host=host,
            user=user,
            password=password,
            port=port
        )

        with conn.cursor() as cursor:
            # Verificar si la base de datos existe
            cursor.execute("SHOW DATABASES LIKE %s", (db_name,))
            result = cursor.fetchone()

            if not result:
                print(f"❌ La base de datos '{db_name}' no existe.")
                print(f"Creando la base de datos '{db_name}'...")
                cursor.execute(f"CREATE DATABASE {db_name} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
                conn.commit()
                print(f"✅ Base de datos '{db_name}' creada exitosamente.")
            else:
                print(f"✅ La base de datos '{db_name}' ya existe.")

        conn.close()

        # Verificar las tablas
        verificar_tablas(db_name)

    except Exception as e:
        print(f"❌ Error al verificar/crear la base de datos: {e}")

def verificar_tablas(db_name):
    """
    Verifica si las tablas necesarias existen y las crea si no existen.
    """
    try:
        # Conectar a la base de datos
        conn = engine.connect()

        # Verificar si las tablas existen
        result = conn.execute(text("SHOW TABLES"))
        tables = [row[0] for row in result]

        print("\nTablas existentes en la base de datos:")
        for table in tables:
            print(f"- {table}")

        # Si no hay tablas, crear las tablas usando SQLAlchemy
        if not tables:
            print("\nCreando tablas usando SQLAlchemy...")
            from app.database.models import Base
            Base.metadata.create_all(bind=engine)
            print("✅ Tablas creadas exitosamente.")

            # Insertar datos iniciales
            insertar_datos_iniciales(conn)
        else:
            # Verificar si las tablas necesarias existen
            required_tables = ["usuarios", "estados", "roles"]
            missing_tables = [table for table in required_tables if table not in tables]

            if missing_tables:
                print(f"\nFaltan las siguientes tablas: {', '.join(missing_tables)}")
                print("Creando tablas faltantes...")
                from app.database.models import Base
                Base.metadata.create_all(bind=engine)
                print("✅ Tablas faltantes creadas exitosamente.")

                # Insertar datos iniciales solo si faltan tablas importantes
                insertar_datos_iniciales(conn)
            else:
                print("\n✅ Todas las tablas necesarias existen.")

        conn.close()

    except Exception as e:
        print(f"❌ Error al verificar/crear las tablas: {e}")

def insertar_datos_iniciales(conn):
    """
    Inserta datos iniciales en las tablas.
    """
    try:
        # Insertar estados (activo, inactivo)
        conn.execute(text("""
            INSERT INTO estados (id_estados, descripcion) VALUES
            (1, 'Activo'),
            (2, 'Inactivo')
            ON DUPLICATE KEY UPDATE descripcion = VALUES(descripcion)
        """))

        # Insertar roles (administrador, usuario)
        conn.execute(text("""
            INSERT INTO roles (id_rol, nombre, descripcion) VALUES
            (1, 'Administrador', 'Administrador del sistema'),
            (2, 'Usuario', 'Usuario regular')
            ON DUPLICATE KEY UPDATE nombre = VALUES(nombre), descripcion = VALUES(descripcion)
        """))

        print("✅ Datos iniciales insertados exitosamente.")
    except Exception as e:
        print(f"❌ Error al insertar datos iniciales: {e}")

if __name__ == "__main__":
    if verificar_conexion():
        verificar_base_datos()
