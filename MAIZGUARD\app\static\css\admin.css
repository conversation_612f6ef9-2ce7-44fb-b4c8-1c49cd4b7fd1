/* Estilos para el panel de administración */

/* Espaciado para navbar fijo en admin */
.navbar-spacing {
    height: 80px;
}

/* Navbar normal en admin - anular TODOS los estilos compactos */
body.admin-page .navbar {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
    height: auto !important;
    min-height: 60px !important;
}

/* Anular estilos del fixed-navbar.css */
body.admin-page .rounded-logo {
    height: 42px !important;
    width: 42px !important;
    margin-right: 15px !important;
    left: -15px !important;
    border: 2px solid white !important;
    border-radius: 50% !important;
    object-fit: cover !important;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.2) !important;
    background-color: transparent !important;
    position: relative !important;
}

body.admin-page .navbar-brand {
    margin-left: -25px !important;
    font-size: 1.5rem !important;
    display: flex !important;
    align-items: center !important;
    font-weight: bold !important;
}

body.admin-page .nav-link {
    font-size: 1rem !important;
    padding: 0.5rem 0.9rem !important;
    margin: 0 2px !important;
    font-weight: 500 !important;
}

body.admin-page .navbar .form-control {
    height: 38px !important;
    font-size: 1rem !important;
    padding: 0.375rem 0.75rem !important;
    width: 200px !important;
    border-radius: 0.375rem 0 0 0.375rem !important;
}

body.admin-page .navbar .btn-light {
    height: 38px !important;
    width: 38px !important;
    padding: 0.375rem 0.75rem !important;
    border-radius: 0 0.375rem 0.375rem 0 !important;
}

/* Anular estilos de small-laptop.css y otros archivos */
body.admin-page .navbar-nav .nav-item {
    margin: 0 2px !important;
}

body.admin-page .navbar > .container {
    padding-left: 15px !important;
    padding-right: 15px !important;
}

/* Asegurar que el icono de perfil se vea correctamente */
body.admin-page .dropdown-toggle img {
    width: 32px !important;
    height: 32px !important;
    border-radius: 50% !important;
    margin-right: 8px !important;
}

body.admin-page .dropdown-toggle {
    display: flex !important;
    align-items: center !important;
    font-size: 1rem !important;
    padding: 0.5rem 0.9rem !important;
}

body.admin-page .dropdown-menu {
    font-size: 1rem !important;
}

body.admin-page .dropdown-item {
    font-size: 0.95rem !important;
    padding: 0.5rem 1rem !important;
}

/* Sidebar */
#sidebar {
    min-height: 100vh;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    z-index: 100;
}

.admin-sidebar {
    min-height: calc(100vh - 80px);
    margin-top: 0;
}

/* Contenido principal del admin */
.admin-main-content {
    margin-left: auto;
    padding-top: 1rem;
}

/* Responsive para admin */
@media (max-width: 768px) {
    body.admin-page .navbar {
        padding: 0.25rem 1rem !important;
    }

    body.admin-page .rounded-logo {
        height: 35px !important;
        width: 35px !important;
    }

    body.admin-page .navbar-brand {
        font-size: 1.25rem !important;
    }

    .admin-sidebar {
        min-height: 100vh;
    }

    .admin-main-content {
        margin-left: 0;
        padding-left: 1rem;
        padding-right: 1rem;
    }
}

.sidebar .nav-link {
    font-weight: 500;
    padding: 0.75rem 1rem;
    transition: all 0.3s;
}

.sidebar .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.sidebar .nav-link.active {
    background-color: rgba(255, 255, 255, 0.2);
    font-weight: 600;
}

.admin-logo {
    max-width: 100px;
    height: auto;
    border-radius: 5px;
    background-color: white;
    padding: 5px;
}

/* Tarjetas de resumen */
.border-left-primary {
    border-left: 4px solid #4e73df !important;
}

.border-left-success {
    border-left: 4px solid #1cc88a !important;
}

.border-left-info {
    border-left: 4px solid #36b9cc !important;
}

.border-left-warning {
    border-left: 4px solid #f6c23e !important;
}

.card {
    position: relative;
    display: flex;
    flex-direction: column;
    min-width: 0;
    word-wrap: break-word;
    background-color: #fff;
    background-clip: border-box;
    border: 1px solid #e3e6f0;
    border-radius: 0.35rem;
    transition: transform 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
}

/* Tarjetas de estadísticas con colores */
.card.bg-primary {
    background: linear-gradient(45deg, #007bff, #0056b3) !important;
    border: none;
}

.card.bg-success {
    background: linear-gradient(45deg, #28a745, #1e7e34) !important;
    border: none;
}

.card.bg-info {
    background: linear-gradient(45deg, #17a2b8, #117a8b) !important;
    border: none;
}

.card.bg-warning {
    background: linear-gradient(45deg, #ffc107, #e0a800) !important;
    border: none;
}

.card-body {
    flex: 1 1 auto;
    min-height: 1px;
    padding: 1.25rem;
}

.text-xs {
    font-size: 0.7rem;
}

.text-gray-300 {
    color: #dddfeb !important;
}

.text-gray-800 {
    color: #5a5c69 !important;
}

/* Actividad reciente */
.activity-feed {
    padding: 15px;
}

.activity-item {
    padding: 15px 0;
    border-bottom: 1px solid #f0f0f0;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-header {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
}

.activity-title {
    font-weight: 600;
    margin-left: 5px;
}

.activity-text {
    margin-bottom: 5px;
}

/* Tablas */
.table-responsive {
    overflow-x: auto;
}

.table th {
    background-color: #f8f9fc;
    font-weight: 600;
}

/* Formularios */
.form-floating > .form-control,
.form-floating > .form-select {
    height: calc(3.5rem + 2px);
    padding: 1rem 0.75rem;
}

.form-floating > label {
    padding: 1rem 0.75rem;
}

/* Botones */
.btn-success {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-success:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

/* Responsive */
@media (max-width: 767.98px) {
    #sidebar {
        position: fixed;
        top: 0;
        bottom: 0;
        left: 0;
        z-index: 1000;
        padding: 0;
        overflow-x: hidden;
        overflow-y: auto;
        width: 100%;
        max-width: 250px;
        transform: translateX(-100%);
        transition: transform 0.3s ease-in-out;
    }

    #sidebar.show {
        transform: translateX(0);
    }

    .sidebar-toggle {
        display: block;
    }
}

/* Badges */
.badge {
    padding: 0.35em 0.65em;
    font-size: 0.75em;
    font-weight: 600;
    border-radius: 0.25rem;
}

/* Paginación */
.pagination {
    margin-bottom: 0;
}

.page-link {
    color: var(--primary-color);
}

.page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Modales */
.modal-header {
    background-color: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
}

.modal-footer {
    background-color: #f8f9fc;
    border-top: 1px solid #e3e6f0;
}

/* Alertas */
.alert {
    border-radius: 0.35rem;
}

/* Tooltips */
.tooltip {
    font-size: 0.8rem;
}

/* Dropdown */
.dropdown-menu {
    font-size: 0.85rem;
    border-radius: 0.35rem;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

.dropdown-item:active {
    background-color: var(--primary-color);
}
