<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MAIZGUARD - Protección para tus cultivos</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome para iconos -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <!-- Estilos personalizados -->
    <link rel="stylesheet" href="/static/css/main.css">
    <link rel="stylesheet" href="/static/css/navbar.css">
    <link rel="stylesheet" href="/static/css/landing.css">
    <link rel="stylesheet" href="/static/css/compact.css">
    <link rel="stylesheet" href="/static/css/super-responsive.css">
    <link rel="stylesheet" href="/static/css/small-laptop.css">
    <link rel="stylesheet" href="/static/css/home-page.css">
    <!-- Vue.js -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>

    <!-- Estilos para las tarjetas de plagas encapsuladas -->
    <style>
        .plague-card-modern {
            border-radius: 0 0 15px 15px !important;
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .plague-card-modern:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
        }

        .plague-img-container {
            border-radius: 15px 15px 0 0;
            overflow: hidden;
            height: 200px;
        }

        .plague-img-modern {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .plague-card-modern:hover .plague-img-modern {
            transform: scale(1.05);
        }

        .plague-card-modern .card-body {
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
        }

        .plague-card-modern .btn-outline-success {
            border-width: 2px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .plague-card-modern .btn-outline-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
        }
    </style>
</head>
<body id="home-app">
    <!-- Incluir el navbar -->
    {% include "partials/navbar.html" %}

    <!-- Banner de administrador -->
    {% if user_role == '1' %}
    <div class="alert alert-primary alert-dismissible fade show m-0" role="alert">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h5 class="alert-heading mb-1">
                        <i class="fas fa-crown me-2"></i>Panel de Administración
                    </h5>
                    <p class="mb-0">Tienes privilegios de administrador. Gestiona usuarios, cultivos y plagas del sistema.</p>
                </div>
                <div class="col-md-4 text-md-end">
                    <a href="/admin" class="btn btn-primary me-2">
                        <i class="fas fa-cog me-2"></i>Ir al Panel
                    </a>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Espacio de separación después del navbar -->
    <div class="container-fluid py-3">
        <!-- Este div crea un espacio entre el navbar y el contenido -->
    </div>

    <!-- Sección de bienvenida para usuarios autenticados -->
    {% if user_id %}
    <section class="welcome-section py-4">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-12 text-center">
                    <h2 class="fw-bold text-success mb-3">¡Bienvenid@ {{ user.nombre if user else "" }}!</h2>
                    <p class="lead mb-0 text-dark">Consulta nuestra base de datos para identificar y tratar las plagas que afectan tus cultivos de maíz.</p>
                </div>
            </div>
        </div>
    </section>
    {% else %}
    <!-- Hero Section para visitantes -->
    <section class="hero-section-simple">
        <div class="container">
            <div class="row align-items-center py-5">
                <div class="col-lg-6 mb-4 mb-lg-0">
                    <div class="hero-content">
                        <h1 class="hero-title">MAIZGUARD</h1>
                        <p class="hero-subtitle">Consulta de Plagas para Cultivos de Maíz</p>
                        <p class="hero-description">
                            Plataforma web especializada en la identificación y tratamiento de plagas que afectan
                            los cultivos de maíz. Consulta nuestra base de datos y obtén recomendaciones específicas
                            según la temporada y fase de tu cultivo.
                        </p>
                        <div class="hero-buttons">
                            <a href="/registro" class="btn btn-hero-primary">
                                <i class="fas fa-user-plus me-2"></i>Comenzar Ahora
                            </a>
                            <a href="/acerca-de" class="btn btn-hero-secondary">
                                <i class="fas fa-info-circle me-2"></i>Conoce Más
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="hero-image">
                        <img src="/static/images/cultivos-de-maiz-de-alto-rendimiento.jpg" alt="Cultivos de Maíz" class="img-fluid rounded-3 shadow-lg">
                    </div>
                </div>
            </div>
        </div>
    </section>
    {% endif %}

    <!-- Carrusel con imágenes de cultivos -->
    <div class="container {% if user_id %}mt-4{% else %}my-5{% endif %}">
        <div id="multiImageCarousel" class="carousel slide mx-auto" data-bs-ride="carousel" data-bs-interval="4000" data-bs-touch="true">
            <div class="carousel-indicators">
                <button type="button" data-bs-target="#multiImageCarousel" data-bs-slide-to="0" class="active" aria-current="true" aria-label="Slide 1"></button>
                <button type="button" data-bs-target="#multiImageCarousel" data-bs-slide-to="1" aria-label="Slide 2"></button>
            </div>
            <div class="carousel-inner">
                <!-- Primer grupo de imágenes (3 imágenes) -->
                <div class="carousel-item active">
                    <div class="row mx-auto px-3 justify-content-center">
                        <div class="col-md-4 col-4 px-2">
                            <div class="card shadow-sm">
                                <img src="/static/images/masorca.jpg" class="card-img-top img-fluid" alt="Cultivo de Maíz 1">
                                <div class="card-body text-center p-2">
                                   
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 col-4 px-2">
                            <div class="card shadow-sm">
                                <img src="/static/images/tm.png" class="card-img-top img-fluid" alt="Cultivo de Maíz 2">
                                <div class="card-body text-center p-2">
                                    
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 col-4 px-2">
                            <div class="card shadow-sm">
                                <img src="/static/images/maiz-hibrido.jpg" class="card-img-top img-fluid" alt="Cultivo de Maíz 3">
                                <div class="card-body text-center p-2">
                                    
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Segundo grupo de imágenes (3 imágenes) -->
                <div class="carousel-item">
                    <div class="row mx-auto px-3 justify-content-center">
                        <div class="col-md-4 col-4 px-2">
                            <div class="card shadow-sm">
                                <img src="/static/images/maiz-dulce.jpg" class="card-img-top img-fluid" alt="Cultivo de Maíz 4">
                                <div class="card-body text-center p-2">
                                    
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 col-4 px-2">
                            <div class="card shadow-sm">
                                <img src="/static/images/ma.jpg" class="card-img-top img-fluid" alt="Cultivo de Maíz 5">
                                <div class="card-body text-center p-2">
                                  
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 col-4 px-2">
                            <div class="card shadow-sm">
                                <img src="/static/images/mejora.jpg" class="card-img-top img-fluid" alt="Cultivo de Maíz 6">
                                <div class="card-body text-center p-2">
                                    
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <button class="carousel-control-prev" type="button" data-bs-target="#multiImageCarousel" data-bs-slide="prev">
                <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                <span class="visually-hidden">Anterior</span>
            </button>
            <button class="carousel-control-next" type="button" data-bs-target="#multiImageCarousel" data-bs-slide="next">
                <span class="carousel-control-next-icon" aria-hidden="true"></span>
                <span class="visually-hidden">Siguiente</span>
            </button>
        </div>
    </div>



    <!-- Sección de Plagas más comunes -->
    <section class="container-fluid bg-light py-4 mt-3">
        <div class="container">
            <h2 class="text-center mb-4 text-success">Plagas más comunes</h2>
        </div>
    </section>

    <!-- Contenido principal: Plagas para usuarios autenticados -->
    {% if user_id and plagas_destacadas %}
    <main class="container my-5">
        <div class="row mb-4">
            <div class="col-12 text-center">
                <h2 class="text-success fw-bold mb-3">Plagas Comunes del Maíz</h2>
                <p class="lead text-muted">Consulta información sobre las principales plagas que afectan los cultivos de maíz</p>
            </div>
        </div>

        <div class="row g-4">
            {% for plaga in plagas_destacadas %}
            <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                <div class="card h-100 shadow-sm border-0 plague-card-modern">
                    <div class="plague-img-container">
                        <img src="{{ plaga.url_img }}" class="card-img-top plague-img-modern" alt="{{ plaga.nombre }}">
                    </div>
                    <div class="card-body text-center p-3">
                        <h5 class="card-title text-success fw-bold mb-2">{{ plaga.nombre }}</h5>
                        <p class="card-text text-muted small mb-3">{{ plaga.descripcion_corta }}...</p>
                        <a href="/plaga-publica/{{ plaga.id_plaga }}" class="btn btn-outline-success btn-sm">
                            <i class="fas fa-eye me-1"></i>Ver Información
                        </a>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>

        <div class="row mt-4">
            <div class="col-12 text-center">
                <a href="/plagas" class="btn btn-success btn-lg px-4">
                    <i class="fas fa-bug me-2"></i>Ver Todas las Plagas
                </a>
            </div>
        </div>
    </main>
    {% endif %}



    <!-- Incluir el footer -->
    {% include "partials/footer.html" %}

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js" defer></script>
    <!-- Custom JS -->
    <script src="/static/js/scripts.js" defer></script>

    <!-- Script para el carrusel -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Inicializar el carrusel con opciones personalizadas
            var multiImageCarousel = document.getElementById('multiImageCarousel');
            if (multiImageCarousel) {
                var carousel = new bootstrap.Carousel(multiImageCarousel, {
                    interval: 4000,  // Cambiar cada 4 segundos
                    wrap: true,      // Volver al principio cuando termine
                    keyboard: true,  // Permitir navegación con teclado
                    touch: true      // Permitir navegación táctil
                });
            }
        });
    </script>
</body>
</html>
