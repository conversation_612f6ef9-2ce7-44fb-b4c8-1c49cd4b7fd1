from fastapi import APIRouter, HTTPException
from typing import List
from pydantic import BaseModel
import aiomysql
from app.routers.fase_cultivo import conectar_db  # Reutilizar la función de conexión a la base de datos

router = APIRouter()

# Función para conectar a la base de datos
async def conectar_db():
    return await aiomysql.connect(
        host="localhost",
        port=3306,
        user="root",
        password="",
        db="MAIZGUARD"
    )
    

# Modelo para la respuesta de tipo de plaga
class TipoPlaga(BaseModel):
    id_tipo_plaga: int
    nombre: str

# Endpoint para listar todos los tipos de plagas
@router.get("/ListarTiposPlaga", response_model=List[TipoPlaga])
async def listar_tipos_plaga():
    conexion = None
    try:
        conexion = await conectar_db()
        async with conexion.cursor(aiomysql.DictCursor) as cursor:
            # Consultar todos los tipos de plagas
            query = "SELECT id_tipo_plaga, nombre FROM tipo_plaga"
            await cursor.execute(query)
            tipos_plaga = await cursor.fetchall()

        return tipos_plaga

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error al listar los tipos de plaga: {str(e)}")
    finally:
        if conexion:
            conexion.close()

# Endpoint para obtener un tipo de plaga por ID
@router.get("/TipoPlaga/{id_tipo_plaga}", response_model=TipoPlaga)
async def obtener_tipo_plaga(id_tipo_plaga: int):
    conexion = None
    try:
        conexion = await conectar_db()
        async with conexion.cursor(aiomysql.DictCursor) as cursor:
            # Consultar el tipo de plaga por ID
            query = "SELECT id_tipo_plaga, nombre FROM tipo_plaga WHERE id_tipo_plaga = %s"
            await cursor.execute(query, (id_tipo_plaga,))
            tipo_plaga = await cursor.fetchone()

            if not tipo_plaga:
                raise HTTPException(status_code=404, detail="Tipo de plaga no encontrado")

        return tipo_plaga

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error al obtener el tipo de plaga: {str(e)}")
    finally:
        if conexion:
            conexion.close()