// Script para la gestión de cultivos de usuario
document.addEventListener('DOMContentLoaded', function() {
    // Referencias a elementos del DOM
    const formCultivo = document.getElementById('formCultivo');
    const btnGuardarCultivo = document.getElementById('btnGuardarCultivo');
    const imagenCultivo = document.getElementById('imagenCultivo');
    const previewImagen = document.getElementById('previewImagen');
    const urlImgInput = document.getElementById('url_img');
    const btnConfirmarEliminar = document.getElementById('btnConfirmarEliminar');
    const btnEditarDesdeDetalle = document.getElementById('btnEditarDesdeDetalle');
    
    // Filtros
    const filtroRegion = document.getElementById('filtroRegion');
    const filtroTemporada = document.getElementById('filtroTemporada');
    const filtroBusqueda = document.getElementById('filtroBusqueda');
    const btnAplicarFiltros = document.getElementById('aplicarFiltros');
    const btnLimpiarFiltros = document.getElementById('limpiarFiltros');
    
    // Variables globales
    let cultivoIdEliminar = null;
    let cultivoIdEditar = null;
    let cultivos = [];
    
    // Inicializar modales de Bootstrap
    const modalNuevoCultivo = new bootstrap.Modal(document.getElementById('modalNuevoCultivo'));
    const modalDetalleCultivo = new bootstrap.Modal(document.getElementById('modalDetalleCultivo'));
    const modalConfirmarEliminar = new bootstrap.Modal(document.getElementById('modalConfirmarEliminar'));
    
    // Cargar cultivos al iniciar
    cargarCultivos();
    
    // Event Listeners
    if (btnGuardarCultivo) {
        btnGuardarCultivo.addEventListener('click', guardarCultivo);
    }
    
    if (imagenCultivo) {
        imagenCultivo.addEventListener('change', previewImagenCultivo);
    }
    
    if (btnConfirmarEliminar) {
        btnConfirmarEliminar.addEventListener('click', eliminarCultivo);
    }
    
    if (btnEditarDesdeDetalle) {
        btnEditarDesdeDetalle.addEventListener('click', function() {
            modalDetalleCultivo.hide();
            editarCultivo(cultivoIdEditar);
        });
    }
    
    if (btnAplicarFiltros) {
        btnAplicarFiltros.addEventListener('click', aplicarFiltros);
    }
    
    if (btnLimpiarFiltros) {
        btnLimpiarFiltros.addEventListener('click', limpiarFiltros);
    }
    
    // Función para cargar cultivos
    function cargarCultivos() {
        fetch('/api/cultivos/usuario')
            .then(response => {
                if (!response.ok) {
                    throw new Error('Error al cargar cultivos');
                }
                return response.json();
            })
            .then(data => {
                cultivos = data;
                actualizarListaCultivos(cultivos);
            })
            .catch(error => {
                console.error('Error:', error);
                mostrarAlerta('Error al cargar cultivos', 'danger');
            });
    }
    
    // Función para guardar cultivo (crear o actualizar)
    function guardarCultivo() {
        const formData = new FormData(formCultivo);
        const cultivoId = document.getElementById('cultivoId').value;
        
        // Determinar si es creación o actualización
        const url = cultivoId ? `/api/cultivos/${cultivoId}` : '/api/cultivos';
        const method = cultivoId ? 'PUT' : 'POST';
        
        // Convertir FormData a objeto JSON
        const cultivoData = {};
        formData.forEach((value, key) => {
            cultivoData[key] = value;
        });
        
        fetch(url, {
            method: method,
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(cultivoData)
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Error al guardar cultivo');
            }
            return response.json();
        })
        .then(data => {
            modalNuevoCultivo.hide();
            mostrarAlerta('Cultivo guardado exitosamente', 'success');
            cargarCultivos(); // Recargar la lista de cultivos
            resetearFormulario();
        })
        .catch(error => {
            console.error('Error:', error);
            mostrarAlerta('Error al guardar cultivo', 'danger');
        });
    }
    
    // Función para previsualizar imagen
    function previewImagenCultivo(event) {
        const file = event.target.files[0];
        if (file) {
            // Validar tipo de archivo
            if (!file.type.match('image.*')) {
                mostrarAlerta('Por favor, selecciona una imagen válida', 'warning');
                return;
            }
            
            // Validar tamaño (máximo 5MB)
            if (file.size > 5 * 1024 * 1024) {
                mostrarAlerta('La imagen es demasiado grande. Máximo 5MB', 'warning');
                return;
            }
            
            // Crear objeto FormData para subir la imagen
            const formData = new FormData();
            formData.append('imagen', file);
            
            // Subir imagen al servidor
            fetch('/api/upload/cultivo', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Error al subir imagen');
                }
                return response.json();
            })
            .then(data => {
                // Actualizar preview y campo oculto con la URL
                previewImagen.src = data.url;
                urlImgInput.value = data.url;
            })
            .catch(error => {
                console.error('Error:', error);
                mostrarAlerta('Error al subir imagen', 'danger');
                
                // Mostrar preview local mientras tanto
                const reader = new FileReader();
                reader.onload = function(e) {
                    previewImagen.src = e.target.result;
                };
                reader.readAsDataURL(file);
            });
        }
    }
    
    // Función para ver detalle de cultivo
    window.verDetalleCultivo = function(id) {
        const cultivo = cultivos.find(c => c.id_cultivo === id);
        if (!cultivo) return;
        
        cultivoIdEditar = id;
        
        // Construir HTML del detalle
        const detalle = `
            <div class="row">
                <div class="col-md-6">
                    <img src="${cultivo.url_img}" alt="${cultivo.nombre}" class="img-fluid rounded">
                </div>
                <div class="col-md-6">
                    <h3>${cultivo.nombre}</h3>
                    <p class="text-muted"><em>Variedad: ${cultivo.variedad}</em></p>
                    
                    <div class="mb-3">
                        <h5>Región</h5>
                        <p>${cultivo.region_nombre}</p>
                    </div>
                    
                    <div class="mb-3">
                        <h5>Temporada</h5>
                        <p>${cultivo.temporada_nombre}</p>
                    </div>
                    
                    <div class="mb-3">
                        <h5>Fecha de registro</h5>
                        <p>${new Date(cultivo.fecha_registro).toLocaleDateString()}</p>
                    </div>
                </div>
            </div>
            
            <div class="row mt-4">
                <div class="col-12">
                    <h5>Descripción</h5>
                    <p>${cultivo.descripcion}</p>
                </div>
            </div>
            
            <div class="row mt-3">
                <div class="col-12">
                    <h5>Cuidados</h5>
                    <p>${cultivo.cuidados}</p>
                </div>
            </div>
        `;
        
        document.getElementById('detalleCultivoContenido').innerHTML = detalle;
        modalDetalleCultivo.show();
    };
    
    // Función para editar cultivo
    window.editarCultivo = function(id) {
        const cultivo = cultivos.find(c => c.id_cultivo === id);
        if (!cultivo) return;
        
        // Llenar formulario con datos del cultivo
        document.getElementById('cultivoId').value = cultivo.id_cultivo;
        document.getElementById('nombre').value = cultivo.nombre;
        document.getElementById('variedad').value = cultivo.variedad;
        document.getElementById('region').value = cultivo.id_region;
        document.getElementById('temporada').value = cultivo.id_temporada;
        document.getElementById('descripcion').value = cultivo.descripcion;
        document.getElementById('cuidados').value = cultivo.cuidados;
        document.getElementById('url_img').value = cultivo.url_img;
        document.getElementById('previewImagen').src = cultivo.url_img;
        
        // Cambiar título del modal
        document.getElementById('modalNuevoCultivoLabel').textContent = 'Editar Cultivo';
        
        // Mostrar modal
        modalNuevoCultivo.show();
    };
    
    // Función para confirmar eliminación
    window.confirmarEliminarCultivo = function(id) {
        cultivoIdEliminar = id;
        modalConfirmarEliminar.show();
    };
    
    // Función para eliminar cultivo
    function eliminarCultivo() {
        if (!cultivoIdEliminar) return;
        
        fetch(`/api/cultivos/${cultivoIdEliminar}`, {
            method: 'DELETE'
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Error al eliminar cultivo');
            }
            return response.json();
        })
        .then(data => {
            modalConfirmarEliminar.hide();
            mostrarAlerta('Cultivo eliminado exitosamente', 'success');
            cargarCultivos(); // Recargar la lista de cultivos
        })
        .catch(error => {
            console.error('Error:', error);
            mostrarAlerta('Error al eliminar cultivo', 'danger');
        });
    }
    
    // Función para aplicar filtros
    function aplicarFiltros() {
        const region = filtroRegion.value;
        const temporada = filtroTemporada.value;
        const busqueda = filtroBusqueda.value.toLowerCase();
        
        const cultivosFiltrados = cultivos.filter(cultivo => {
            // Filtrar por región
            if (region && cultivo.id_region != region) return false;
            
            // Filtrar por temporada
            if (temporada && cultivo.id_temporada != temporada) return false;
            
            // Filtrar por búsqueda
            if (busqueda && !cultivo.nombre.toLowerCase().includes(busqueda) && 
                !cultivo.variedad.toLowerCase().includes(busqueda)) return false;
            
            return true;
        });
        
        actualizarListaCultivos(cultivosFiltrados);
    }
    
    // Función para limpiar filtros
    function limpiarFiltros() {
        filtroRegion.value = '';
        filtroTemporada.value = '';
        filtroBusqueda.value = '';
        actualizarListaCultivos(cultivos);
    }
    
    // Función para actualizar la lista de cultivos en la UI
    function actualizarListaCultivos(cultivosLista) {
        const contenedor = document.querySelector('.row-cols-1');
        if (!contenedor) return;
        
        // Limpiar contenedor
        contenedor.innerHTML = '';
        
        if (cultivosLista.length === 0) {
            contenedor.innerHTML = `
                <div class="col-12">
                    <div class="alert alert-info" role="alert">
                        <h4 class="alert-heading"><i class="fas fa-info-circle me-2"></i>No se encontraron cultivos</h4>
                        <p>No hay cultivos que coincidan con los criterios de búsqueda o aún no has registrado ningún cultivo.</p>
                    </div>
                </div>
            `;
            return;
        }
        
        // Agregar cada cultivo
        cultivosLista.forEach(cultivo => {
            const card = document.createElement('div');
            card.className = 'col cultivo-card';
            card.dataset.region = cultivo.id_region;
            card.dataset.temporada = cultivo.id_temporada;
            card.dataset.nombre = `${cultivo.nombre.toLowerCase()} ${cultivo.variedad.toLowerCase()}`;
            
            card.innerHTML = `
                <div class="card h-100 shadow-sm">
                    <div class="position-relative">
                        <img src="${cultivo.url_img}" class="card-img-top" alt="${cultivo.nombre}" style="height: 200px; object-fit: cover;">
                        <div class="position-absolute top-0 end-0 p-2">
                            <span class="badge bg-success">${cultivo.region_nombre}</span>
                        </div>
                    </div>
                    <div class="card-body">
                        <h5 class="card-title">${cultivo.nombre}</h5>
                        <h6 class="card-subtitle mb-2 text-muted">Variedad: ${cultivo.variedad}</h6>
                        <p class="card-text">${truncateText(cultivo.descripcion, 100)}</p>
                        <div class="d-flex justify-content-between align-items-center">
                            <small class="text-muted">
                                <i class="fas fa-calendar-alt me-1"></i> ${cultivo.temporada_nombre}
                            </small>
                            <small class="text-muted">
                                <i class="fas fa-clock me-1"></i> ${new Date(cultivo.fecha_registro).toLocaleDateString()}
                            </small>
                        </div>
                    </div>
                    <div class="card-footer bg-transparent border-top-0">
                        <div class="d-flex justify-content-between">
                            <button type="button" class="btn btn-sm btn-outline-success" 
                                    onclick="verDetalleCultivo(${cultivo.id_cultivo})">
                                <i class="fas fa-eye me-1"></i> Ver detalles
                            </button>
                            <div>
                                <button type="button" class="btn btn-sm btn-outline-primary me-1" 
                                        onclick="editarCultivo(${cultivo.id_cultivo})">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-danger" 
                                        onclick="confirmarEliminarCultivo(${cultivo.id_cultivo})">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            contenedor.appendChild(card);
        });
    }
    
    // Función para resetear formulario
    function resetearFormulario() {
        formCultivo.reset();
        document.getElementById('cultivoId').value = '';
        document.getElementById('url_img').value = '/static/images/maiz-default.jpg';
        document.getElementById('previewImagen').src = '/static/images/maiz-default.jpg';
        document.getElementById('modalNuevoCultivoLabel').textContent = 'Registrar Nuevo Cultivo';
    }
    
    // Función para mostrar alertas
    function mostrarAlerta(mensaje, tipo) {
        const alertaHTML = `
            <div class="alert alert-${tipo} alert-dismissible fade show" role="alert">
                ${mensaje}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `;
        
        const alertaContainer = document.createElement('div');
        alertaContainer.className = 'container mt-3';
        alertaContainer.innerHTML = alertaHTML;
        
        document.body.insertBefore(alertaContainer, document.body.firstChild);
        
        // Auto-cerrar después de 5 segundos
        setTimeout(() => {
            const alerta = alertaContainer.querySelector('.alert');
            if (alerta) {
                const bsAlerta = new bootstrap.Alert(alerta);
                bsAlerta.close();
            }
        }, 5000);
    }
    
    // Función para truncar texto
    function truncateText(text, maxLength) {
        if (!text) return '';
        return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
    }
});
