<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Recuperar Contraseña - MAIZGUARD</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <!-- Estilos personalizados -->
    <link rel="stylesheet" href="/static/css/main.css">
    <link rel="stylesheet" href="/static/css/compact.css">
    <link rel="stylesheet" href="/static/css/fixed-navbar.css">
    <link rel="stylesheet" href="/static/css/super-responsive.css">
    <link rel="stylesheet" href="/static/css/small-laptop.css">
    <link rel="stylesheet" href="/static/css/navbar.css">
    <link rel="preload" href="/static/images/Logo m.png" as="image">
</head>
<body class="d-flex flex-column min-vh-100">
    <!-- Incluir el navbar -->
    {% include "partials/navbar.html" %}

    <!-- Espacio de separación después del navbar -->
    <div class="container-fluid py-2">
        <!-- Este div crea un espacio entre el navbar y el contenido -->
    </div>

    <!-- Formulario de Recuperación de Contraseña -->
    <section class="py-5">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-8 col-lg-5">
                    <div class="card shadow-lg border-0 rounded-lg">
                        <div class="card-header bg-success text-white text-center py-4">
                            <h3 class="mb-0 fw-bold">Recuperar Contraseña</h3>
                        </div>
                        {% if error %}
                        <div class="alert alert-danger alert-dismissible fade show m-3" role="alert">
                            {{ error }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                        {% endif %}
                        {% if success %}
                        <div class="alert alert-success alert-dismissible fade show m-3" role="alert">
                            {{ success }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                        {% endif %}
                        <div class="card-body p-4">
                            <p class="text-center mb-4">Ingresa tu correo electrónico y te enviaremos instrucciones para restablecer tu contraseña.</p>
                            <form action="/recuperar-password" method="post">
                                <div class="form-floating mb-3">
                                    <input class="form-control" id="inputEmail" type="email" name="email" placeholder="<EMAIL>" required />
                                    <label for="inputEmail">Correo electrónico</label>
                                </div>
                                <div class="d-grid">
                                    <button class="btn btn-success btn-lg" type="submit">Enviar instrucciones</button>
                                </div>
                            </form>
                        </div>
                        <div class="card-footer text-center py-3">
                            <div class="small">
                                <a href="/login" class="text-success"><i class="fas fa-arrow-left me-1"></i> Volver a Iniciar Sesión</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Incluir el footer -->
    {% include "partials/footer.html" %}

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js" defer></script>
    <!-- Scripts personalizados -->
    <script src="/static/js/scripts.js" defer></script>
    <script src="/static/js/script.js" defer></script>
</body>
</html>
