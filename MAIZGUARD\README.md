# MaizGuard

Sistema para la gestión de cultivos de maíz.

## Estructura del Proyecto

```
MaizGuard/
├── app/                    # Aplicación principal
│   ├── database/           # Configuración y modelos de base de datos
│   ├── models/             # Modelos de datos
│   ├── routers/            # Rutas de la API
│   ├── schemas/            # Esquemas de validación
│   ├── static/             # Archivos estáticos (CSS, JS, imágenes)
│   ├── templates/          # Plantillas HTML
│   └── utils/              # Utilidades
├── scripts/                # Scripts de utilidad
│   ├── database/           # Scripts relacionados con la base de datos
│   ├── updates/            # Scripts para actualizar datos
│   └── utils/              # Scripts de utilidad general
├── .env                    # Variables de entorno
├── main.py                 # Punto de entrada de la aplicación
├── README.md               # Este archivo
└── requirements.txt        # Dependencias del proyecto
```

## Requisitos

- Python 3.8+
- MySQL 8.0+
- Dependencias listadas en requirements.txt

## Instalación

1. Clonar el repositorio
2. Crear un entorno virtual: `python -m venv venv`
3. Activar el entorno virtual:
   - Windows: `venv\Scripts\activate`
   - Linux/Mac: `source venv/bin/activate`
4. Instalar dependencias: `pip install -r requirements.txt`
5. Configurar variables de entorno en el archivo `.env`
6. Inicializar la base de datos: `python scripts/database/setup_database.py`
7. Ejecutar la aplicación: `python main.py`

## Uso

Para iniciar el servidor:

```bash
python main.py
```

Para crear un usuario administrador:

```bash
python scripts/utils/crear_admin.py
```

## Licencia

[Especificar licencia]