@echo off
echo Eliminando usuarios de la base de datos MAIZGUARD...
echo.

REM Ruta al cliente MySQL de XAMPP
set MYSQL_PATH=C:\xampp\mysql\bin\mysql.exe

REM Verificar si el cliente MySQL existe
if not exist "%MYSQL_PATH%" (
    echo Error: No se encontró el cliente MySQL en %MYSQL_PATH%
    echo Asegúrate de que XAMPP esté instalado correctamente.
    pause
    exit /b 1
)

REM Ejecutar el script SQL
echo Ejecutando script SQL...
"%MYSQL_PATH%" -u root < delete_users.sql

if %ERRORLEVEL% neq 0 (
    echo Error al ejecutar el script SQL.
    echo Asegúrate de que el servicio MySQL esté en ejecución.
    pause
    exit /b 1
)

echo.
echo Usuarios eliminados correctamente.
echo.
pause
