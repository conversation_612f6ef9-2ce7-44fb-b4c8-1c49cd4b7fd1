#!/usr/bin/env python3
"""
Script para crear la tabla de cultivos completa con todos los campos necesarios
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from app.database.config import engine
from sqlalchemy import text

def crear_tabla_cultivos_completa():
    """Crear la tabla de cultivos con todos los campos necesarios"""
    try:
        with engine.connect() as conn:
            print("🌽 Creando tabla de cultivos completa...")
            
            # Eliminar tabla si existe para recrearla
            conn.execute(text("DROP TABLE IF EXISTS cultivos"))
            
            # Crear tabla cultivos con todos los campos
            conn.execute(text("""
                CREATE TABLE cultivos (
                    id_cultivo INT AUTO_INCREMENT PRIMARY KEY,
                    nombre VARCHAR(100) NOT NULL COMMENT 'Nombre del cultivo',
                    descripcion TEXT NOT NULL COMMENT 'Descripción detallada del cultivo',
                    cantidad INT NOT NULL COMMENT 'Cantidad de plantas o área cultivada',
                    unidad_cantidad ENUM('plantas', 'hectareas', 'metros_cuadrados') DEFAULT 'plantas' COMMENT 'Unidad de medida para la cantidad',
                    ancho DECIMAL(10,2) NOT NULL COMMENT 'Ancho del área de cultivo en metros',
                    altura DECIMAL(10,2) NOT NULL COMMENT 'Altura del área de cultivo en metros',
                    fase_cultivo ENUM('germinacion', 'crecimiento', 'floracion', 'maduracion', 'cosecha') NOT NULL COMMENT 'Fase actual del cultivo',
                    fecha_siembra DATE COMMENT 'Fecha de siembra del cultivo',
                    fecha_estimada_cosecha DATE COMMENT 'Fecha estimada de cosecha',
                    ubicacion VARCHAR(255) COMMENT 'Ubicación del cultivo',
                    notas TEXT COMMENT 'Notas adicionales del usuario',
                    id_usuarios INT NOT NULL COMMENT 'ID del usuario propietario',
                    fecha_registro DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT 'Fecha de registro en el sistema',
                    fecha_actualizacion DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Fecha de última actualización',
                    activo BOOLEAN DEFAULT TRUE COMMENT 'Estado del cultivo (activo/inactivo)',
                    INDEX idx_usuario (id_usuarios),
                    INDEX idx_fase (fase_cultivo),
                    INDEX idx_fecha_registro (fecha_registro)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Tabla para almacenar los cultivos de los usuarios'
            """))
            
            conn.commit()
            print("✅ Tabla 'cultivos' creada exitosamente.")
            
            # Verificar estructura de la tabla
            result = conn.execute(text("DESCRIBE cultivos"))
            campos = result.fetchall()
            
            print("\n📋 Estructura de la tabla 'cultivos':")
            print("-" * 80)
            for campo in campos:
                print(f"  📝 {campo.Field:<25} | {campo.Type:<30} | {campo.Null:<5} | {campo.Key:<5} | {campo.Default}")
            
            # Insertar algunos datos de ejemplo
            print("\n🌱 Insertando datos de ejemplo...")
            
            # Obtener un usuario para los ejemplos
            result = conn.execute(text("SELECT id_usuarios FROM usuarios WHERE id_rol = 2 LIMIT 1"))
            user = result.fetchone()
            
            if user:
                user_id = user.id_usuarios
                
                ejemplos = [
                    {
                        "nombre": "Maíz Amarillo",
                        "descripcion": "Cultivo de maíz amarillo para consumo familiar. Variedad criolla adaptada al clima local.",
                        "cantidad": 50,
                        "unidad_cantidad": "plantas",
                        "ancho": 10.5,
                        "altura": 8.0,
                        "fase_cultivo": "maduracion",
                        "fecha_siembra": "2024-01-15",
                        "ubicacion": "Parcela Norte",
                        "notas": "Riego cada 3 días. Aplicar fertilizante orgánico mensualmente."
                    },
                    {
                        "nombre": "Maíz Blanco",
                        "descripcion": "Cultivo de maíz blanco para venta local. Variedad mejorada con alta resistencia a plagas.",
                        "cantidad": 2,
                        "unidad_cantidad": "hectareas",
                        "ancho": 100.0,
                        "altura": 200.0,
                        "fase_cultivo": "cosecha",
                        "fecha_siembra": "2023-12-01",
                        "ubicacion": "Campo Sur",
                        "notas": "Listo para cosecha. Excelente desarrollo y sin presencia de plagas."
                    },
                    {
                        "nombre": "Maíz Dulce",
                        "descripcion": "Cultivo experimental de maíz dulce para mercado gourmet.",
                        "cantidad": 500,
                        "unidad_cantidad": "metros_cuadrados",
                        "ancho": 20.0,
                        "altura": 25.0,
                        "fase_cultivo": "floracion",
                        "fecha_siembra": "2024-02-10",
                        "ubicacion": "Invernadero 1",
                        "notas": "Requiere cuidado especial. Monitoreo diario de humedad."
                    }
                ]
                
                for ejemplo in ejemplos:
                    conn.execute(text("""
                        INSERT INTO cultivos (
                            nombre, descripcion, cantidad, unidad_cantidad, ancho, altura,
                            fase_cultivo, fecha_siembra, ubicacion, notas, id_usuarios
                        ) VALUES (
                            :nombre, :descripcion, :cantidad, :unidad_cantidad, :ancho, :altura,
                            :fase_cultivo, :fecha_siembra, :ubicacion, :notas, :id_usuarios
                        )
                    """), {**ejemplo, "id_usuarios": user_id})
                
                conn.commit()
                print(f"✅ Insertados {len(ejemplos)} cultivos de ejemplo para el usuario ID: {user_id}")
            else:
                print("⚠️  No se encontró ningún usuario para insertar ejemplos.")
            
            # Verificar datos insertados
            result = conn.execute(text("""
                SELECT id_cultivo, nombre, fase_cultivo, cantidad, unidad_cantidad, 
                       CONCAT(ancho, 'x', altura, ' m') as dimensiones
                FROM cultivos 
                ORDER BY fecha_registro DESC
            """))
            
            cultivos = result.fetchall()
            
            if cultivos:
                print("\n🌽 Cultivos registrados:")
                print("-" * 80)
                for cultivo in cultivos:
                    print(f"  🌱 ID: {cultivo.id_cultivo} | {cultivo.nombre} | Fase: {cultivo.fase_cultivo} | Cantidad: {cultivo.cantidad} {cultivo.unidad_cantidad} | Dimensiones: {cultivo.dimensiones}")
            
            print(f"\n🎉 ¡Tabla de cultivos creada exitosamente con {len(cultivos)} registros!")
            
    except Exception as e:
        print(f"❌ Error al crear tabla de cultivos: {e}")

if __name__ == "__main__":
    print("🌽 Creando tabla de cultivos completa para MaizGuard...")
    print("=" * 60)
    crear_tabla_cultivos_completa()
