#!/usr/bin/env python3
"""
Script para generar avatares SVG con iniciales y colores
"""

import os

def generar_avatar_svg(inicial, color_fondo, color_texto, nombre_archivo):
    """Generar un avatar SVG con inicial y colores específicos"""
    
    svg_content = f'''<svg width="200" height="200" xmlns="http://www.w3.org/2000/svg">
  <circle cx="100" cy="100" r="100" fill="{color_fondo}"/>
  <text x="100" y="125" font-family="Arial, sans-serif" font-size="80" font-weight="bold" 
        text-anchor="middle" fill="{color_texto}">{inicial}</text>
</svg>'''
    
    # Crear directorio si no existe
    avatar_dir = "app/static/images/avatares"
    os.makedirs(avatar_dir, exist_ok=True)
    
    # Escribir archivo SVG
    file_path = os.path.join(avatar_dir, nombre_archivo)
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(svg_content)
    
    print(f"✅ Avatar generado: {file_path}")

def generar_avatares_predeterminados():
    """Generar una colección de avatares predeterminados"""
    
    avatares = [
        # Avatares con iniciales comunes
        {"inicial": "A", "color_fondo": "#28a745", "color_texto": "white", "archivo": "avatar-inicial-a.svg"},
        {"inicial": "B", "color_fondo": "#007bff", "color_texto": "white", "archivo": "avatar-inicial-b.svg"},
        {"inicial": "C", "color_fondo": "#dc3545", "color_texto": "white", "archivo": "avatar-inicial-c.svg"},
        {"inicial": "D", "color_fondo": "#fd7e14", "color_texto": "white", "archivo": "avatar-inicial-d.svg"},
        {"inicial": "E", "color_fondo": "#6f42c1", "color_texto": "white", "archivo": "avatar-inicial-e.svg"},
        {"inicial": "F", "color_fondo": "#20c997", "color_texto": "white", "archivo": "avatar-inicial-f.svg"},
        {"inicial": "G", "color_fondo": "#6c757d", "color_texto": "white", "archivo": "avatar-inicial-g.svg"},
        {"inicial": "H", "color_fondo": "#e83e8c", "color_texto": "white", "archivo": "avatar-inicial-h.svg"},
        {"inicial": "I", "color_fondo": "#17a2b8", "color_texto": "white", "archivo": "avatar-inicial-i.svg"},
        {"inicial": "J", "color_fondo": "#ffc107", "color_texto": "#212529", "archivo": "avatar-inicial-j.svg"},
        {"inicial": "K", "color_fondo": "#343a40", "color_texto": "white", "archivo": "avatar-inicial-k.svg"},
        {"inicial": "L", "color_fondo": "#795548", "color_texto": "white", "archivo": "avatar-inicial-l.svg"},
        {"inicial": "M", "color_fondo": "#607d8b", "color_texto": "white", "archivo": "avatar-inicial-m.svg"},
        {"inicial": "N", "color_fondo": "#ff5722", "color_texto": "white", "archivo": "avatar-inicial-n.svg"},
        {"inicial": "O", "color_fondo": "#9c27b0", "color_texto": "white", "archivo": "avatar-inicial-o.svg"},
        {"inicial": "P", "color_fondo": "#3f51b5", "color_texto": "white", "archivo": "avatar-inicial-p.svg"},
        {"inicial": "Q", "color_fondo": "#009688", "color_texto": "white", "archivo": "avatar-inicial-q.svg"},
        {"inicial": "R", "color_fondo": "#4caf50", "color_texto": "white", "archivo": "avatar-inicial-r.svg"},
        {"inicial": "S", "color_fondo": "#ff9800", "color_texto": "white", "archivo": "avatar-inicial-s.svg"},
        {"inicial": "T", "color_fondo": "#f44336", "color_texto": "white", "archivo": "avatar-inicial-t.svg"},
        {"inicial": "U", "color_fondo": "#2196f3", "color_texto": "white", "archivo": "avatar-inicial-u.svg"},
        {"inicial": "V", "color_fondo": "#8bc34a", "color_texto": "white", "archivo": "avatar-inicial-v.svg"},
        {"inicial": "W", "color_fondo": "#cddc39", "color_texto": "#212529", "archivo": "avatar-inicial-w.svg"},
        {"inicial": "X", "color_fondo": "#ffeb3b", "color_texto": "#212529", "archivo": "avatar-inicial-x.svg"},
        {"inicial": "Y", "color_fondo": "#ffc107", "color_texto": "#212529", "archivo": "avatar-inicial-y.svg"},
        {"inicial": "Z", "color_fondo": "#ff5722", "color_texto": "white", "archivo": "avatar-inicial-z.svg"},
        
        # Avatares especiales
        {"inicial": "👤", "color_fondo": "#6c757d", "color_texto": "white", "archivo": "avatar-usuario.svg"},
        {"inicial": "🌱", "color_fondo": "#28a745", "color_texto": "white", "archivo": "avatar-agricultor.svg"},
        {"inicial": "🌽", "color_fondo": "#ffc107", "color_texto": "#212529", "archivo": "avatar-maiz.svg"},
        {"inicial": "🔬", "color_fondo": "#007bff", "color_texto": "white", "archivo": "avatar-cientifico.svg"},
    ]
    
    print("🎨 Generando avatares SVG...")
    
    for avatar in avatares:
        generar_avatar_svg(
            avatar["inicial"],
            avatar["color_fondo"],
            avatar["color_texto"],
            avatar["archivo"]
        )
    
    print(f"\n🎉 Se generaron {len(avatares)} avatares SVG exitosamente!")
    print("📁 Ubicación: app/static/images/avatares/")

if __name__ == "__main__":
    print("🌟 Generador de Avatares SVG para MaizGuard")
    print("=" * 50)
    generar_avatares_predeterminados()
