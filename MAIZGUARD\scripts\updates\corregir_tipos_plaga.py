from app.database.config import engine
from sqlalchemy import text

def corregir_tipos_plaga():
    """
    Corrige los tipos de plaga para asegurar que sean: Insecto, Ácaro, Hongo, Bacteria y Virus.
    """
    try:
        with engine.connect() as conn:
            print("Verificando tipos de plaga actuales...")
            
            # Verificar tipos de plaga actuales
            result = conn.execute(text("SELECT id_tipo_plaga, descripcion FROM tipos_plaga ORDER BY id_tipo_plaga"))
            tipos_actuales = result.fetchall()
            
            print("Tipos de plaga actuales:")
            for tipo in tipos_actuales:
                print(f"ID: {tipo[0]}, Descripción: {tipo[1]}")
            
            # Actualizar los tipos de plaga
            print("\nActualizando tipos de plaga...")
            
            # Primero, actualizar todas las plagas a un tipo temporal (ID 1)
            conn.execute(text("UPDATE plagas SET id_tipo_plaga = 1"))
            
            # Ahora podemos actualizar los tipos sin restricciones de clave foránea
            conn.execute(text("UPDATE tipos_plaga SET descripcion = 'Insecto' WHERE id_tipo_plaga = 1"))
            
            # Verificar si existe el tipo con ID 2
            result = conn.execute(text("SELECT COUNT(*) FROM tipos_plaga WHERE id_tipo_plaga = 2"))
            if result.scalar() > 0:
                conn.execute(text("UPDATE tipos_plaga SET descripcion = 'Ácaro' WHERE id_tipo_plaga = 2"))
            else:
                conn.execute(text("INSERT INTO tipos_plaga (id_tipo_plaga, descripcion) VALUES (2, 'Ácaro')"))
            
            # Verificar si existe el tipo con ID 3
            result = conn.execute(text("SELECT COUNT(*) FROM tipos_plaga WHERE id_tipo_plaga = 3"))
            if result.scalar() > 0:
                conn.execute(text("UPDATE tipos_plaga SET descripcion = 'Hongo' WHERE id_tipo_plaga = 3"))
            else:
                conn.execute(text("INSERT INTO tipos_plaga (id_tipo_plaga, descripcion) VALUES (3, 'Hongo')"))
            
            # Verificar si existe el tipo con ID 4
            result = conn.execute(text("SELECT COUNT(*) FROM tipos_plaga WHERE id_tipo_plaga = 4"))
            if result.scalar() > 0:
                conn.execute(text("UPDATE tipos_plaga SET descripcion = 'Bacteria' WHERE id_tipo_plaga = 4"))
            else:
                conn.execute(text("INSERT INTO tipos_plaga (id_tipo_plaga, descripcion) VALUES (4, 'Bacteria')"))
            
            # Verificar si existe el tipo con ID 5
            result = conn.execute(text("SELECT COUNT(*) FROM tipos_plaga WHERE id_tipo_plaga = 5"))
            if result.scalar() > 0:
                conn.execute(text("UPDATE tipos_plaga SET descripcion = 'Virus' WHERE id_tipo_plaga = 5"))
            else:
                conn.execute(text("INSERT INTO tipos_plaga (id_tipo_plaga, descripcion) VALUES (5, 'Virus')"))
            
            # Eliminar tipos adicionales si existen
            conn.execute(text("DELETE FROM tipos_plaga WHERE id_tipo_plaga > 5"))
            
            # Actualizar las plagas con los tipos correctos
            print("\nActualizando categorías de plagas...")
            
            # Gusano cogollero y Gusano elotero: Insecto
            conn.execute(text("""
                UPDATE plagas SET id_tipo_plaga = 1
                WHERE nombre LIKE '%Gusano%'
            """))
            
            # Araña roja: Ácaro
            conn.execute(text("""
                UPDATE plagas SET id_tipo_plaga = 2
                WHERE nombre LIKE '%Araña%'
            """))
            
            # Roya del maíz y Tizón foliar: Hongo
            conn.execute(text("""
                UPDATE plagas SET id_tipo_plaga = 3
                WHERE nombre LIKE '%Roya%' OR nombre LIKE '%Tizón%'
            """))
            
            # Confirmar cambios
            conn.commit()
            
            # Mostrar tipos de plaga actualizados
            print("\nTIPOS DE PLAGA CORREGIDOS:")
            result = conn.execute(text("SELECT * FROM tipos_plaga ORDER BY id_tipo_plaga"))
            tipos = result.fetchall()
            for tipo in tipos:
                print(f"ID: {tipo[0]}, Descripción: {tipo[1]}")
            
            # Mostrar plagas actualizadas
            print("\nPLAGAS ACTUALIZADAS:")
            result = conn.execute(text("""
                SELECT p.id_plaga, p.nombre, tp.descripcion as tipo
                FROM plagas p
                LEFT JOIN tipos_plaga tp ON p.id_tipo_plaga = tp.id_tipo_plaga
                ORDER BY p.id_plaga
            """))
            plagas = result.fetchall()
            for plaga in plagas:
                print(f"ID: {plaga[0]}, Nombre: {plaga[1]}, Tipo: {plaga[2]}")
            
    except Exception as e:
        print(f"Error al corregir tipos de plaga: {e}")

if __name__ == "__main__":
    corregir_tipos_plaga()
