from app.database.config import engine
from sqlalchemy import text

def verificar_tabla_usuarios():
    """
    Verifica la estructura de la tabla usuarios.
    """
    try:
        with engine.connect() as conn:
            # Obtener la estructura de la tabla
            result = conn.execute(text("DESCRIBE usuarios"))
            columnas = result.fetchall()
            
            print("Estructura de la tabla usuarios:")
            for columna in columnas:
                print(f"Columna: {columna[0]}, Tipo: {columna[1]}, Nulo: {columna[2]}, Clave: {columna[3]}, Default: {columna[4]}, Extra: {columna[5]}")
            
            # Verificar si existe la columna 'rol'
            tiene_rol = any(columna[0] == 'rol' for columna in columnas)
            
            if not tiene_rol:
                print("\nLa columna 'rol' no existe en la tabla usuarios.")
                
                # Preguntar si se desea agregar la columna
                respuesta = input("¿Desea agregar la columna 'rol' a la tabla usuarios? (s/n): ")
                
                if respuesta.lower() == 's':
                    # Agregar la columna 'rol'
                    conn.execute(text("ALTER TABLE usuarios ADD COLUMN rol VARCHAR(20) DEFAULT 'usuario'"))
                    conn.commit()
                    print("Columna 'rol' agregada exitosamente.")
                    
                    # Verificar nuevamente la estructura
                    result = conn.execute(text("DESCRIBE usuarios"))
                    columnas = result.fetchall()
                    
                    print("\nNueva estructura de la tabla usuarios:")
                    for columna in columnas:
                        print(f"Columna: {columna[0]}, Tipo: {columna[1]}, Nulo: {columna[2]}, Clave: {columna[3]}, Default: {columna[4]}, Extra: {columna[5]}")
            else:
                print("\nLa columna 'rol' ya existe en la tabla usuarios.")
            
    except Exception as e:
        print(f"Error al verificar la tabla usuarios: {e}")

if __name__ == "__main__":
    verificar_tabla_usuarios()
