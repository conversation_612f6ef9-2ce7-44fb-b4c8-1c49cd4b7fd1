# 🌽 MaizGuard - Funcionalidades de Administrador

## 👑 Usuario Administrador: <PERSON>

**Email:** <EMAIL>  
**Rol:** SuperAdmin (ID: 1)  
**Estado:** ✅ Activo

---

## 🎯 Funcionalidades Implementadas

### 🔐 **1. Autenticación y Seguridad**
- ✅ **Acceso exclusivo:** Solo usuarios con `id_rol = 1` pueden acceder al panel
- ✅ **Validación automática:** Verificación de permisos en cada solicitud
- ✅ **Protección de rutas:** Todas las rutas `/admin/*` están protegidas
- ✅ **Enlace en navbar:** Solo visible para administradores

### 🌽 **2. Gestión de Cultivos de Usuarios**
**Ruta:** `/admin/cultivos-usuarios`

**Funcionalidades:**
- ✅ **Ver todos los cultivos** registrados por usuarios
- ✅ **Estadísticas en tiempo real:**
  - Total de cultivos
  - Cultivos en maduración
  - Cultivos en cosecha
  - Usuarios activos con cultivos
- ✅ **Filtros avanzados:**
  - Búsqueda por nombre o usuario
  - Filtro por fase (maduración/cosecha)
  - Filtro por usuario específico
- ✅ **Información detallada:**
  - Datos del cultivo (nombre, descripción, cantidad, dimensiones)
  - Información del propietario (nombre, email)
  - Fechas de registro y siembra
  - Ubicación y notas
- ✅ **Acciones disponibles:**
  - Ver detalles completos del cultivo
  - Contactar al usuario por email

### 👥 **3. Gestión de Usuarios**
**Ruta:** `/admin/usuarios`

**Funcionalidades:**
- ✅ **Ver todos los usuarios** registrados en el sistema
- ✅ **Información completa:**
  - Datos personales (nombre, apellido, email)
  - Rol actual (Administrador/Usuario)
  - Estado (Activo/Inactivo)
  - Fechas de registro y actualización
- ✅ **Cambiar roles:**
  - Convertir usuarios en administradores
  - Quitar privilegios de administrador
  - Protección: No se puede quitar el rol al último admin
- ✅ **Gestionar estados:**
  - Activar/desactivar usuarios
  - Protección: No se puede desactivar al último admin activo
- ✅ **Ver detalles:** Modal con información completa del usuario

### 🐛 **4. Gestión de Plagas** (Existente)
**Ruta:** `/admin/plagas`

**Funcionalidades:**
- ✅ **Ver base de datos de plagas**
- ✅ **Registrar nuevas plagas**
- ✅ **Editar información de plagas**
- ✅ **Gestionar imágenes y descripciones**

### 📊 **5. Panel de Control (Dashboard)**
**Ruta:** `/admin`

**Funcionalidades:**
- ✅ **Estadísticas generales** del sistema
- ✅ **Usuarios recientes**
- ✅ **Actividades del sistema**
- ✅ **Navegación rápida** a todas las secciones

---

## 🌐 **Accesos del Administrador**

### **URLs Principales:**
- **Panel principal:** http://127.0.0.1:8000/admin
- **Gestión de usuarios:** http://127.0.0.1:8000/admin/usuarios
- **Cultivos de usuarios:** http://127.0.0.1:8000/admin/cultivos-usuarios
- **Gestión de plagas:** http://127.0.0.1:8000/admin/plagas
- **Configuración:** http://127.0.0.1:8000/admin/configuracion

### **APIs Disponibles:**
- `GET /admin/api/usuarios` - Obtener todos los usuarios
- `PUT /admin/api/usuarios/{id}/rol` - Cambiar rol de usuario
- `PUT /admin/api/usuarios/{id}/estado` - Cambiar estado de usuario
- `GET /admin/api/cultivos-usuarios` - Obtener cultivos de usuarios
- `GET /admin/api/cultivos-usuarios/{id}` - Obtener cultivo específico
- `GET /admin/api/estadisticas` - Estadísticas del dashboard

---

## 🔧 **Características Técnicas**

### **Seguridad:**
- Validación de autenticación en cada endpoint
- Verificación de rol de administrador
- Protección contra acciones destructivas (último admin)
- Manejo de errores y excepciones

### **Interfaz:**
- Diseño responsive con Bootstrap 5
- Iconos Font Awesome
- Modales para detalles y confirmaciones
- Notificaciones en tiempo real
- Filtros y búsquedas dinámicas

### **Base de Datos:**
- Consultas optimizadas con JOIN
- Manejo de fechas y timestamps
- Transacciones seguras para actualizaciones
- Validaciones de integridad

---

## 🎯 **Casos de Uso Principales**

### **1. Supervisión de Cultivos**
Maria puede ver todos los cultivos registrados por los usuarios, filtrar por fase o usuario específico, y contactar a los usuarios si necesita más información.

### **2. Gestión de Permisos**
Maria puede convertir usuarios normales en administradores o viceversa, siempre manteniendo al menos un administrador activo en el sistema.

### **3. Moderación de Usuarios**
Maria puede activar o desactivar cuentas de usuarios según sea necesario, manteniendo el control sobre quién puede acceder al sistema.

### **4. Administración de Contenido**
Maria puede gestionar la base de datos de plagas, agregando nuevas plagas o editando información existente.

---

## ✅ **Estado de Implementación**

**🟢 Completamente Funcional:**
- Autenticación y autorización
- Gestión de cultivos de usuarios
- Gestión de usuarios y roles
- Cambio de estados de usuarios
- Interfaz de administración
- APIs protegidas

**🟡 Funcional (Existente):**
- Gestión de plagas
- Dashboard básico

**🔵 Listo para Uso:**
El sistema está completamente operativo y Maria Romero puede acceder a todas las funcionalidades de administración desde su cuenta.

---

## 📞 **Soporte**

Para cualquier consulta sobre las funcionalidades de administración, contactar al equipo de desarrollo de MaizGuard.
