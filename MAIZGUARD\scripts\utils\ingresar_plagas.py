from app.database.config import engine
from sqlalchemy import text

def ingresar_plagas_ejemplo():
    """
    Ingresa plagas de ejemplo en la base de datos.
    """
    try:
        with engine.connect() as conn:
            # Verificar si ya hay plagas registradas
            result = conn.execute(text("SELECT COUNT(*) FROM plagas"))
            num_plagas = result.scalar()
            
            if num_plagas > 0:
                print(f"Ya hay {num_plagas} plagas registradas en la base de datos.")
                return
            
            # Verificar si existen las tablas necesarias
            result = conn.execute(text("SHOW TABLES LIKE 'fases_cultivo'"))
            if not result.fetchone():
                print("La tabla 'fases_cultivo' no existe. Creando tabla...")
                conn.execute(text("""
                    CREATE TABLE IF NOT EXISTS fases_cultivo (
                        id_fase_cultivo INT AUTO_INCREMENT PRIMARY KEY,
                        descripcion VARCHAR(100) NOT NULL
                    )
                """))
                
                # Insertar fases de cultivo básicas
                conn.execute(text("INSERT INTO fases_cultivo (descripcion) VALUES ('Germinación')"))
                conn.execute(text("INSERT INTO fases_cultivo (descripcion) VALUES ('Crecimiento vegetativo')"))
                conn.execute(text("INSERT INTO fases_cultivo (descripcion) VALUES ('Floración')"))
                conn.execute(text("INSERT INTO fases_cultivo (descripcion) VALUES ('Fructificación')"))
                conn.execute(text("INSERT INTO fases_cultivo (descripcion) VALUES ('Maduración')"))
                print("Tabla 'fases_cultivo' creada y datos básicos insertados.")
            
            result = conn.execute(text("SHOW TABLES LIKE 'tipos_plaga'"))
            if not result.fetchone():
                print("La tabla 'tipos_plaga' no existe. Creando tabla...")
                conn.execute(text("""
                    CREATE TABLE IF NOT EXISTS tipos_plaga (
                        id_tipo_plaga INT AUTO_INCREMENT PRIMARY KEY,
                        descripcion VARCHAR(100) NOT NULL
                    )
                """))
                
                # Insertar tipos de plaga básicos
                conn.execute(text("INSERT INTO tipos_plaga (descripcion) VALUES ('Insecto')"))
                conn.execute(text("INSERT INTO tipos_plaga (descripcion) VALUES ('Hongo')"))
                conn.execute(text("INSERT INTO tipos_plaga (descripcion) VALUES ('Bacteria')"))
                conn.execute(text("INSERT INTO tipos_plaga (descripcion) VALUES ('Virus')"))
                conn.execute(text("INSERT INTO tipos_plaga (descripcion) VALUES ('Maleza')"))
                print("Tabla 'tipos_plaga' creada y datos básicos insertados.")
            
            result = conn.execute(text("SHOW TABLES LIKE 'temporadas'"))
            if not result.fetchone():
                print("La tabla 'temporadas' no existe. Creando tabla...")
                conn.execute(text("""
                    CREATE TABLE IF NOT EXISTS temporadas (
                        id_temporada INT AUTO_INCREMENT PRIMARY KEY,
                        descripcion VARCHAR(100) NOT NULL
                    )
                """))
                
                # Insertar temporadas básicas
                conn.execute(text("INSERT INTO temporadas (descripcion) VALUES ('Primavera')"))
                conn.execute(text("INSERT INTO temporadas (descripcion) VALUES ('Verano')"))
                conn.execute(text("INSERT INTO temporadas (descripcion) VALUES ('Otoño')"))
                conn.execute(text("INSERT INTO temporadas (descripcion) VALUES ('Invierno')"))
                conn.execute(text("INSERT INTO temporadas (descripcion) VALUES ('Todo el año')"))
                print("Tabla 'temporadas' creada y datos básicos insertados.")
            
            # Obtener el ID del primer usuario (para asignarlo como creador de las plagas)
            result = conn.execute(text("SELECT id_usuarios FROM usuarios LIMIT 1"))
            id_usuario = result.scalar()
            
            if not id_usuario:
                print("No hay usuarios registrados. No se pueden crear plagas sin un usuario creador.")
                return
            
            # Lista de plagas comunes del maíz
            plagas = [
                {
                    "nombre": "Gusano cogollero",
                    "nombre_cientifico": "Spodoptera frugiperda",
                    "url_img": "/static/images/plagas/gusano_cogollero.jpg",
                    "descripcion": "El gusano cogollero es una de las plagas más destructivas del maíz. Las larvas se alimentan de las hojas jóvenes del cogollo, causando daños significativos al cultivo.",
                    "recomendaciones": "Control biológico con enemigos naturales, aplicación de insecticidas específicos en etapas tempranas, rotación de cultivos.",
                    "id_fase_cultivo": 2,  # Crecimiento vegetativo
                    "id_tipo_plaga": 1,    # Insecto
                    "id_temporada": 5      # Todo el año
                },
                {
                    "nombre": "Gusano elotero",
                    "nombre_cientifico": "Helicoverpa zea",
                    "url_img": "/static/images/plagas/gusano_elotero.jpg",
                    "descripcion": "El gusano elotero ataca principalmente los estigmas y granos en desarrollo. Las larvas penetran en la mazorca y se alimentan de los granos, causando daños directos y facilitando la entrada de patógenos.",
                    "recomendaciones": "Aplicación de insecticidas durante la emisión de estigmas, uso de variedades resistentes, eliminación de residuos de cosecha.",
                    "id_fase_cultivo": 4,  # Fructificación
                    "id_tipo_plaga": 1,    # Insecto
                    "id_temporada": 2      # Verano
                },
                {
                    "nombre": "Araña roja",
                    "nombre_cientifico": "Tetranychus urticae",
                    "url_img": "/static/images/plagas/arana_roja.jpg",
                    "descripcion": "La araña roja es un ácaro que se alimenta de la savia de las hojas, causando manchas amarillentas y decoloración. En infestaciones severas, puede causar la muerte de las plantas.",
                    "recomendaciones": "Mantener la humedad adecuada, aplicación de acaricidas específicos, control biológico con ácaros depredadores.",
                    "id_fase_cultivo": 3,  # Floración
                    "id_tipo_plaga": 1,    # Insecto
                    "id_temporada": 2      # Verano
                },
                {
                    "nombre": "Roya del maíz",
                    "nombre_cientifico": "Puccinia sorghi",
                    "url_img": "/static/images/plagas/roya_maiz.jpg",
                    "descripcion": "La roya del maíz es una enfermedad fúngica que produce pústulas de color marrón rojizo en las hojas. Reduce la capacidad fotosintética y puede causar pérdidas significativas en el rendimiento.",
                    "recomendaciones": "Uso de variedades resistentes, aplicación de fungicidas, rotación de cultivos, eliminación de residuos infectados.",
                    "id_fase_cultivo": 3,  # Floración
                    "id_tipo_plaga": 2,    # Hongo
                    "id_temporada": 3      # Otoño
                },
                {
                    "nombre": "Tizón foliar",
                    "nombre_cientifico": "Exserohilum turcicum",
                    "url_img": "/static/images/plagas/tizon_foliar.jpg",
                    "descripcion": "El tizón foliar causa lesiones alargadas de color gris o marrón en las hojas. En condiciones favorables, puede propagarse rápidamente y causar la muerte prematura de las hojas.",
                    "recomendaciones": "Aplicación de fungicidas, uso de variedades resistentes, rotación de cultivos, manejo adecuado de residuos.",
                    "id_fase_cultivo": 2,  # Crecimiento vegetativo
                    "id_tipo_plaga": 2,    # Hongo
                    "id_temporada": 1      # Primavera
                }
            ]
            
            # Insertar las plagas en la base de datos
            for plaga in plagas:
                conn.execute(
                    text("""
                        INSERT INTO plagas (
                            nombre, nombre_cientifico, url_img, descripcion, recomendaciones, 
                            id_fase_cultivo, id_tipo_plaga, id_temporada, id_usuarios, fecha_registro
                        ) VALUES (
                            :nombre, :nombre_cientifico, :url_img, :descripcion, :recomendaciones,
                            :id_fase_cultivo, :id_tipo_plaga, :id_temporada, :id_usuarios, NOW()
                        )
                    """),
                    {
                        "nombre": plaga["nombre"],
                        "nombre_cientifico": plaga["nombre_cientifico"],
                        "url_img": plaga["url_img"],
                        "descripcion": plaga["descripcion"],
                        "recomendaciones": plaga["recomendaciones"],
                        "id_fase_cultivo": plaga["id_fase_cultivo"],
                        "id_tipo_plaga": plaga["id_tipo_plaga"],
                        "id_temporada": plaga["id_temporada"],
                        "id_usuarios": id_usuario
                    }
                )
            
            conn.commit()
            print(f"Se han insertado {len(plagas)} plagas de ejemplo en la base de datos.")
            
            # Verificar las plagas insertadas
            result = conn.execute(text("SELECT id_plaga, nombre FROM plagas"))
            plagas_insertadas = result.fetchall()
            print("\nPlagas insertadas:")
            for plaga in plagas_insertadas:
                print(f"ID: {plaga[0]}, Nombre: {plaga[1]}")
                
    except Exception as e:
        print(f"Error al ingresar plagas de ejemplo: {e}")

if __name__ == "__main__":
    ingresar_plagas_ejemplo()
