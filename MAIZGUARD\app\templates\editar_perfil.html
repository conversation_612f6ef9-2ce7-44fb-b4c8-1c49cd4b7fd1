<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Editar Perfil - MAIZGUARD</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <!-- Estilos personalizados -->
    <link rel="stylesheet" href="/static/css/main.css">
    <link rel="stylesheet" href="/static/css/compact.css">
    <link rel="stylesheet" href="/static/css/fixed-navbar.css">
    <link rel="stylesheet" href="/static/css/super-responsive.css">
    <link rel="stylesheet" href="/static/css/small-laptop.css">
    <link rel="preload" href="/static/images/Logo.jpeg" as="image">
</head>
<body class="d-flex flex-column min-vh-100">
    <!-- Incluir el navbar -->
    {% include "partials/navbar.html" %}

    <!-- Espacio de separación después del navbar -->
    <div class="container-fluid py-3">
        <!-- Este div crea un espacio entre el navbar y el contenido -->
    </div>

    <!-- Contenido principal -->
    <main class="container my-4">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card shadow">
                    <div class="card-header bg-success text-white">
                        <h3 class="mb-0"><i class="fas fa-user-edit"></i> Editar perfil</h3>
                    </div>
                    <div class="card-body">
                        {% if error %}
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            {{ error }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                        {% endif %}
                        {% if success %}
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            {{ success }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                        {% endif %}

                        <form action="/perfil/editar" method="post" enctype="multipart/form-data">
                            <div class="mb-4 text-center">
                                <div class="position-relative d-inline-block">
                                    <img id="edit-preview-image"
                                         src="{% if user.fotografia %}{{ user.fotografia }}{% else %}/static/images/perfil icono.jpg{% endif %}"
                                         alt="Foto de perfil"
                                         class="rounded-circle img-thumbnail mb-3 border border-3 {% if user.fotografia %}border-success{% else %}border-secondary{% endif %} shadow"
                                         style="width: 150px; height: 150px; object-fit: cover;">
                                    <label for="inputFoto" class="position-absolute bottom-0 end-0 bg-success text-white rounded-circle p-2 shadow-sm" style="cursor: pointer; transition: all 0.3s ease;" title="Cambiar foto de perfil" onmouseover="this.style.transform='scale(1.1)'" onmouseout="this.style.transform='scale(1)'">
                                        <i class="fas fa-camera"></i>
                                    </label>
                                    <input type="file" id="inputFoto" name="foto" class="d-none" accept="image/*">
                                </div>
                                <small class="text-muted d-block mt-2">
                                    <i class="fas fa-info-circle"></i>
                                    Formatos soportados: JPG, PNG, GIF. Tamaño máximo: 5MB
                                </small>


                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="inputNombre" class="form-label">Nombre</label>
                                    <input type="text" class="form-control" id="inputNombre" name="nombre" value="{{ user.nombre }}" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="inputApellido" class="form-label">Apellido</label>
                                    <input type="text" class="form-control" id="inputApellido" name="apellido" value="{{ user.apellido }}" required>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="inputEmail" class="form-label">Correo electrónico</label>
                                <input type="email" class="form-control" id="inputEmail" name="email" value="{{ user.correo_electronico }}" required>
                            </div>

                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a href="/perfil" class="btn btn-outline-secondary">Cancelar</a>
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-save"></i> Guardar cambios
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Incluir el footer -->
    {% include "partials/footer.html" %}

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js" defer></script>
    <!-- Scripts personalizados -->
    <script src="/static/js/scripts.js" defer></script>
    <script src="/static/js/script.js" defer></script>

    <!-- Script para previsualizar la imagen seleccionada -->


    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const inputFoto = document.getElementById('inputFoto');
            const previewImage = document.getElementById('edit-preview-image');

            // Función para actualizar el borde de la imagen
            function updateImageBorder(hasPhoto) {
                previewImage.classList.remove('border-success', 'border-secondary');
                if (hasPhoto) {
                    previewImage.classList.add('border-success');
                } else {
                    previewImage.classList.add('border-secondary');
                }
            }

            // Función para validar archivo
            function validateFile(file) {
                // Validar tipo de archivo
                if (!file.type.match('image.*')) {
                    alert('Por favor, selecciona una imagen válida (JPG, PNG, GIF).');
                    return false;
                }

                // Validar tamaño (máximo 5MB)
                if (file.size > 5 * 1024 * 1024) {
                    alert('La imagen es demasiado grande. El tamaño máximo es 5MB.');
                    return false;
                }

                return true;
            }

            inputFoto.addEventListener('change', function() {
                if (this.files && this.files[0]) {
                    const file = this.files[0];

                    // Validar archivo
                    if (!validateFile(file)) {
                        this.value = ''; // Limpiar el input
                        return;
                    }

                    const reader = new FileReader();

                    reader.onload = function(e) {
                        previewImage.src = e.target.result;
                        updateImageBorder(true);

                        // Agregar efecto de transición suave
                        previewImage.style.opacity = '0.5';
                        setTimeout(() => {
                            previewImage.style.opacity = '1';
                        }, 100);
                    }

                    reader.readAsDataURL(file);
                }
            });

            // Agregar efecto hover al botón de cámara
            const cameraButton = document.querySelector('label[for="inputFoto"]');
            cameraButton.addEventListener('mouseenter', function() {
                this.style.backgroundColor = '#198754';
            });

            cameraButton.addEventListener('mouseleave', function() {
                this.style.backgroundColor = '#28a745';
            });
        });


    </script>
</body>
</html>
