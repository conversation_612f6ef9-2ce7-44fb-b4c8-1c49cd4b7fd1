#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script para insertar plagas en la base de datos.
"""

import os
import sys
import datetime

# Añadir el directorio raíz al path para poder importar los módulos de la aplicación
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from app.database.config import engine
from sqlalchemy import text

def insertar_plagas():
    """
    Inserta plagas en la base de datos.
    """
    try:
        with engine.connect() as conn:
            print("Verificando tablas necesarias...")

            # Verificar si existen las tablas necesarias
            result = conn.execute(text("SHOW TABLES"))
            tablas = [row[0] for row in result.fetchall()]

            tablas_necesarias = ['fase_cultivo', 'tipo_plaga', 'temporada', 'plaga']
            tablas_faltantes = [tabla for tabla in tablas_necesarias if tabla not in tablas]

            if tablas_faltantes:
                print(f"Faltan las siguientes tablas: {', '.join(tablas_faltantes)}")
                print("Creando tablas faltantes...")

                # Crear tabla fase_cultivo si no existe
                if 'fase_cultivo' in tablas_faltantes:
                    conn.execute(text("""
                        CREATE TABLE fase_cultivo (
                            id_fase_cultivo INT AUTO_INCREMENT PRIMARY KEY,
                            descripcion VARCHAR(100) NOT NULL
                        )
                    """))
                    print("Tabla fase_cultivo creada.")

                # Crear tabla tipo_plaga si no existe
                if 'tipo_plaga' in tablas_faltantes:
                    conn.execute(text("""
                        CREATE TABLE tipo_plaga (
                            id_tipo_plaga INT AUTO_INCREMENT PRIMARY KEY,
                            nombre VARCHAR(100) NOT NULL
                        )
                    """))
                    print("Tabla tipo_plaga creada.")

                # Crear tabla temporada si no existe
                if 'temporada' in tablas_faltantes:
                    conn.execute(text("""
                        CREATE TABLE temporada (
                            id_temporada INT AUTO_INCREMENT PRIMARY KEY,
                            nombre VARCHAR(100) NOT NULL
                        )
                    """))
                    print("Tabla temporada creada.")

                # Crear tabla plaga si no existe
                if 'plaga' in tablas_faltantes:
                    conn.execute(text("""
                        CREATE TABLE plaga (
                            id_plaga INT AUTO_INCREMENT PRIMARY KEY,
                            nombre VARCHAR(100) NOT NULL,
                            nombre_cientifico VARCHAR(100) NOT NULL,
                            url_img VARCHAR(255),
                            descripcion TEXT,
                            recomendaciones TEXT,
                            id_fase_cultivo INT,
                            id_tipo_plaga INT,
                            id_temporada INT,
                            id_usuarios INT,
                            FOREIGN KEY (id_fase_cultivo) REFERENCES fase_cultivo(id_fase_cultivo),
                            FOREIGN KEY (id_tipo_plaga) REFERENCES tipo_plaga(id_tipo_plaga),
                            FOREIGN KEY (id_temporada) REFERENCES temporada(id_temporada)
                        )
                    """))
                    print("Tabla plaga creada.")

            # Limpiar tablas de referencia
            print("Limpiando tablas de referencia...")
            conn.execute(text("DELETE FROM plaga"))
            conn.execute(text("DELETE FROM fase_cultivo"))
            conn.execute(text("DELETE FROM tipo_plaga"))
            conn.execute(text("DELETE FROM temporada"))

            # Insertar datos en fase_cultivo
            print("Insertando datos en fase_cultivo...")
            fases_cultivo = [
                "Germinación",
                "Crecimiento vegetativo",
                "Floración",
                "Maduración",
                "Cosecha"
            ]

            for i, fase in enumerate(fases_cultivo, 1):
                conn.execute(text("""
                    INSERT INTO fase_cultivo (id_fase_cultivo, descripcion)
                    VALUES (:id, :fase)
                """), {"id": i, "fase": fase})

            # Insertar datos en tipo_plaga
            print("Insertando datos en tipo_plaga...")
            tipos_plaga = [
                "Insecto",
                "Hongo",
                "Bacteria",
                "Virus",
                "Ácaro",
                "Nematodo"
            ]

            for i, tipo in enumerate(tipos_plaga, 1):
                conn.execute(text("""
                    INSERT INTO tipo_plaga (id_tipo_plaga, nombre)
                    VALUES (:id, :tipo)
                """), {"id": i, "tipo": tipo})

            # Insertar datos en temporada
            print("Insertando datos en temporada...")
            temporadas = [
                "Temporada de lluvia",
                "Temporada de sequía",
                "Todo el año"
            ]

            for i, temporada in enumerate(temporadas, 1):
                conn.execute(text("""
                    INSERT INTO temporada (id_temporada, nombre)
                    VALUES (:id, :temporada)
                """), {"id": i, "temporada": temporada})

            # Insertar datos en plaga
            print("Insertando datos en plaga...")

            # Plagas a insertar
            plagas = [
                {
                    "nombre": "Gusano Cogollero",
                    "nombre_cientifico": "Spodoptera frugiperda",
                    "url_img": "/static/images/gusano-cogollero.jpg",
                    "descripcion": "El gusano cogollero es una plaga que afecta principalmente durante la fase de crecimiento vegetativo y floración. Las larvas se alimentan de las hojas y el cogollo, causando daños significativos al desarrollo de la planta. Es más común durante la temporada de lluvia.",
                    "recomendaciones": """PLAGUICIDAS RECOMENDADOS:

1. QUÍMICOS LÍQUIDOS:
   - Cipermetrina 25 EC: 25-30 ml por 20 litros de agua (1.25-1.5 ml/litro)
   - Clorpirifos 48 EC: 40-50 ml por 20 litros de agua (2-2.5 ml/litro)
   - Metomil 90 SP: 15-20 g por 20 litros de agua (0.75-1 g/litro)
   - Cobertura: 200-300 litros de mezcla por hectárea (0.02-0.03 litros/m²)

2. QUÍMICOS EN POLVO:
   - Lambda-cihalotrina 2.5 WG: 10-15 g por 20 litros de agua (0.5-0.75 g/litro)
   - Diazinon 10 DP: Aplicar 20-25 kg/ha directamente al cogollo (2-2.5 g/m²)
   - Cobertura: Aplicar en un radio de 10-15 cm alrededor del tallo de cada planta

3. BIOLÓGICOS:
   - Bacillus thuringiensis: 0.5-1 kg por hectárea (0.05-0.1 g/m²)
   - Beauveria bassiana: 0.3-0.5 kg por hectárea (0.03-0.05 g/m²)
   - Liberación de Trichogramma: 50,000-100,000 avispas/ha

APLICACIÓN:
- Frecuencia: 2-3 aplicaciones con intervalos de 7-10 días
- Momento: Primeras horas de la mañana o al atardecer
- Área de tratamiento: Asegurar cobertura en un radio de 5 metros alrededor del cultivo afectado""",
                    "id_fase_cultivo": 2,
                    "id_tipo_plaga": 1,
                    "id_temporada": 1,
                    "id_usuarios": 1
                },
                {
                    "nombre": "Gusano Elotero",
                    "nombre_cientifico": "Helicoverpa zea",
                    "url_img": "/static/images/gusano-elotero.jpg",
                    "descripcion": "El gusano elotero ataca principalmente durante la fase de floración y maduración. Las larvas penetran en la mazorca y se alimentan de los granos, causando daños directos y facilitando la entrada de patógenos. Es más prevalente en temporadas de lluvia.",
                    "recomendaciones": """PLAGUICIDAS RECOMENDADOS:

1. QUÍMICOS LÍQUIDOS:
   - Spinosad 12 SC: 10-15 ml por 20 litros de agua (0.5-0.75 ml/litro)
   - Lambda-cihalotrina 5 CS: 15-20 ml por 20 litros de agua (0.75-1 ml/litro)
   - Emamectina benzoato 5 SG: 8-10 g por 20 litros de agua (0.4-0.5 g/litro)
   - Cobertura: 250-350 litros de mezcla por hectárea (0.025-0.035 litros/m²)

2. QUÍMICOS EN POLVO:
   - Carbarilo 85 WP: 30-40 g por 20 litros de agua (1.5-2 g/litro)
   - Metomil 90 SP: 15-20 g por 20 litros de agua (0.75-1 g/litro)
   - Cobertura: Aplicar directamente a los estigmas de la mazorca

3. BIOLÓGICOS:
   - Bacillus thuringiensis var. kurstaki: 0.75-1 kg por hectárea (0.075-0.1 g/m²)
   - Liberación de Trichogramma pretiosum: 75,000-100,000 avispas/ha
   - Virus de la poliedrosis nuclear: 100-150 g/ha (0.01-0.015 g/m²)

APLICACIÓN:
- Frecuencia: 2-3 aplicaciones con intervalos de 5-7 días durante la emisión de estigmas
- Momento: Al atardecer para mayor efectividad
- Área de tratamiento: Concentrar aplicación en un radio de 2-3 metros alrededor de las plantas con síntomas""",
                    "id_fase_cultivo": 3,
                    "id_tipo_plaga": 1,
                    "id_temporada": 1,
                    "id_usuarios": 1
                },
                {
                    "nombre": "Araña Roja",
                    "nombre_cientifico": "Tetranychus urticae",
                    "url_img": "/static/images/arana-roja.jpg",
                    "descripcion": "La araña roja es un ácaro que se alimenta de la savia de las hojas durante la fase de maduración, causando manchas amarillentas y decoloración. En infestaciones severas, puede causar la muerte de las plantas. Es especialmente problemática durante temporadas de sequía.",
                    "recomendaciones": """PLAGUICIDAS RECOMENDADOS:

1. ACARICIDAS LÍQUIDOS:
   - Abamectina 1.8 EC: 10-15 ml por 20 litros de agua (0.5-0.75 ml/litro)
   - Propargite 57 EC: 30-40 ml por 20 litros de agua (1.5-2 ml/litro)
   - Spiromesifen 24 SC: 15-20 ml por 20 litros de agua (0.75-1 ml/litro)
   - Cobertura: 300-400 litros de mezcla por hectárea (0.03-0.04 litros/m²)

2. ACARICIDAS EN POLVO:
   - Azufre mojable 80 WP: 50-60 g por 20 litros de agua (2.5-3 g/litro)
   - Diafentiuron 50 WP: 20-25 g por 20 litros de agua (1-1.25 g/litro)
   - Cobertura: Asegurar buena cobertura del envés de las hojas

3. BIOLÓGICOS:
   - Ácaros depredadores Phytoseiulus persimilis: 10-15 individuos/m²
   - Ácaros depredadores Amblyseius californicus: 25-50 individuos/m²
   - Aceite de neem: 30-40 ml por 20 litros de agua (1.5-2 ml/litro)

APLICACIÓN:
- Frecuencia: 2 aplicaciones con intervalo de 7 días
- Momento: Temprano en la mañana cuando la temperatura es más baja
- Área de tratamiento: Tratar un radio de 10 metros alrededor de las plantas infestadas
- Riego: Mantener humedad con riego por aspersión de 5-10 mm para reducir poblaciones""",
                    "id_fase_cultivo": 4,
                    "id_tipo_plaga": 5,
                    "id_temporada": 2,
                    "id_usuarios": 1
                },
                {
                    "nombre": "Roya del Maíz",
                    "nombre_cientifico": "Puccinia sorghi",
                    "url_img": "/static/images/roya-maiz.jpg",
                    "descripcion": "La roya del maíz es una enfermedad fúngica que produce pústulas de color marrón rojizo en las hojas durante la fase de maduración. Reduce la capacidad fotosintética y puede causar pérdidas significativas en el rendimiento. Es más común durante la temporada de lluvia debido a la alta humedad.",
                    "recomendaciones": """FUNGICIDAS RECOMENDADOS:

1. FUNGICIDAS LÍQUIDOS:
   - Tebuconazol 25 EW: 20-25 ml por 20 litros de agua (1-1.25 ml/litro)
   - Azoxistrobina 25 SC: 10-15 ml por 20 litros de agua (0.5-0.75 ml/litro)
   - Propiconazol 25 EC: 15-20 ml por 20 litros de agua (0.75-1 ml/litro)
   - Cobertura: 200-300 litros de mezcla por hectárea (0.02-0.03 litros/m²)

2. FUNGICIDAS EN POLVO:
   - Mancozeb 80 WP: 40-50 g por 20 litros de agua (2-2.5 g/litro)
   - Oxicloruro de cobre 50 WP: 50-60 g por 20 litros de agua (2.5-3 g/litro)
   - Cobertura: Asegurar buena cobertura del follaje

3. FUNGICIDAS SISTÉMICOS:
   - Epoxiconazol + Piraclostrobina: 15-20 ml por 20 litros de agua (0.75-1 ml/litro)
   - Trifloxistrobina + Tebuconazol: 15-20 ml por 20 litros de agua (0.75-1 ml/litro)
   - Cobertura: 250-300 litros de mezcla por hectárea (0.025-0.03 litros/m²)

APLICACIÓN:
- Frecuencia: 2-3 aplicaciones con intervalos de 14 días
- Momento: Al primer síntoma para mayor efectividad
- Área de tratamiento: Tratar un radio de 15-20 metros alrededor de las plantas infectadas
- Manejo de residuos: Eliminar residuos infectados en un área de al menos 500 m²""",
                    "id_fase_cultivo": 4,
                    "id_tipo_plaga": 2,
                    "id_temporada": 1,
                    "id_usuarios": 1
                },
                {
                    "nombre": "Gallina Ciega",
                    "nombre_cientifico": "Phyllophaga spp.",
                    "url_img": "/static/images/gallina-ciega.jpg",
                    "descripcion": "La gallina ciega es la larva de escarabajos que ataca las raíces del maíz durante la fase de germinación y crecimiento vegetativo. Causa amarillamiento, marchitez y retraso en el crecimiento. Puede ser devastadora en suelos con alto contenido de materia orgánica.",
                    "recomendaciones": """PLAGUICIDAS RECOMENDADOS:

1. QUÍMICOS LÍQUIDOS:
   - Imidacloprid 35 SC: 30-40 ml por 20 litros de agua (1.5-2 ml/litro)
   - Clorpirifos 48 EC: 50-60 ml por 20 litros de agua (2.5-3 ml/litro)
   - Fipronil 20 SC: 20-25 ml por 20 litros de agua (1-1.25 ml/litro)
   - Cobertura: 400-500 litros de mezcla por hectárea (0.04-0.05 litros/m²)

2. QUÍMICOS EN POLVO:
   - Diazinon 10 DP: Aplicar 25-30 kg/ha directamente al suelo (2.5-3 g/m²)
   - Carbofuran 5 G: Aplicar 20-25 kg/ha al momento de la siembra (2-2.5 g/m²)
   - Cobertura: Incorporar al suelo a una profundidad de 5-10 cm

3. BIOLÓGICOS:
   - Beauveria bassiana: 1-2 kg por hectárea (0.1-0.2 g/m²)
   - Metarhizium anisopliae: 1-2 kg por hectárea (0.1-0.2 g/m²)
   - Heterorhabditis bacteriophora (nematodos): 2.5-5 billones/ha

APLICACIÓN:
- Frecuencia: 1-2 aplicaciones antes de la siembra o durante la preparación del terreno
- Momento: Aplicar al suelo húmedo para mejor penetración
- Área de tratamiento: Tratar toda la superficie del cultivo y un margen de 5 metros
- Rotación: Implementar rotación de cultivos con leguminosas cada 2-3 temporadas""",
                    "id_fase_cultivo": 1,
                    "id_tipo_plaga": 1,
                    "id_temporada": 3,
                    "id_usuarios": 1
                },
                {
                    "nombre": "Pulgones",
                    "nombre_cientifico": "Rhopalosiphum maidis",
                    "url_img": "/static/images/pulgones.jpg",
                    "descripcion": "Los pulgones son insectos pequeños que succionan la savia de las hojas y tallos durante todas las fases de crecimiento. Causan amarillamiento, enrollamiento de hojas y pueden transmitir virus. Son más activos durante temporadas cálidas y secas.",
                    "recomendaciones": """PLAGUICIDAS RECOMENDADOS:

1. QUÍMICOS LÍQUIDOS:
   - Imidacloprid 35 SC: 15-20 ml por 20 litros de agua (0.75-1 ml/litro)
   - Tiametoxam 25 WG: 10-15 g por 20 litros de agua (0.5-0.75 g/litro)
   - Acetamiprid 20 SP: 10-15 g por 20 litros de agua (0.5-0.75 g/litro)
   - Cobertura: 250-300 litros de mezcla por hectárea (0.025-0.03 litros/m²)

2. QUÍMICOS EN POLVO:
   - Pirimicarb 50 WG: 15-20 g por 20 litros de agua (0.75-1 g/litro)
   - Dinotefuran 20 SG: 10-15 g por 20 litros de agua (0.5-0.75 g/litro)
   - Cobertura: Asegurar buena cobertura del envés de las hojas

3. BIOLÓGICOS:
   - Liberación de Chrysoperla carnea: 10,000-15,000 huevos/ha
   - Liberación de Aphidius colemani: 0.5-1 individuo/m²
   - Extracto de ajo: 50-60 ml por 20 litros de agua (2.5-3 ml/litro)
   - Aceite de neem: 40-50 ml por 20 litros de agua (2-2.5 ml/litro)

APLICACIÓN:
- Frecuencia: 2-3 aplicaciones con intervalos de 7 días
- Momento: Temprano en la mañana o al atardecer
- Área de tratamiento: Tratar un radio de 10 metros alrededor de las plantas infestadas
- Monitoreo: Instalar trampas amarillas (1 trampa cada 100 m²) para detección temprana""",
                    "id_fase_cultivo": 2,
                    "id_tipo_plaga": 1,
                    "id_temporada": 2,
                    "id_usuarios": 1
                },
                {
                    "nombre": "Trips",
                    "nombre_cientifico": "Frankliniella spp.",
                    "url_img": "/static/images/trips.jpg",
                    "descripcion": "Los trips son insectos pequeños que raspan la superficie de las hojas y se alimentan de los jugos celulares. Causan manchas plateadas, deformaciones y pueden transmitir virus. Son más problemáticos durante temporadas secas y cálidas.",
                    "recomendaciones": """PLAGUICIDAS RECOMENDADOS:

1. QUÍMICOS LÍQUIDOS:
   - Spinosad 12 SC: 10-15 ml por 20 litros de agua (0.5-0.75 ml/litro)
   - Imidacloprid 35 SC: 15-20 ml por 20 litros de agua (0.75-1 ml/litro)
   - Abamectina 1.8 EC: 10-15 ml por 20 litros de agua (0.5-0.75 ml/litro)
   - Cobertura: 300-400 litros de mezcla por hectárea (0.03-0.04 litros/m²)

2. QUÍMICOS EN POLVO:
   - Acefato 75 SP: 20-25 g por 20 litros de agua (1-1.25 g/litro)
   - Tiametoxam 25 WG: 10-15 g por 20 litros de agua (0.5-0.75 g/litro)
   - Cobertura: Asegurar buena cobertura del envés de las hojas y brotes tiernos

3. BIOLÓGICOS:
   - Ácaros depredadores Amblyseius swirskii: 50-100 individuos/m²
   - Ácaros depredadores Amblyseius cucumeris: 100-200 individuos/m²
   - Extracto de ajo: 50-60 ml por 20 litros de agua (2.5-3 ml/litro)
   - Aceite de neem: 40-50 ml por 20 litros de agua (2-2.5 ml/litro)

APLICACIÓN:
- Frecuencia: 2-3 aplicaciones con intervalos de 7 días
- Momento: Temprano en la mañana cuando la humedad es alta
- Área de tratamiento: Tratar un radio de 10 metros alrededor de las plantas infestadas
- Monitoreo: Instalar trampas azules (1 trampa cada 100 m²) para detección temprana""",
                    "id_fase_cultivo": 2,
                    "id_tipo_plaga": 1,
                    "id_temporada": 2,
                    "id_usuarios": 1
                }
            ]

            for plaga in plagas:
                conn.execute(text("""
                    INSERT INTO plaga (
                        nombre, nombre_cientifico, url_img, descripcion, recomendaciones,
                        id_fase_cultivo, id_tipo_plaga, id_temporada, id_usuarios
                    ) VALUES (
                        :nombre, :nombre_cientifico, :url_img, :descripcion, :recomendaciones,
                        :id_fase_cultivo, :id_tipo_plaga, :id_temporada, :id_usuarios
                    )
                """), plaga)

            # Confirmar cambios
            conn.commit()

            print("Plagas insertadas correctamente.")

            # Verificar las plagas insertadas
            result = conn.execute(text("SELECT id_plaga, nombre FROM plaga"))
            plagas_insertadas = result.fetchall()

            print("\nPlagas insertadas:")
            for plaga in plagas_insertadas:
                print(f"ID: {plaga[0]}, Nombre: {plaga[1]}")

    except Exception as e:
        print(f"Error al insertar plagas: {e}")

if __name__ == "__main__":
    insertar_plagas()
