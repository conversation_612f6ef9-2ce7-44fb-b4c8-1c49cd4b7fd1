from app.database.config import engine
from sqlalchemy import text
import bcrypt
from datetime import datetime

def crear_admin():
    """
    Crea un usuario administrador en la base de datos.
    """
    try:
        with engine.connect() as conn:
            # Verificar si ya existe un administrador
            result = conn.execute(text("SELECT id_usuarios FROM usuarios WHERE rol = 'admin'"))
            admin = result.fetchone()
            
            if admin:
                print(f"Ya existe un administrador con ID: {admin[0]}")
                return
            
            # Datos del administrador
            nombre = "Admin"
            apellido = "MAIZGUARD"
            correo = "<EMAIL>"
            password = "admin123"  # Contraseña simple para pruebas
            fecha_nacimiento = datetime.now().strftime("%Y-%m-%d")
            
            # Hashear la contraseña
            hashed_password = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
            
            # Insertar el administrador
            query = text("""
                INSERT INTO usuarios (nombre, apellido, correo_electronico, contrasena, fecha_nacimiento, rol, fecha_registro)
                VALUES (:nombre, :apellido, :correo, :contrasena, :fecha_nacimiento, 'admin', NOW())
            """)
            
            result = conn.execute(query, {
                "nombre": nombre,
                "apellido": apellido,
                "correo": correo,
                "contrasena": hashed_password,
                "fecha_nacimiento": fecha_nacimiento
            })
            
            conn.commit()
            
            print(f"Administrador creado exitosamente.")
            print(f"Correo: {correo}")
            print(f"Contraseña: {password}")
            
    except Exception as e:
        print(f"Error al crear administrador: {e}")

if __name__ == "__main__":
    crear_admin()
