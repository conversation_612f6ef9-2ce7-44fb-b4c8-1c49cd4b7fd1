// Script para validación del formulario de registro

document.addEventListener('DOMContentLoaded', function() {
    // Obtener referencias a los elementos del formulario
    const form = document.getElementById('registration-form');
    const nameInput = document.getElementById('inputName');
    const emailInput = document.getElementById('inputEmail');
    const birthdateInput = document.getElementById('inputBirthdate');
    const passwordInput = document.getElementById('inputPassword');
    const passwordConfirmInput = document.getElementById('inputPasswordConfirm');
    const togglePassword = document.getElementById('togglePassword');
    const togglePasswordConfirm = document.getElementById('togglePasswordConfirm');
    const termsInput = document.getElementById('inputTerms');
    const submitButton = document.getElementById('submitBtn');

    // Referencia al toast de notificación
    const notificationToast = document.getElementById('notificationToast');
    const toastTitle = document.getElementById('toastTitle');
    const toastMessage = document.getElementById('toastMessage');

    // Crear instancia de Bootstrap Toast
    let toast;
    if (typeof bootstrap !== 'undefined') {
        toast = new bootstrap.Toast(notificationToast);
    }

    // Función para mostrar notificación
    function showNotification(title, message, isError = false) {
        if (toast) {
            toastTitle.textContent = title;
            toastMessage.textContent = message;

            // Cambiar el color del toast según el tipo de mensaje
            notificationToast.classList.remove('bg-danger', 'text-white', 'bg-success');
            if (isError) {
                notificationToast.classList.add('bg-danger', 'text-white');
            } else {
                notificationToast.classList.add('bg-success', 'text-white');
            }

            toast.show();
        } else {
            // Fallback si bootstrap no está disponible
            alert(`${title}: ${message}`);
        }
    }

    // Validar correo electrónico
    function validateEmail(email) {
        const re = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
        return re.test(email);
    }

    // Validar contraseña
    function validatePassword(password) {
        // Solo verificamos que tenga al menos 8 caracteres
        return password.length >= 8;
    }

    // Funcionalidad para mostrar/ocultar contraseña
    if (togglePassword) {
        togglePassword.addEventListener('click', function() {
            // Cambiar el tipo de input entre password y text para ambos campos
            const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordInput.setAttribute('type', type);
            passwordConfirmInput.setAttribute('type', type);

            // Cambiar el icono
            const icon = this.querySelector('i');
            icon.classList.toggle('fa-eye');
            icon.classList.toggle('fa-eye-slash');
        });
    }

    // Validar fecha de nacimiento (mayor de 18 años)
    function validateBirthdate(birthdate) {
        const today = new Date();
        const birthdateDate = new Date(birthdate);
        let age = today.getFullYear() - birthdateDate.getFullYear();
        const monthDiff = today.getMonth() - birthdateDate.getMonth();

        if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthdateDate.getDate())) {
            age--;
        }

        return age >= 18;
    }

    // Evento para validar el correo electrónico mientras se escribe
    emailInput.addEventListener('input', function() {
        if (this.value && !validateEmail(this.value)) {
            this.classList.add('is-invalid');
        } else {
            this.classList.remove('is-invalid');
        }
    });

    // Evento para validar la contraseña mientras se escribe
    passwordInput.addEventListener('input', function() {
        const passwordFeedback = document.getElementById('passwordFeedback');

        if (this.value && !validatePassword(this.value)) {
            this.classList.add('is-invalid');
            passwordFeedback.style.display = 'block !important';
        } else {
            this.classList.remove('is-invalid');
            passwordFeedback.style.display = 'none !important';

            // Verificar si la confirmación de contraseña coincide
            const passwordConfirmFeedback = document.getElementById('passwordConfirmFeedback');
            if (passwordConfirmInput.value && passwordConfirmInput.value !== this.value) {
                passwordConfirmInput.classList.add('is-invalid');
                passwordConfirmFeedback.style.display = 'block !important';
            } else if (passwordConfirmInput.value) {
                passwordConfirmInput.classList.remove('is-invalid');
                passwordConfirmFeedback.style.display = 'none !important';
            }
        }
    });

    // Evento para validar la confirmación de contraseña
    passwordConfirmInput.addEventListener('input', function() {
        const passwordConfirmFeedback = document.getElementById('passwordConfirmFeedback');

        if (this.value && this.value !== passwordInput.value) {
            this.classList.add('is-invalid');
            passwordConfirmFeedback.style.display = 'block !important';
        } else {
            this.classList.remove('is-invalid');
            passwordConfirmFeedback.style.display = 'none !important';
        }
    });

    // Evento para validar la fecha de nacimiento
    birthdateInput.addEventListener('change', function() {
        if (this.value && !validateBirthdate(this.value)) {
            this.classList.add('is-invalid');
            this.nextElementSibling.nextElementSibling.textContent = 'Debes ser mayor de 18 años para registrarte.';
        } else {
            this.classList.remove('is-invalid');
            this.nextElementSibling.nextElementSibling.textContent = 'Por favor, selecciona tu fecha de nacimiento.';
        }
    });

    // Evento para validar el formulario al enviar
    form.addEventListener('submit', function(event) {
        let isValid = true;
        let errorMessage = '';

        // Validar nombre
        if (!nameInput.value.trim()) {
            nameInput.classList.add('is-invalid');
            isValid = false;
            errorMessage = 'Por favor, completa todos los campos requeridos.';
        } else {
            nameInput.classList.remove('is-invalid');
        }

        // Validar correo electrónico
        if (!emailInput.value.trim() || !validateEmail(emailInput.value)) {
            emailInput.classList.add('is-invalid');
            isValid = false;
            errorMessage = errorMessage || 'Por favor, ingresa un correo electrónico válido.';
        } else {
            emailInput.classList.remove('is-invalid');
        }

        // Validar fecha de nacimiento
        if (!birthdateInput.value) {
            birthdateInput.classList.add('is-invalid');
            isValid = false;
            errorMessage = errorMessage || 'Por favor, selecciona tu fecha de nacimiento.';
        } else if (!validateBirthdate(birthdateInput.value)) {
            birthdateInput.classList.add('is-invalid');
            isValid = false;
            errorMessage = 'Debes ser mayor de 18 años para registrarte.';
        } else {
            birthdateInput.classList.remove('is-invalid');
        }

        // Validar contraseña
        const passwordFeedback = document.getElementById('passwordFeedback');
        if (!passwordInput.value || !validatePassword(passwordInput.value)) {
            passwordInput.classList.add('is-invalid');
            passwordFeedback.style.display = 'block !important';
            isValid = false;
            errorMessage = errorMessage || 'La contraseña debe tener al menos 8 caracteres.';
        } else {
            passwordInput.classList.remove('is-invalid');
            passwordFeedback.style.display = 'none !important';
        }

        // Validar confirmación de contraseña
        const passwordConfirmFeedback = document.getElementById('passwordConfirmFeedback');
        if (!passwordConfirmInput.value || passwordConfirmInput.value !== passwordInput.value) {
            passwordConfirmInput.classList.add('is-invalid');
            passwordConfirmFeedback.style.display = 'block !important';
            isValid = false;
            errorMessage = errorMessage || 'Las contraseñas no coinciden.';
        } else {
            passwordConfirmInput.classList.remove('is-invalid');
            passwordConfirmFeedback.style.display = 'none !important';
        }

        // Validar términos y condiciones
        if (!termsInput.checked) {
            termsInput.classList.add('is-invalid');
            isValid = false;
            errorMessage = errorMessage || 'Debes aceptar los términos y condiciones.';
        } else {
            termsInput.classList.remove('is-invalid');
        }

        // Si hay errores, prevenir el envío del formulario y mostrar notificación
        if (!isValid) {
            event.preventDefault();
            showNotification('Error de validación', errorMessage, true);
        } else {
            // Mostrar notificación de éxito
            showNotification('¡Formulario válido!', 'Procesando tu registro...', false);
            // El formulario se enviará normalmente
        }
    });
});
