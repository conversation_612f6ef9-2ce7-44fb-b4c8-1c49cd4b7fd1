# app/models/schemas.py

from pydantic import BaseModel, EmailStr
from datetime import datetime
from typing import Optional, Union

# Esquema para lectura de usuario
class UsuarioSchema(BaseModel):
    id_usuarios: int
    nombre: str
    apellido: str
    correo_electronico: EmailStr
    fecha_registro: datetime

    class Config:
        orm_mode = True

# Esquema para creación de usuario
class UsuarioCreateSchema(BaseModel):
    nombre: str
    apellido: str
    contrasena: str
    correo_electronico: EmailStr
    fotografia: Union[None, str] = None
