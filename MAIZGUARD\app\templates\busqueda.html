<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Resultados de búsqueda - MAIZGUARD</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <!-- Estilos personalizados -->
    <link rel="stylesheet" href="/static/css/main.css">
    <link rel="stylesheet" href="/static/css/navbar.css">
    <link rel="stylesheet" href="/static/css/compact.css">
    <link rel="stylesheet" href="/static/css/super-responsive.css">
    <link rel="stylesheet" href="/static/css/small-laptop.css">
    <link rel="stylesheet" href="/static/css/admin-panel.css">
    <link rel="preload" href="/static/images/Logo.jpeg" as="image">

    <style>
        .search-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 1.5rem;
        }

        .search-card {
            transition: all 0.3s ease;
            border: none;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 3px 12px rgba(0, 0, 0, 0.1);
            max-width: 320px;
            margin: 0 auto;
        }

        .search-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
        }

        .search-card .card-img-top {
            height: 160px;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .search-card:hover .card-img-top {
            transform: scale(1.03);
        }

        .search-card .card-body {
            padding: 1rem;
        }

        .search-card .card-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #2c3e50 !important;
            margin-bottom: 0.5rem;
        }

        .search-card .card-text {
            font-size: 0.9rem;
            color: #495057 !important;
            line-height: 1.4;
        }

        .search-badge {
            font-size: 0.8rem;
            padding: 0.5rem 0.75rem;
            border-radius: 20px;
        }

        .no-results {
            background: linear-gradient(135deg, rgba(46, 204, 64, 0.1) 0%, rgba(255, 255, 255, 0.95) 100%);
            border-radius: 15px;
            padding: 3rem;
            text-align: center;
        }

        .results-counter-text {
            font-size: 0.95rem;
        }
    </style>
</head>
<body class="d-flex flex-column min-vh-100">
    <!-- Incluir el navbar -->
    {% include "partials/navbar.html" %}

    <!-- Encabezado de búsqueda -->
    <div class="search-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8 mx-auto text-center">
                    <h2 class="fw-bold mb-2 text-white" style="color: white !important; text-shadow: 2px 2px 4px rgba(0,0,0,0.5);">
                        <i class="fas fa-search me-2"></i>Resultados de Búsqueda
                    </h2>
                    <p class="mb-3 text-white" style="color: white !important; text-shadow: 1px 1px 2px rgba(0,0,0,0.5);">
                        Búsqueda para: <span class="fw-bold">"{{ query }}"</span>
                    </p>
                    {% if resultados %}
                    <div class="bg-white bg-opacity-90 rounded-3 p-3 d-inline-block shadow-sm">
                        <h4 class="fw-bold mb-1 text-dark">{{ resultados|length }}</h4>
                        <div class="text-dark fw-bold results-counter-text">resultado{{ 's' if resultados|length != 1 else '' }} encontrado{{ 's' if resultados|length != 1 else '' }}</div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Contenido principal -->
    <main class="container my-5">

        {% if resultados %}
            <!-- Filtros de resultados -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="Refinar búsqueda..." id="refinarBusqueda">
                        <button class="btn btn-outline-primary" type="button">
                            <i class="fas fa-filter"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-6">
                    <select class="form-select" id="ordenarResultados">
                        <option value="relevancia">Ordenar por relevancia</option>
                        <option value="nombre">Ordenar por nombre</option>
                        <option value="tipo">Ordenar por tipo</option>
                    </select>
                </div>
            </div>

            <!-- Grid de resultados mejorado -->
            <div class="row justify-content-center">
                {% for resultado in resultados %}
                    <div class="col-xl-3 col-lg-4 col-md-6 col-sm-8 mb-3">
                        <div class="card h-100 search-card">
                            {% if resultado.imagen %}
                                <div class="position-relative overflow-hidden">
                                    <img src="{{ resultado.imagen }}" class="card-img-top" alt="{{ resultado.titulo }}" loading="lazy">
                                    <div class="position-absolute top-0 end-0 m-2">
                                        <span class="search-badge bg-primary text-white">
                                            <i class="fas fa-bug me-1"></i>Plaga
                                        </span>
                                    </div>
                                </div>
                            {% else %}
                                <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                                    <i class="fas fa-bug fa-3x text-muted"></i>
                                </div>
                            {% endif %}

                            <div class="card-body">
                                <h5 class="card-title fw-bold text-primary">{{ resultado.titulo }}</h5>
                                {% if resultado.subtitulo %}
                                    <h6 class="card-subtitle mb-3">
                                        <span class="search-badge bg-warning text-dark">
                                            <i class="fas fa-tag me-1"></i>{{ resultado.subtitulo }}
                                        </span>
                                    </h6>
                                {% endif %}

                                <p class="card-text text-muted">{{ resultado.descripcion[:120] }}{% if resultado.descripcion|length > 120 %}...{% endif %}</p>

                                <!-- Información adicional -->
                                <div class="row small text-muted mb-3">
                                    <div class="col-12">
                                        <i class="fas fa-leaf me-1 text-success"></i>
                                        <strong>Fase:</strong> {{ resultado.fase_cultivo or 'No especificada' }}
                                    </div>
                                    <div class="col-12 mt-1">
                                        <i class="fas fa-calendar me-1 text-info"></i>
                                        <strong>Temporada:</strong> {{ resultado.temporada or 'No especificada' }}
                                    </div>
                                </div>
                            </div>

                            <div class="card-footer bg-light border-0">
                                {% if user_id %}
                                    <a href="{{ resultado.url }}" class="btn btn-primary w-100 fw-bold">
                                        <i class="fas fa-eye me-2"></i>Ver Detalles Completos
                                    </a>
                                {% else %}
                                    <a href="/login" class="btn btn-warning w-100 fw-bold">
                                        <i class="fas fa-lock me-2"></i>Iniciar Sesión para Ver Detalles
                                    </a>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i> No se encontraron resultados para tu búsqueda.
            </div>

            <div class="mt-4">
                <h3>Sugerencias:</h3>
                <ul>
                    <li>Verifica la ortografía de las palabras ingresadas</li>
                    <li>Utiliza términos más generales</li>
                    <li>Prueba con sinónimos</li>
                </ul>
            </div>

            <div class="mt-4">
                <h3>Búsquedas populares:</h3>
                <div class="d-flex flex-wrap gap-2">
                    <a href="/buscar?q=gusano+cogollero" class="btn btn-outline-success">Gusano cogollero</a>
                    <a href="/buscar?q=gallina+ciega" class="btn btn-outline-success">Gallina ciega</a>
                    <a href="/buscar?q=maiz+hibrido" class="btn btn-outline-success">Maíz híbrido</a>
                    <a href="/buscar?q=control+plagas" class="btn btn-outline-success">Control de plagas</a>
                </div>
            </div>
        {% endif %}
    </main>

    <!-- Incluir el footer -->
    {% include "partials/footer.html" %}

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js" defer></script>
    <!-- Scripts personalizados -->
    <script src="/static/js/scripts.js" defer></script>
    <script src="/static/js/script.js" defer></script>

    <!-- Script para mejorar la experiencia de carga -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Agregar evento de clic a todos los botones "Ver detalles"
            const botonesDetalles = document.querySelectorAll('.ver-detalles');

            botonesDetalles.forEach(function(boton) {
                boton.addEventListener('click', function(e) {
                    // Verificar si es un botón de inicio de sesión o de ver detalles
                    if (this.textContent.includes('Iniciar sesión')) {
                        // Cambiar el texto del botón para indicar redirección
                        this.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Redirigiendo...';
                    } else {
                        // Cambiar el texto del botón para indicar carga
                        this.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Cargando...';
                    }

                    this.classList.add('disabled');

                    // Permitir que la navegación continúe normalmente
                    return true;
                });
            });
        });
    </script>
</body>
</html>
