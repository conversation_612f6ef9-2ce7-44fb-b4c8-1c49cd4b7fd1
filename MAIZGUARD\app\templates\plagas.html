<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Plagas - MAIZGUARD</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <!-- Estilos personalizados -->
    <link rel="stylesheet" href="/static/css/main.css">
    <link rel="preload" href="/static/images/Logo.jpeg" as="image">
</head>
<body class="d-flex flex-column min-vh-100">
    <!-- Incluir el navbar -->
    {% include "partials/navbar.html" %}

    <!-- Contenido principal -->
    <main class="container my-5">
        <div class="row mb-4">
            <div class="col-12">
                <h1 class="text-center mb-4">Catálogo de Plagas</h1>
                <p class="lead text-center">Encuentra información sobre las plagas más comunes que afectan al maíz durante las fases de maduración y cosecha.</p>
            </div>
        </div>

        <!-- Filtros -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card shadow">
                    <div class="card-header bg-success text-white">
                        <h4 class="mb-0"><i class="fas fa-filter"></i> Filtrar plagas</h4>
                    </div>
                    <div class="card-body">
                        <form action="/plagas" method="get" class="row g-3">
                            <!-- Filtro por fase de cultivo -->
                            <div class="col-md-4">
                                <label for="fase" class="form-label">Fase de cultivo</label>
                                <select class="form-select" id="fase" name="fase">
                                    <option value="">Todas las fases</option>
                                    {% for fase in fases_cultivo %}
                                    <option value="{{ fase.id_fase_cultivo }}" {% if filtro_fase == fase.id_fase_cultivo %}selected{% endif %}>{{ fase.descripcion }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            
                            <!-- Filtro por temporada -->
                            <div class="col-md-4">
                                <label for="temporada" class="form-label">Temporada</label>
                                <select class="form-select" id="temporada" name="temporada">
                                    <option value="">Todas las temporadas</option>
                                    {% for temporada in temporadas %}
                                    <option value="{{ temporada.id_temporada }}" {% if filtro_temporada == temporada.id_temporada %}selected{% endif %}>{{ temporada.descripcion }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            
                            <!-- Filtro por tipo de plaga -->
                            <div class="col-md-4">
                                <label for="tipo" class="form-label">Tipo de plaga</label>
                                <select class="form-select" id="tipo" name="tipo">
                                    <option value="">Todos los tipos</option>
                                    {% for tipo in tipos_plaga %}
                                    <option value="{{ tipo.id_tipo_plaga }}" {% if filtro_tipo == tipo.id_tipo_plaga %}selected{% endif %}>{{ tipo.descripcion }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            
                            <!-- Botones -->
                            <div class="col-12 text-end">
                                <a href="/plagas" class="btn btn-outline-secondary me-2">Limpiar filtros</a>
                                <button type="submit" class="btn btn-success">Aplicar filtros</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Resultados -->
        <div class="row">
            {% if plagas %}
                {% for plaga in plagas %}
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card h-100 shadow">
                        <img src="{{ plaga.url_img }}" class="card-img-top" alt="{{ plaga.nombre }}" 
                             loading="lazy" height="200" style="object-fit: cover;">
                        <div class="card-body">
                            <h5 class="card-title">{{ plaga.nombre }}</h5>
                            <h6 class="card-subtitle mb-2 text-muted"><em>{{ plaga.nombre_cientifico }}</em></h6>
                            
                            <div class="mb-3">
                                <span class="badge bg-primary">{{ plaga.tipo_plaga }}</span>
                                <span class="badge bg-success">{{ plaga.fase_cultivo }}</span>
                                <span class="badge bg-warning text-dark">{{ plaga.temporada }}</span>
                            </div>
                            
                            <p class="card-text">{{ plaga.descripcion_corta }}</p>
                            
                            <a href="/plaga/{{ plaga.id_plaga }}" class="btn btn-success">Ver detalles</a>
                        </div>
                    </div>
                </div>
                {% endfor %}
            {% else %}
                <div class="col-12 text-center py-5">
                    <i class="fas fa-bug fa-4x text-muted mb-3"></i>
                    <h3>No se encontraron plagas</h3>
                    <p class="lead">No hay plagas que coincidan con los filtros seleccionados.</p>
                    <a href="/plagas" class="btn btn-success mt-3">Ver todas las plagas</a>
                </div>
            {% endif %}
        </div>
    </main>

    <!-- Footer simplificado -->
    <footer class="bg-dark text-white py-3 mt-auto">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center">
                    <p class="mb-0">&copy; 2025 MaizGuard. Todos los derechos reservados.</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js" defer></script>
    <!-- Scripts personalizados -->
    <script src="/static/js/scripts.js" defer></script>
    <script src="/static/js/script.js" defer></script>
</body>
</html>
