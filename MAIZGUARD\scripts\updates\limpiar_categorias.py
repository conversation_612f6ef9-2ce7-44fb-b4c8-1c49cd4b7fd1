from app.database.config import engine
from sqlalchemy import text

def limpiar_categorias():
    """
    Limpia y recrea las categorías de plagas.
    """
    try:
        with engine.connect() as conn:
            # Primero, actualizar las plagas para usar categorías temporales
            print("Actualizando plagas a categorías temporales...")
            conn.execute(text("UPDATE plagas SET id_fase_cultivo = 2, id_temporada = 5, id_tipo_plaga = 1"))
            
            # Eliminar categorías duplicadas o vacías
            print("\nEliminando categorías duplicadas o vacías...")
            
            # Fases de cultivo
            conn.execute(text("DELETE FROM fases_cultivo WHERE descripcion = '' OR descripcion IS NULL"))
            conn.execute(text("DELETE FROM fases_cultivo WHERE id_fase_cultivo > 5"))
            
            # Temporadas
            conn.execute(text("DELETE FROM temporadas WHERE descripcion = '' OR descripcion IS NULL"))
            conn.execute(text("DELETE FROM temporadas WHERE id_temporada > 5"))
            
            # Tipos de plaga
            conn.execute(text("DELETE FROM tipos_plaga WHERE descripcion = '' OR descripcion IS NULL"))
            conn.execute(text("DELETE FROM tipos_plaga WHERE id_tipo_plaga > 5"))
            
            # Actualizar las categorías existentes
            print("\nActualizando categorías existentes...")
            
            # Fases de cultivo
            conn.execute(text("UPDATE fases_cultivo SET descripcion = 'Germinación' WHERE id_fase_cultivo = 1"))
            conn.execute(text("UPDATE fases_cultivo SET descripcion = 'Crecimiento vegetativo' WHERE id_fase_cultivo = 2"))
            conn.execute(text("UPDATE fases_cultivo SET descripcion = 'Floración' WHERE id_fase_cultivo = 3"))
            conn.execute(text("UPDATE fases_cultivo SET descripcion = 'Maduración' WHERE id_fase_cultivo = 4"))
            conn.execute(text("UPDATE fases_cultivo SET descripcion = 'Cosecha' WHERE id_fase_cultivo = 5"))
            
            # Temporadas
            conn.execute(text("UPDATE temporadas SET descripcion = 'Temporada de lluvia' WHERE id_temporada = 1"))
            conn.execute(text("UPDATE temporadas SET descripcion = 'Temporada de sequía' WHERE id_temporada = 2"))
            conn.execute(text("UPDATE temporadas SET descripcion = 'Ambas temporadas' WHERE id_temporada = 3"))
            conn.execute(text("DELETE FROM temporadas WHERE id_temporada > 3"))
            
            # Tipos de plaga
            conn.execute(text("UPDATE tipos_plaga SET descripcion = 'Insecto' WHERE id_tipo_plaga = 1"))
            conn.execute(text("UPDATE tipos_plaga SET descripcion = 'Hongo' WHERE id_tipo_plaga = 2"))
            conn.execute(text("UPDATE tipos_plaga SET descripcion = 'Bacteria' WHERE id_tipo_plaga = 3"))
            conn.execute(text("UPDATE tipos_plaga SET descripcion = 'Virus' WHERE id_tipo_plaga = 4"))
            conn.execute(text("UPDATE tipos_plaga SET descripcion = 'Maleza' WHERE id_tipo_plaga = 5"))
            
            # Actualizar las plagas existentes
            print("\nActualizando plagas existentes...")
            
            # Gusano cogollero: Insecto, Cosecha, Sequía
            conn.execute(text("""
                UPDATE plagas SET 
                    id_fase_cultivo = 5, 
                    id_temporada = 2, 
                    id_tipo_plaga = 1 
                WHERE nombre = 'Gusano cogollero'
            """))
            
            # Gusano elotero: Insecto, Cosecha, Sequía
            conn.execute(text("""
                UPDATE plagas SET 
                    id_fase_cultivo = 5, 
                    id_temporada = 2, 
                    id_tipo_plaga = 1 
                WHERE nombre = 'Gusano elotero'
            """))
            
            # Araña roja: Insecto, Maduración, Sequía
            conn.execute(text("""
                UPDATE plagas SET 
                    id_fase_cultivo = 4, 
                    id_temporada = 2, 
                    id_tipo_plaga = 1 
                WHERE nombre = 'Araña roja'
            """))
            
            # Roya del maíz: Hongo, Maduración, Lluvia
            conn.execute(text("""
                UPDATE plagas SET 
                    id_fase_cultivo = 4, 
                    id_temporada = 1, 
                    id_tipo_plaga = 2 
                WHERE nombre = 'Roya del maíz'
            """))
            
            # Tizón foliar: Hongo, Maduración, Lluvia
            conn.execute(text("""
                UPDATE plagas SET 
                    id_fase_cultivo = 4, 
                    id_temporada = 1, 
                    id_tipo_plaga = 2 
                WHERE nombre = 'Tizón foliar'
            """))
            
            # Confirmar cambios
            conn.commit()
            
            # Mostrar categorías actualizadas
            print("\nCategorías actualizadas:")
            
            print("\nFASES DE CULTIVO:")
            result = conn.execute(text("SELECT * FROM fases_cultivo"))
            fases = result.fetchall()
            for fase in fases:
                print(f"ID: {fase[0]}, Descripción: {fase[1]}")
            
            print("\nTEMPORADAS:")
            result = conn.execute(text("SELECT * FROM temporadas"))
            temporadas = result.fetchall()
            for temporada in temporadas:
                print(f"ID: {temporada[0]}, Descripción: {temporada[1]}")
            
            print("\nTIPOS DE PLAGA:")
            result = conn.execute(text("SELECT * FROM tipos_plaga"))
            tipos = result.fetchall()
            for tipo in tipos:
                print(f"ID: {tipo[0]}, Descripción: {tipo[1]}")
            
            # Mostrar plagas actualizadas
            print("\nPLAGAS ACTUALIZADAS:")
            result = conn.execute(text("""
                SELECT p.id_plaga, p.nombre, fc.descripcion as fase, t.descripcion as temporada, tp.descripcion as tipo
                FROM plagas p
                LEFT JOIN fases_cultivo fc ON p.id_fase_cultivo = fc.id_fase_cultivo
                LEFT JOIN temporadas t ON p.id_temporada = t.id_temporada
                LEFT JOIN tipos_plaga tp ON p.id_tipo_plaga = tp.id_tipo_plaga
                ORDER BY p.id_plaga
            """))
            plagas = result.fetchall()
            for plaga in plagas:
                print(f"ID: {plaga[0]}, Nombre: {plaga[1]}, Fase: {plaga[2]}, Temporada: {plaga[3]}, Tipo: {plaga[4]}")
            
    except Exception as e:
        print(f"Error al limpiar categorías: {e}")

if __name__ == "__main__":
    limpiar_categorias()
