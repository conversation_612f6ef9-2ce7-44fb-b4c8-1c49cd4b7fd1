/* Estilos para la página Acerca de */
/* Encabezado y fondo eliminados */

.about-img-container {
    position: relative;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.5s ease;
}

.about-img-container:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.2);
}

.section-title {
    color: var(--primary-color);
    font-weight: 700;
    position: relative;
    padding-bottom: 15px;
    margin-bottom: 25px;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 70px;
    height: 3px;
    background-color: var(--accent-color);
}

.about-content {
    position: relative;
}

.about-content .lead {
    font-size: 1.2rem;
    color: #333;
}

/* Estilos para las características */
.features-list .feature-item {
    transition: all 0.3s ease;
}

.features-list .feature-item:hover {
    transform: translateX(5px);
}

.feature-icon {
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.feature-text {
    font-weight: 500;
}

/* Estilos para misión y visión */
.mission-icon, .vision-icon {
    color: var(--primary-color);
}

.mission-highlights .fas,
.vision-goals .fas {
    color: var(--primary-color);
}

.goal-item {
    transition: all 0.3s ease;
}

.goal-item:hover {
    background-color: rgba(40, 167, 69, 0.1) !important;
    transform: translateX(5px);
}

/* Divisor personalizado */
.divider-custom {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.divider-custom-line {
    width: 100%;
    max-width: 7rem;
    height: 0.25rem;
    background-color: var(--primary-color);
    border-radius: 1rem;
    opacity: 0.5;
}

.divider-custom-icon {
    font-size: 1.5rem;
    margin: 0 1rem;
}

/* Tarjetas de valores */
.value-card {
    padding: 20px;
    transition: transform 0.3s ease;
    border-radius: 10px;
    background-color: white;
    box-shadow: 0 5px 15px rgba(0,0,0,0.05);
    height: 100%;
}

.value-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
}
