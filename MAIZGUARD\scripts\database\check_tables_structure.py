from app.database.config import engine
from sqlalchemy import text

def check_tables_structure():
    try:
        with engine.connect() as conn:
            # Obtener todas las tablas
            result = conn.execute(text("SHOW TABLES"))
            tables = [row[0] for row in result]
            print("Tablas en la base de datos:")
            for table in tables:
                print(f"- {table}")
            
            # Verificar la estructura de la tabla plaga
            print("\nEstructura de la tabla 'plaga':")
            try:
                result = conn.execute(text("DESCRIBE plaga"))
                columns = result.fetchall()
                for column in columns:
                    print(f"- {column[0]}: {column[1]}")
            except Exception as e:
                print(f"Error al verificar la tabla 'plaga': {e}")
                
                # Verificar si existe la tabla plagas (plural)
                result = conn.execute(text("SHOW TABLES LIKE 'plagas'"))
                if result.fetchone():
                    print("\nEstructura de la tabla 'plagas':")
                    result = conn.execute(text("DESCRIBE plagas"))
                    columns = result.fetchall()
                    for column in columns:
                        print(f"- {column[0]}: {column[1]}")
    except Exception as e:
        print(f"Error al verificar las tablas: {e}")

if __name__ == "__main__":
    check_tables_structure()
