import secrets
from datetime import datetime, timedelta
from typing import Optional
import smtplib
import bcrypt
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON><PERSON>ip<PERSON>

def hash_password(password: str) -> str:
    """
    Hashea una contraseña usando bcrypt.
    """
    # Generar un hash bcrypt con un salt aleatorio
    hashed = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt())
    # Devolver el hash como string
    return hashed.decode('utf-8')

def verify_password(stored_password: str, provided_password: str) -> bool:
    """
    Verifica si una contraseña coincide con su hash almacenado usando bcrypt.
    """
    # Verificar la contraseña con bcrypt
    return bcrypt.checkpw(provided_password.encode('utf-8'), stored_password.encode('utf-8'))

def generate_reset_token() -> str:
    """
    Genera un token aleatorio para restablecer la contraseña.
    """
    return secrets.token_urlsafe(32)

def send_reset_email(email: str, token: str, base_url: str) -> bool:
    """
    Envía un correo electrónico con el enlace para restablecer la contraseña.

    En un entorno de producción, deberías configurar un servidor SMTP real.
    Para desarrollo, simplemente imprimimos el enlace en la consola.
    """
    reset_link = f"{base_url}/reset-password/{token}"

    # En un entorno de desarrollo, solo imprimimos el enlace
    print(f"\n[CORREO DE RECUPERACIÓN] Enlace para restablecer contraseña: {reset_link}\n")

    # En un entorno de producción, descomentar y configurar esto:
    """
    try:
        # Configuración del servidor SMTP
        smtp_server = "smtp.gmail.com"  # Cambiar según tu proveedor
        smtp_port = 587
        smtp_user = "<EMAIL>"  # Cambiar por tu correo
        smtp_password = "tu_contraseña"  # Cambiar por tu contraseña o token de app

        # Crear mensaje
        msg = MIMEMultipart()
        msg['From'] = smtp_user
        msg['To'] = email
        msg['Subject'] = "Recuperación de contraseña - MAIZGUARD"

        # Cuerpo del mensaje
        body = f"Hola,\n\nHas solicitado restablecer tu contraseña en MAIZGUARD.\n\nHaz clic en el siguiente enlace para crear una nueva contraseña:\n{reset_link}\n\nEste enlace expirará en 1 hora.\n\nSi no solicitaste restablecer tu contraseña, puedes ignorar este correo.\n\nSaludos,\nEl equipo de MAIZGUARD"

        msg.attach(MIMEText(body, 'plain'))

        # Conectar y enviar
        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()
        server.login(smtp_user, smtp_password)
        server.send_message(msg)
        server.quit()

        return True
    except Exception as e:
        print(f"Error al enviar correo: {str(e)}")
        return False
    """

    # Para desarrollo, siempre devolvemos True
    return True
