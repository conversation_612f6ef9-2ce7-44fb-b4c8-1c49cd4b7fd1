import os
import sys
import subprocess

def start_server():
    """
    Inicia el servidor FastAPI usando uvicorn.
    """
    try:
        # Obtener la ruta absoluta del directorio actual
        current_dir = os.path.dirname(os.path.abspath(__file__))
        print(f"Directorio actual: {current_dir}")
        
        # Cambiar al directorio del proyecto
        os.chdir(current_dir)
        print(f"Cambiado al directorio: {os.getcwd()}")
        
        # Iniciar el servidor
        print("Iniciando el servidor...")
        subprocess.run([sys.executable, "-m", "uvicorn", "main:app", "--reload", "--host", "127.0.0.1", "--port", "8000"])
    except Exception as e:
        print(f"Error al iniciar el servidor: {e}")

if __name__ == "__main__":
    start_server()
