from fastapi import APIRouter, HTTPException, Depends
from typing import List, Optional
from pydantic import BaseModel
import aiomysql
from app.routers.fase_cultivo import conectar_db  # Reutilizar la función de conexión a la base de datos

router = APIRouter()

# Función para conectar a la base de datos
async def conectar_db():
    return await aiomysql.connect(
        host="localhost",
        port=3306,
        user="root",
        password="",
        db="MAIZGUARD"
    )
    

# Modelo para crear/actualizar un registro de cultivo
class RegistroCultivoCreate(BaseModel):
    id_fase_cultivo: int
    id_usuarios: int
    ancho: Optional[float] = None
    largo: Optional[float] = None
    url_img: str
    descripcion: Optional[str] = None
    id_estados: int

# Modelo para la respuesta de un registro de cultivo
class RegistroCultivo(BaseModel):
    id_registro_cultivo: int
    id_fase_cultivo: int
    id_usuarios: int
    ancho: Optional[float]
    largo: Optional[float]
    url_img: str
    descripcion: Optional[str]
    id_estados: int
    fecha_registro: str

# Endpoint para registrar un nuevo registro de cultivo
@router.post("/RegistrarRegistroCultivo", response_model=RegistroCultivo)
async def registrar_registro_cultivo(registro: RegistroCultivoCreate):
    conexion = None
    try:
        conexion = await conectar_db()
        async with conexion.cursor() as cursor:
            # Insertar el nuevo registro de cultivo
            query = """
                INSERT INTO registro_cultivo (id_fase_cultivo, id_usuarios, ancho, largo, url_img, descripcion, id_estados)
                VALUES (%s, %s, %s, %s, %s, %s, %s)
            """
            valores = (
                registro.id_fase_cultivo,
                registro.id_usuarios,
                registro.ancho,
                registro.largo,
                registro.url_img,
                registro.descripcion,
                registro.id_estados,
            )
            await cursor.execute(query, valores)
            await conexion.commit()

            # Obtener el ID generado
            id_registro_cultivo = cursor.lastrowid

        return RegistroCultivo(id_registro_cultivo=id_registro_cultivo, **registro.dict(), fecha_registro="")

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error al registrar el cultivo: {str(e)}")
    finally:
        if conexion:
            conexion.close()

# Endpoint para listar todos los registros de cultivo
@router.get("/ListarRegistrosCultivo", response_model=List[RegistroCultivo])
async def listar_registros_cultivo():
    conexion = None
    try:
        conexion = await conectar_db()
        async with conexion.cursor(aiomysql.DictCursor) as cursor:
            # Consultar todos los registros de cultivo
            query = "SELECT * FROM registro_cultivo"
            await cursor.execute(query)
            registros = await cursor.fetchall()

            # Convertir fecha_registro a string
            for registro in registros:
                registro["fecha_registro"] = str(registro["fecha_registro"])

        return registros

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error al listar los registros de cultivo: {str(e)}")
    finally:
        if conexion:
            conexion.close()

# Endpoint para obtener un registro de cultivo por ID
@router.get("/RegistroCultivo/{id_registro_cultivo}", response_model=RegistroCultivo)
async def obtener_registro_cultivo(id_registro_cultivo: int):
    conexion = None
    try:
        conexion = await conectar_db()
        async with conexion.cursor(aiomysql.DictCursor) as cursor:
            # Consultar el registro de cultivo por ID
            query = "SELECT * FROM registro_cultivo WHERE id_registro_cultivo = %s"
            await cursor.execute(query, (id_registro_cultivo,))
            registro = await cursor.fetchone()

            if not registro:
                raise HTTPException(status_code=404, detail="Registro de cultivo no encontrado")

            # Convertir fecha_registro a string
            registro["fecha_registro"] = str(registro["fecha_registro"])

        return registro

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error al obtener el registro de cultivo: {str(e)}")
    finally:
        if conexion:
            conexion.close()

# Endpoint para actualizar un registro de cultivo
@router.put("/ActualizarRegistroCultivo/{id_registro_cultivo}")
async def actualizar_registro_cultivo(id_registro_cultivo: int, registro: RegistroCultivoCreate):
    conexion = None
    try:
        conexion = await conectar_db()
        async with conexion.cursor() as cursor:
            # Verificar si el registro de cultivo existe
            query_verificar = "SELECT id_registro_cultivo FROM registro_cultivo WHERE id_registro_cultivo = %s"
            await cursor.execute(query_verificar, (id_registro_cultivo,))
            registro_existente = await cursor.fetchone()

            if not registro_existente:
                raise HTTPException(status_code=404, detail="Registro de cultivo no encontrado")

            # Actualizar el registro de cultivo
            query = """
                UPDATE registro_cultivo
                SET id_fase_cultivo = %s, id_usuarios = %s, ancho = %s, largo = %s, url_img = %s, descripcion = %s, id_estados = %s
                WHERE id_registro_cultivo = %s
            """
            valores = (
                registro.id_fase_cultivo,
                registro.id_usuarios,
                registro.ancho,
                registro.largo,
                registro.url_img,
                registro.descripcion,
                registro.id_estados,
                id_registro_cultivo,
            )
            await cursor.execute(query, valores)
            await conexion.commit()

        return {"mensaje": "Registro de cultivo actualizado exitosamente"}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error al actualizar el registro de cultivo: {str(e)}")
    finally:
        if conexion:
            conexion.close()

# Endpoint para eliminar un registro de cultivo
@router.delete("/EliminarRegistroCultivo/{id_registro_cultivo}")
async def eliminar_registro_cultivo(id_registro_cultivo: int):
    conexion = None
    try:
        conexion = await conectar_db()
        async with conexion.cursor() as cursor:
            # Verificar si el registro de cultivo existe
            query_verificar = "SELECT id_registro_cultivo FROM registro_cultivo WHERE id_registro_cultivo = %s"
            await cursor.execute(query_verificar, (id_registro_cultivo,))
            registro_existente = await cursor.fetchone()

            if not registro_existente:
                raise HTTPException(status_code=404, detail="Registro de cultivo no encontrado")

            # Eliminar el registro de cultivo
            query = "DELETE FROM registro_cultivo WHERE id_registro_cultivo = %s"
            await cursor.execute(query, (id_registro_cultivo,))
            await conexion.commit()

        return {"mensaje": "Registro de cultivo eliminado exitosamente"}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error al eliminar el registro de cultivo: {str(e)}")
    finally:
        if conexion:
            conexion.close()