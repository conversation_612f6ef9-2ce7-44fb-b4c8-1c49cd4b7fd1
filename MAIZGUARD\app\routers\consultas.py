from fastapi import APIRouter, HTTPException
from typing import List
from pydantic import BaseModel
import aiomysql
from app.routers.fase_cultivo import conectar_db  # Reutilizar la función de conexión a la base de datos

router = APIRouter()

# Función para conectar a la base de datos
async def conectar_db():
    return await aiomysql.connect(
        host="localhost",
        port=3306,
        user="root",
        password="",
        db="MAIZGUARD"
    )
    

# Modelo para registrar una consulta
class ConsultaCreate(BaseModel):
    id_Plaga: int
    id_usuarios: int

# Modelo para la respuesta de una consulta
class Consulta(BaseModel):
    id_consultas: int
    id_Plaga: int
    id_usuarios: int

    # Modelo para la respuesta de una plaga
class Plaga(BaseModel):
    id_Plaga: int
    nombre: str
    nombre_cientifico: str
    url_img: str
    descripcion: str
    recomendaciones: str

# Endpoint para buscar plagas con filtros
@router.get("/BuscarPlagas", response_model=List[Plaga])
async def buscar_plagas(
    id_fase_cultivo: int = None,
    id_temporada: int = None,
    id_tipo_plaga: int = None
):
    conexion = None
    try:
        conexion = await conectar_db()
        async with conexion.cursor(aiomysql.DictCursor) as cursor:
            # Construir la consulta SQL con filtros opcionales
            query = """
                SELECT p.id_Plaga, p.nombre, p.nombre_cientifico, p.url_img, p.descripcion, p.recomendaciones
                FROM plaga p
                INNER JOIN fase_cultivo fc ON p.id_fase_cultivo = fc.id_fase_cultivo
                INNER JOIN temporada t ON p.id_temporada = t.id_temporada
                INNER JOIN tipo_plaga tp ON p.id_tipo_plaga = tp.id_tipo_plaga
                WHERE 1=1
            """
            valores = []

            # Agregar filtros opcionales
            if id_fase_cultivo:
                query += " AND p.id_fase_cultivo = %s"
                valores.append(id_fase_cultivo)
            if id_temporada:
                query += " AND p.id_temporada = %s"
                valores.append(id_temporada)
            if id_tipo_plaga:
                query += " AND p.id_tipo_plaga = %s"
                valores.append(id_tipo_plaga)

            # Ejecutar la consulta
            await cursor.execute(query, valores)
            plagas = await cursor.fetchall()

        return plagas

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error al buscar las plagas: {str(e)}")
    finally:
        if conexion:
            conexion.close()

# Endpoint para registrar una consulta
@router.post("/RegistrarConsulta", response_model=Consulta)
async def registrar_consulta(consulta: ConsultaCreate):
    conexion = None
    try:
        conexion = await conectar_db()
        async with conexion.cursor() as cursor:
            # Verificar si la plaga existe
            query_verificar_plaga = "SELECT id_Plaga FROM plaga WHERE id_Plaga = %s"
            await cursor.execute(query_verificar_plaga, (consulta.id_Plaga,))
            plaga_existente = await cursor.fetchone()

            if not plaga_existente:
                raise HTTPException(status_code=404, detail="Plaga no encontrada")

            # Verificar si el usuario existe
            query_verificar_usuario = "SELECT id_usuarios FROM usuarios WHERE id_usuarios = %s"
            await cursor.execute(query_verificar_usuario, (consulta.id_usuarios,))
            usuario_existente = await cursor.fetchone()

            if not usuario_existente:
                raise HTTPException(status_code=404, detail="Usuario no encontrado")

            # Insertar la consulta
            query = "INSERT INTO Consultas (id_Plaga, id_usuarios) VALUES (%s, %s)"
            valores = (consulta.id_Plaga, consulta.id_usuarios)
            await cursor.execute(query, valores)
            await conexion.commit()

            # Obtener el ID generado
            id_consultas = cursor.lastrowid

        return Consulta(id_consultas=id_consultas, **consulta.dict())

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error al registrar la consulta: {str(e)}")
    finally:
        if conexion:
            conexion.close()