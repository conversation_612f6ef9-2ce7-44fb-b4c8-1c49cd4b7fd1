from app.database.config import engine
from sqlalchemy import text

def verificar_db():
    """
    Verifica el estado actual de la base de datos.
    """
    try:
        with engine.connect() as conn:
            # Verificar tipos de plaga
            print("TIPOS DE PLAGA:")
            result = conn.execute(text("SELECT * FROM tipos_plaga ORDER BY id_tipo_plaga"))
            tipos = result.fetchall()
            for tipo in tipos:
                print(f"ID: {tipo[0]}, Descripción: {tipo[1]}")
            
            # Verificar plagas
            print("\nPLAGAS:")
            result = conn.execute(text("""
                SELECT p.id_plaga, p.nombre, p.id_tipo_plaga
                FROM plagas p
                ORDER BY p.id_plaga
            """))
            plagas = result.fetchall()
            for plaga in plagas:
                print(f"ID: {plaga[0]}, Nombre: {plaga[1]}, ID Tipo: {plaga[2]}")
            
    except Exception as e:
        print(f"Error al verificar la base de datos: {e}")

if __name__ == "__main__":
    verificar_db()
