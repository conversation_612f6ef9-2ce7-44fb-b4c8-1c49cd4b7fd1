from fastapi import APIRouter, HTTPException, Depends
from typing import List
from pydantic import BaseModel
import aiomysql

router = APIRouter()

# Función para conectar a la base de datos
async def conectar_db():
    return await aiomysql.connect(
        host="localhost",
        port=3306,
        user="root",
        password="",
        db="MAIZGUARD"
    )

# Modelo para la respuesta de fase de cultivo
class FaseCultivo(BaseModel):
    id_fase_cultivo: int
    descripcion: str

# Endpoint para listar todas las fases de cultivo
@router.get("/ListarFasesCultivo", response_model=List[FaseCultivo])
async def listar_fases_cultivo():
    conexion = None
    try:
        conexion = await conectar_db()
        async with conexion.cursor(aiomysql.DictCursor) as cursor:
            # Consultar todas las fases de cultivo
            query = "SELECT id_fase_cultivo, descripcion FROM fase_cultivo"
            await cursor.execute(query)
            fases = await cursor.fetchall()

        return fases

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error al listar las fases de cultivo: {str(e)}")
    finally:
        if conexion:
            conexion.close()

# Endpoint para obtener una fase de cultivo por ID
@router.get("/FaseCultivo/{id_fase_cultivo}", response_model=FaseCultivo)
async def obtener_fase_cultivo(id_fase_cultivo: int):
    conexion = None
    try:
        conexion = await conectar_db()
        async with conexion.cursor(aiomysql.DictCursor) as cursor:
            # Consultar la fase de cultivo por ID
            query = "SELECT id_fase_cultivo, descripcion FROM fase_cultivo WHERE id_fase_cultivo = %s"
            await cursor.execute(query, (id_fase_cultivo,))
            fase = await cursor.fetchone()

            if not fase:
                raise HTTPException(status_code=404, detail="Fase de cultivo no encontrada")

        return fase

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error al obtener la fase de cultivo: {str(e)}")
    finally:
        if conexion:
            conexion.close()

# Endpoint para crear una nueva fase de cultivo
@router.post("/CrearFaseCultivo")
async def crear_fase_cultivo(fase: FaseCultivo):
    conexion = None
    try:
        conexion = await conectar_db()
        async with conexion.cursor() as cursor:
            # Insertar la nueva fase de cultivo
            query = "INSERT INTO fase_cultivo (descripcion) VALUES (%s)"
            await cursor.execute(query, (fase.descripcion,))
            await conexion.commit()

            # Obtener el ID generado
            id_fase_cultivo = cursor.lastrowid

        return {"mensaje": "Fase de cultivo creada exitosamente", "id_fase_cultivo": id_fase_cultivo}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error al crear la fase de cultivo: {str(e)}")
    finally:
        if conexion:
            conexion.close()