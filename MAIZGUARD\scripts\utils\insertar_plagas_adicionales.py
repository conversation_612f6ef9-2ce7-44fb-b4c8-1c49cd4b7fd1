#!/usr/bin/env python3
"""
Script para insertar plagas adicionales basándose en las imágenes disponibles
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from app.database.config import engine
from sqlalchemy import text

def insertar_plagas_adicionales():
    """Insertar plagas adicionales basándose en las imágenes disponibles"""
    try:
        with engine.connect() as conn:
            # Obtener el ID de un usuario administrador
            result = conn.execute(text("SELECT id_usuarios FROM usuarios WHERE id_rol = 1 LIMIT 1"))
            admin_user = result.fetchone()
            
            if not admin_user:
                result = conn.execute(text("SELECT id_usuarios FROM usuarios LIMIT 1"))
                admin_user = result.fetchone()
                admin_user_id = admin_user.id_usuarios if admin_user else 1
            else:
                admin_user_id = admin_user.id_usuarios

            # Obtener ID del tipo de plaga "Ácaro"
            result = conn.execute(text("SELECT id_tipo_plaga FROM tipos_plaga WHERE nombre = 'Ácaro'"))
            acaro_tipo = result.fetchone()
            acaro_tipo_id = acaro_tipo.id_tipo_plaga if acaro_tipo else 6

            # INSECTOS ADICIONALES
            insectos_adicionales = [
                {
                    "nombre": "Gusano Soldado",
                    "nombre_cientifico": "Spodoptera frugiperda",
                    "descripcion": "Oruga que consume hojas, cogollos y granos, reduciendo el rendimiento y la calidad del maíz. Similar al gusano cogollero pero con comportamiento más agresivo.",
                    "url_img": "/static/images/Gusano soldado.png",
                    "recomendaciones": """
CONTROL QUÍMICO:
• Plaguicida: Clorpirifós 48% EC
• Cantidad: 1.5-2.0 litros por hectárea (150-200 ml por 100 m²)
• Aplicación: Aspersión foliar dirigida al cogollo
• Frecuencia: Cada 7-10 días según nivel de infestación

• Plaguicida biológico: Bacillus thuringiensis var. kurstaki
• Cantidad: 1.5-2.0 kg por hectárea (150-200 g por 100 m²)
• Aplicación: Aspersión foliar en horas de la tarde
• Frecuencia: Cada 5-7 días

CONTROL BIOLÓGICO:
• Liberación de Trichogramma pretiosum: 100,000 individuos por hectárea
• Aplicación: Distribución uniforme cada 15 días
• Uso de feromonas para monitoreo: 1 trampa cada 50 m²
                    """
                },
                {
                    "nombre": "Langosta Centroamericana",
                    "nombre_cientifico": "Schistocerca piceifrons",
                    "descripcion": "Insecto saltador que puede causar defoliación severa en cultivos de maíz. Forma enjambres que pueden devastar grandes áreas de cultivo.",
                    "url_img": "/static/images/Langosta centroamericana.jpeg",
                    "recomendaciones": """
CONTROL QUÍMICO:
• Plaguicida: Malation 57% EC
• Cantidad: 1.5-2.0 litros por hectárea (150-200 ml por 100 m²)
• Aplicación: Aspersión foliar de cobertura total
• Frecuencia: Al detectar enjambres

• Plaguicida alternativo: Carbaril 85% WP
• Cantidad: 2.0-2.5 kg por hectárea (200-250 g por 100 m²)
• Aplicación: Aspersión foliar
• Momento: Temprano en la mañana o al atardecer

CONTROL PREVENTIVO:
• Monitoreo de enjambres migratorios
• Eliminación de malezas hospederas
• Coordinación con autoridades fitosanitarias
• Aplicación de barreras físicas
                    """
                },
                {
                    "nombre": "Perforador del Maíz",
                    "nombre_cientifico": "Diatraea saccharalis",
                    "descripcion": "Larva que perfora tallos y mazorcas del maíz, creando galerías que debilitan la planta y facilitan la entrada de patógenos.",
                    "url_img": "/static/images/Perforador del maíz.png",
                    "recomendaciones": """
CONTROL QUÍMICO:
• Plaguicida: Spinosad 12% SC
• Cantidad: 0.2-0.3 litros por hectárea (20-30 ml por 100 m²)
• Aplicación: Aspersión dirigida a tallos y mazorcas
• Frecuencia: Cada 15 días durante período crítico

CONTROL BIOLÓGICO:
• Liberación de Cotesia flavipes (parasitoide)
• Cantidad: 2,000-3,000 individuos por hectárea
• Aplicación: Distribución en focos de infestación
• Momento: Al detectar primeras larvas

MANEJO CULTURAL:
• Eliminación de residuos de cosecha
• Rotación con cultivos no hospederos
• Siembra en fechas recomendadas
                    """
                },
                {
                    "nombre": "Perforador del Tallo del Maíz",
                    "nombre_cientifico": "Ostrinia nubilalis",
                    "descripcion": "Las larvas de esta polilla perforan los tallos y mazorcas del maíz. Las larvas son de color crema con manchas oscuras.",
                    "url_img": "/static/images/Perforador del tallo del maiz.jpg",
                    "recomendaciones": """
CONTROL QUÍMICO:
• Plaguicida: Lambda-cihalotrina 2.5% EC
• Cantidad: 0.3-0.5 litros por hectárea (30-50 ml por 100 m²)
• Aplicación: Aspersión dirigida a tallos
• Frecuencia: Cada 10-15 días durante floración

CONTROL BIOLÓGICO:
• Liberación de Trichogramma brassicae: 150,000 individuos por hectárea
• Aplicación: 3 liberaciones cada 10 días
• Uso de feromonas para monitoreo

MANEJO INTEGRADO:
• Eliminación de residuos vegetales
• Preparación profunda del suelo
• Monitoreo con trampas de feromonas
                    """
                },
                {
                    "nombre": "Gusanos de Alambre",
                    "nombre_cientifico": "Elateridae spp.",
                    "descripcion": "Larvas de escarabajos que atacan las raíces y semillas en germinación, causando daños significativos en las primeras etapas del cultivo.",
                    "url_img": "/static/images/Elateridae (gusanos de alambre).png",
                    "recomendaciones": """
CONTROL QUÍMICO:
• Plaguicida: Imidacloprid 35% SC
• Cantidad: 0.5-0.7 litros por hectárea (50-70 ml por 100 m²)
• Aplicación: Drench al suelo antes de la siembra
• Frecuencia: Una aplicación por ciclo

• Plaguicida granulado: Carbofuran 3% G
• Cantidad: 20-25 kg por hectárea (2-2.5 kg por 100 m²)
• Aplicación: Incorporación al suelo al momento de siembra
• Profundidad: 5-10 cm en el surco

CONTROL CULTURAL:
• Preparación profunda del suelo
• Rotación con leguminosas
• Eliminación de malezas gramíneas
• Control de humedad del suelo
                    """
                },
                {
                    "nombre": "Mosca del Maíz",
                    "nombre_cientifico": "Delia platura",
                    "descripcion": "Mosca cuyas larvas atacan las semillas en germinación y plántulas jóvenes, causando fallas en la emergencia y establecimiento del cultivo.",
                    "url_img": "/static/images/Mosca del maíz.jpeg",
                    "recomendaciones": """
CONTROL QUÍMICO:
• Plaguicida: Diazinón 60% EC
• Cantidad: 1.0-1.5 litros por hectárea (100-150 ml por 100 m²)
• Aplicación: Aspersión al suelo antes de siembra
• Frecuencia: Una aplicación preventiva

CONTROL PREVENTIVO:
• Tratamiento de semillas con insecticidas
• Siembra en condiciones óptimas de temperatura
• Evitar siembra en suelos muy húmedos
• Eliminación de materia orgánica en descomposición
                    """
                }
            ]

            # ÁCAROS ADICIONALES
            acaros_adicionales = [
                {
                    "nombre": "Ácaro Bronceado",
                    "nombre_cientifico": "Aculops lycopersici",
                    "descripcion": "Ácaro microscópico que causa bronceado en las hojas del maíz, afectando la fotosíntesis y el desarrollo de la planta.",
                    "url_img": "/static/images/Acaro bronceado.png",
                    "recomendaciones": """
CONTROL QUÍMICO:
• Acaricida: Abamectina 1.8% EC
• Cantidad: 0.5-0.8 litros por hectárea (50-80 ml por 100 m²)
• Aplicación: Aspersión foliar con énfasis en el envés
• Frecuencia: Cada 12-15 días, máximo 2 aplicaciones

CONTROL PREVENTIVO:
• Monitoreo regular con lupa
• Manejo de humedad relativa
• Eliminación de malezas hospederas
• Rotación de acaricidas para evitar resistencia
                    """
                },
                {
                    "nombre": "Ácaros de Dos Manchas",
                    "nombre_cientifico": "Tetranychus urticae",
                    "descripcion": "Ácaro conocido por las dos manchas características en su dorso. Causa amarillamiento y bronceado de las hojas.",
                    "url_img": "/static/images/Acaros de dos manchas.jpg",
                    "recomendaciones": """
CONTROL QUÍMICO:
• Acaricida: Propargite 57% EC
• Cantidad: 1.0-1.5 litros por hectárea (100-150 ml por 100 m²)
• Aplicación: Aspersión foliar dirigida al envés
• Frecuencia: Cada 15 días según infestación

CONTROL BIOLÓGICO:
• Liberación de Phytoseiulus persimilis
• Cantidad: 5-10 individuos por m²
• Aplicación: Distribución manual en focos
• Condiciones: Humedad relativa >60%
                    """
                },
                {
                    "nombre": "Ácaro del Falso Rojo",
                    "nombre_cientifico": "Brevipalpus phoenicis",
                    "descripcion": "Ácaro que causa manchas cloróticas y puede transmitir virus a las plantas de maíz, afectando significativamente el rendimiento.",
                    "url_img": "/static/images/Ácaro del falso rojo.png",
                    "recomendaciones": """
CONTROL QUÍMICO:
• Acaricida: Spiromesifen 24% SC
• Cantidad: 0.4-0.6 litros por hectárea (40-60 ml por 100 m²)
• Aplicación: Aspersión foliar con buen mojamiento
• Frecuencia: Una aplicación, repetir si es necesario a los 15 días

CONTROL INTEGRADO:
• Eliminación de malezas hospederas
• Control de vectores de virus
• Monitoreo constante de síntomas
• Uso de variedades resistentes
                    """
                }
            ]

            # Insertar INSECTOS ADICIONALES
            for plaga in insectos_adicionales:
                result = conn.execute(text("""
                    SELECT COUNT(*) as count FROM plagas 
                    WHERE nombre = :nombre
                """), {"nombre": plaga["nombre"]})
                
                if result.fetchone().count == 0:
                    conn.execute(text("""
                        INSERT INTO plagas (
                            nombre, nombre_cientifico, url_img, descripcion, recomendaciones,
                            id_fase_cultivo, id_tipo_plaga, id_temporada, id_usuarios
                        ) VALUES (
                            :nombre, :nombre_cientifico, :url_img, :descripcion, :recomendaciones,
                            4, 1, 1, :id_usuarios
                        )
                    """), {
                        **plaga,
                        "id_usuarios": admin_user_id
                    })
                    print(f"✅ Insecto '{plaga['nombre']}' insertado.")

            # Insertar ÁCAROS ADICIONALES
            for plaga in acaros_adicionales:
                result = conn.execute(text("""
                    SELECT COUNT(*) as count FROM plagas 
                    WHERE nombre = :nombre
                """), {"nombre": plaga["nombre"]})
                
                if result.fetchone().count == 0:
                    conn.execute(text("""
                        INSERT INTO plagas (
                            nombre, nombre_cientifico, url_img, descripcion, recomendaciones,
                            id_fase_cultivo, id_tipo_plaga, id_temporada, id_usuarios
                        ) VALUES (
                            :nombre, :nombre_cientifico, :url_img, :descripcion, :recomendaciones,
                            4, :id_tipo_plaga, 1, :id_usuarios
                        )
                    """), {
                        **plaga,
                        "id_tipo_plaga": acaro_tipo_id,
                        "id_usuarios": admin_user_id
                    })
                    print(f"✅ Ácaro '{plaga['nombre']}' insertado.")

            conn.commit()
            print(f"\n🎉 Plagas adicionales insertadas exitosamente!")
            
    except Exception as e:
        print(f"❌ Error al insertar plagas adicionales: {e}")

if __name__ == "__main__":
    print("🌽 Insertando plagas adicionales basadas en imágenes...")
    insertar_plagas_adicionales()
