<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Registro - MAIZGUARD</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <!-- Estilos personalizados -->
    <link rel="stylesheet" href="/static/css/main.css">
    <link rel="stylesheet" href="/static/css/navbar.css">
    <link rel="stylesheet" href="/static/css/compact.css">
    <link rel="stylesheet" href="/static/css/super-responsive.css">
    <link rel="stylesheet" href="/static/css/small-laptop.css">
    <link rel="preload" href="/static/images/Logo.jpeg" as="image">
</head>
<body class="d-flex flex-column min-vh-100">
    <!-- Incluir el navbar -->
    {% include "partials/navbar.html" %}

    <!-- Formulario de Registro -->
    <section class="py-2">
        <div class="container register-container">
            <div class="row justify-content-center">
                <div class="col-md-12">
                    <div class="card shadow-lg border-0 rounded-lg register-card">
                        <div class="card-header bg-success text-white text-center py-2">
                            <h4 class="mb-0 fw-bold">Crear una cuenta</h4>
                        </div>
                        {% if error %}
                        <div class="alert alert-danger alert-dismissible fade show m-3" role="alert">
                            {{ error }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                        {% endif %}
                        <div class="card-body p-2">
                            <form action="/registro" method="post" id="registration-form" novalidate>
                                <div class="mb-2">
                                    <label for="inputName" class="form-label">Nombre completo</label>
                                    <input class="form-control" id="inputName" type="text" name="name" placeholder="Ingresa tu nombre completo" required />
                                    <div class="invalid-feedback">Por favor, ingresa tu nombre completo.</div>
                                </div>
                                <div class="mb-2">
                                    <label for="inputEmail" class="form-label">Correo electrónico</label>
                                    <input class="form-control" id="inputEmail" type="email" name="email" placeholder="<EMAIL>" required pattern="[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,}$" />
                                    <div class="invalid-feedback">Por favor, ingresa un correo electrónico válido.</div>
                                </div>
                                <div class="mb-2">
                                    <label for="inputBirthdate" class="form-label">Fecha de nacimiento</label>
                                    <input class="form-control" id="inputBirthdate" type="date" name="birthdate" required />
                                    <div class="invalid-feedback">Por favor, selecciona tu fecha de nacimiento.</div>
                                </div>
                                <div class="mb-2">
                                    <label for="inputPassword" class="form-label">Contraseña</label>
                                    <div class="input-group">
                                        <input class="form-control" id="inputPassword" type="password" name="password" placeholder="Crea una contraseña" required minlength="8" />
                                        <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('inputPassword')" title="Mostrar/ocultar contraseña">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                    <div class="form-text small">La contraseña debe tener al menos 8 caracteres.</div>
                                    <div class="invalid-feedback d-block" id="passwordFeedback" style="display: none !important;">La contraseña debe tener al menos 8 caracteres.</div>
                                </div>
                                <div class="mb-2">
                                    <label for="inputPasswordConfirm" class="form-label">Confirmar contraseña</label>
                                    <div class="input-group">
                                        <input class="form-control" id="inputPasswordConfirm" type="password" name="passwordConfirm" placeholder="Confirma la contraseña" required />
                                        <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('inputPasswordConfirm')" title="Mostrar/ocultar contraseña">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                    <div class="invalid-feedback d-block" id="passwordConfirmFeedback" style="display: none !important;">Las contraseñas no coinciden.</div>
                                </div>
                                <div class="form-check mb-2">
                                    <input class="form-check-input" id="inputTerms" type="checkbox" name="terms" required />
                                    <label class="form-check-label small" for="inputTerms">
                                        Acepto los <a href="/terminos-condiciones" class="text-success">términos y condiciones</a>
                                    </label>
                                    <div class="invalid-feedback small">Debes aceptar los términos y condiciones.</div>
                                </div>
                                <div class="d-grid">
                                    <button class="btn btn-success btn-sm" type="submit" id="submitBtn">
                                        Registrarse
                                    </button>
                                </div>
                            </form>

                            <!-- Toast para notificaciones -->
                            <div class="position-fixed bottom-0 end-0 p-3" style="z-index: 11">
                                <div id="notificationToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
                                    <div class="toast-header">
                                        <strong class="me-auto" id="toastTitle">Notificación</strong>
                                        <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
                                    </div>
                                    <div class="toast-body" id="toastMessage">
                                        Mensaje de notificación
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer text-center py-3">
                            <div class="small">
                                ¿Ya tienes una cuenta? <a href="/login" class="text-success">Inicia sesión</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Incluir el footer -->
    {% include "partials/footer.html" %}

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Scripts personalizados -->
    <script src="/static/js/scripts.js"></script>
    <script src="/static/js/script.js"></script>
    <!-- Script de validación de registro -->
    <script src="/static/js/registro.js"></script>

    <!-- Script específico para ojitos de contraseña -->
    <script>
        function togglePassword(inputId) {
            const input = document.getElementById(inputId);
            const button = document.querySelector(`[onclick*="${inputId}"]`);
            const icon = button ? button.querySelector('i') : null;

            if (input && icon) {
                if (input.type === 'password') {
                    input.type = 'text';
                    icon.className = 'fas fa-eye-slash';
                } else {
                    input.type = 'password';
                    icon.className = 'fas fa-eye';
                }
            }
        }
    </script>
</body>
</html>
