#!/usr/bin/env python3
"""
Script para crear la tabla de cultivos si no existe
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from app.database.config import engine
from sqlalchemy import text

def crear_tabla_cultivos():
    """Crear la tabla de cultivos si no existe"""
    try:
        with engine.connect() as conn:
            # Verificar si la tabla cultivos existe
            result = conn.execute(text("""
                SELECT COUNT(*) as count 
                FROM information_schema.tables 
                WHERE table_schema = 'maizguard' AND table_name = 'cultivos'
            """))
            
            tabla_existe = result.fetchone().count > 0
            
            if tabla_existe:
                print("✅ La tabla 'cultivos' ya existe.")
                return
            
            print("🔧 Creando tabla 'cultivos'...")
            
            # Crear tabla cultivos
            conn.execute(text("""
                CREATE TABLE cultivos (
                    id_cultivo INT AUTO_INCREMENT PRIMARY KEY,
                    nombre VARCHAR(100) NOT NULL,
                    variedad VARCHAR(100),
                    url_img VARCHAR(255),
                    descripcion TEXT NOT NULL,
                    cuidados TEXT,
                    id_region INT NOT NULL,
                    id_temporada INT NOT NULL,
                    id_usuarios INT NOT NULL,
                    fecha_registro DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (id_region) REFERENCES regiones(id_region),
                    FOREIGN KEY (id_temporada) REFERENCES temporadas(id_temporada),
                    FOREIGN KEY (id_usuarios) REFERENCES usuarios(id_usuarios)
                )
            """))
            
            conn.commit()
            print("✅ Tabla 'cultivos' creada exitosamente.")
            
            # Verificar que se creó correctamente
            result = conn.execute(text("DESCRIBE cultivos"))
            campos = result.fetchall()
            
            print("\n📋 Estructura de la tabla 'cultivos':")
            for campo in campos:
                print(f"  📝 {campo.Field}: {campo.Type}")
            
    except Exception as e:
        print(f"❌ Error al crear tabla cultivos: {e}")

if __name__ == "__main__":
    print("🌽 Verificando y creando tabla de cultivos...")
    crear_tabla_cultivos()
