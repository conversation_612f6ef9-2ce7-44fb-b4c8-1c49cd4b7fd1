from app.database.config import engine
from sqlalchemy import text
import bcrypt
from datetime import datetime

def crear_admin():
    """
    Crea un usuario administrador en la base de datos.
    """
    try:
        with engine.connect() as conn:
            # Verificar si existe la tabla roles
            result = conn.execute(text("SHOW TABLES LIKE 'roles'"))
            if not result.fetchone():
                print("La tabla 'roles' no existe. Creando tabla...")
                conn.execute(text("""
                    CREATE TABLE roles (
                        id_rol INT PRIMARY KEY AUTO_INCREMENT,
                        nombre VARCHAR(50) NOT NULL,
                        descripcion VARCHAR(255)
                    )
                """))
                
                # Insertar roles básicos
                conn.execute(text("INSERT INTO roles (nombre, descripcion) VALUES ('usuario', 'Usuario regular')"))
                conn.execute(text("INSERT INTO roles (nombre, descripcion) VALUES ('admin', 'Administrador del sistema')"))
                conn.commit()
                print("Tabla 'roles' creada exitosamente.")
            
            # Verificar si ya existe un administrador
            result = conn.execute(text("""
                SELECT u.id_usuarios 
                FROM usuarios u
                JOIN roles r ON u.id_rol = r.id_rol
                WHERE r.nombre = 'admin'
            """))
            admin = result.fetchone()
            
            if admin:
                print(f"Ya existe un administrador con ID: {admin[0]}")
                return
            
            # Obtener el ID del rol de administrador
            result = conn.execute(text("SELECT id_rol FROM roles WHERE nombre = 'admin'"))
            admin_rol = result.fetchone()
            
            if not admin_rol:
                print("No se encontró el rol de administrador.")
                return
            
            admin_rol_id = admin_rol[0]
            
            # Obtener el ID del estado activo
            result = conn.execute(text("SELECT id_estados FROM estados WHERE descripcion = 'Activo'"))
            estado_activo = result.fetchone()
            
            if not estado_activo:
                print("No se encontró el estado 'Activo'. Creando estado...")
                conn.execute(text("INSERT INTO estados (descripcion) VALUES ('Activo')"))
                conn.commit()
                
                result = conn.execute(text("SELECT id_estados FROM estados WHERE descripcion = 'Activo'"))
                estado_activo = result.fetchone()
            
            estado_activo_id = estado_activo[0]
            
            # Datos del administrador
            nombre = "Admin"
            apellido = "MAIZGUARD"
            correo = "<EMAIL>"
            password = "admin123"  # Contraseña simple para pruebas
            
            # Verificar si el correo ya existe
            result = conn.execute(text("SELECT id_usuarios FROM usuarios WHERE correo_electronico = :correo"), {"correo": correo})
            if result.fetchone():
                print(f"Ya existe un usuario con el correo {correo}")
                return
            
            # Hashear la contraseña
            hashed_password = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
            
            # Insertar el administrador
            query = text("""
                INSERT INTO usuarios (
                    id_estados, 
                    id_rol, 
                    nombre, 
                    apellido, 
                    correo_electronico, 
                    contrasena, 
                    fecha_registro
                )
                VALUES (
                    :id_estados, 
                    :id_rol, 
                    :nombre, 
                    :apellido, 
                    :correo, 
                    :contrasena, 
                    NOW()
                )
            """)
            
            result = conn.execute(query, {
                "id_estados": estado_activo_id,
                "id_rol": admin_rol_id,
                "nombre": nombre,
                "apellido": apellido,
                "correo": correo,
                "contrasena": hashed_password
            })
            
            conn.commit()
            
            print(f"Administrador creado exitosamente.")
            print(f"Correo: {correo}")
            print(f"Contraseña: {password}")
            
    except Exception as e:
        print(f"Error al crear administrador: {e}")

if __name__ == "__main__":
    crear_admin()
