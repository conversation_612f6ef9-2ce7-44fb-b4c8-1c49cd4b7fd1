from app.database.config import engine
from sqlalchemy import text

def reiniciar_tipos_plaga():
    """
    Reinicia completamente los tipos de plaga.
    """
    try:
        with engine.connect() as conn:
            print("Reiniciando tipos de plaga...")
            
            # Primero, actualizar todas las plagas a un tipo temporal (ID 1)
            conn.execute(text("UPDATE plagas SET id_tipo_plaga = 1"))
            
            # Eliminar todos los tipos de plaga
            conn.execute(text("DELETE FROM tipos_plaga"))
            
            # Insertar los nuevos tipos
            conn.execute(text("INSERT INTO tipos_plaga (id_tipo_plaga, descripcion) VALUES (1, 'Insecto')"))
            conn.execute(text("INSERT INTO tipos_plaga (id_tipo_plaga, descripcion) VALUES (2, 'Ácaro')"))
            conn.execute(text("INSERT INTO tipos_plaga (id_tipo_plaga, descripcion) VALUES (3, 'Hongo')"))
            conn.execute(text("INSERT INTO tipos_plaga (id_tipo_plaga, descripcion) VALUES (4, 'Bacteria')"))
            conn.execute(text("INSERT INTO tipos_plaga (id_tipo_plaga, descripcion) VALUES (5, 'Virus')"))
            
            # Actualizar las plagas con los tipos correctos
            print("\nActualizando categorías de plagas...")
            
            # Gusano cogollero y Gusano elotero: Insecto
            conn.execute(text("""
                UPDATE plagas SET id_tipo_plaga = 1
                WHERE nombre LIKE '%Gusano%'
            """))
            
            # Araña roja: Ácaro
            conn.execute(text("""
                UPDATE plagas SET id_tipo_plaga = 2
                WHERE nombre LIKE '%Araña%'
            """))
            
            # Roya del maíz y Tizón foliar: Hongo
            conn.execute(text("""
                UPDATE plagas SET id_tipo_plaga = 3
                WHERE nombre LIKE '%Roya%' OR nombre LIKE '%Tizón%'
            """))
            
            # Confirmar cambios
            conn.commit()
            
            # Mostrar tipos de plaga actualizados
            print("\nTIPOS DE PLAGA REINICIADOS:")
            result = conn.execute(text("SELECT * FROM tipos_plaga ORDER BY id_tipo_plaga"))
            tipos = result.fetchall()
            for tipo in tipos:
                print(f"ID: {tipo[0]}, Descripción: {tipo[1]}")
            
            # Mostrar plagas actualizadas
            print("\nPLAGAS ACTUALIZADAS:")
            result = conn.execute(text("""
                SELECT p.id_plaga, p.nombre, tp.descripcion as tipo
                FROM plagas p
                LEFT JOIN tipos_plaga tp ON p.id_tipo_plaga = tp.id_tipo_plaga
                ORDER BY p.id_plaga
            """))
            plagas = result.fetchall()
            for plaga in plagas:
                print(f"ID: {plaga[0]}, Nombre: {plaga[1]}, Tipo: {plaga[2]}")
            
    except Exception as e:
        print(f"Error al reiniciar tipos de plaga: {e}")

if __name__ == "__main__":
    reiniciar_tipos_plaga()
