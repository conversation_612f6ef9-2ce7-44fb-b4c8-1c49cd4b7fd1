from app.database.config import engine
from sqlalchemy import text

def actualizar_categorias():
    """
    Actualiza las categorías de plagas según los requerimientos.
    """
    try:
        with engine.connect() as conn:
            # 1. Agregar nuevas temporadas (lluvia y sequía)
            print("Agregando nuevas temporadas...")
            
            # Verificar si ya existen las temporadas de lluvia y sequía
            result = conn.execute(text("SELECT id_temporada FROM temporadas WHERE descripcion IN ('Temporada de lluvia', 'Temporada de sequía')"))
            if not result.fetchone():
                # Insertar nuevas temporadas
                conn.execute(text("INSERT INTO temporadas (descripcion) VALUES ('Temporada de lluvia')"))
                conn.execute(text("INSERT INTO temporadas (descripcion) VALUES ('Temporada de sequía')"))
                print("Nuevas temporadas agregadas correctamente.")
            else:
                print("Las temporadas de lluvia y sequía ya existen.")
            
            # 2. Agregar tipos de plaga faltantes
            print("\nVerificando tipos de plaga...")
            result = conn.execute(text("SELECT descripcion FROM tipos_plaga"))
            tipos_existentes = [row[0] for row in result.fetchall()]
            
            tipos_requeridos = ['Bacteria', 'Hongo', 'Virus', 'Insecto', 'Maleza']
            tipos_faltantes = [tipo for tipo in tipos_requeridos if tipo not in tipos_existentes]
            
            if tipos_faltantes:
                for tipo in tipos_faltantes:
                    conn.execute(text(f"INSERT INTO tipos_plaga (descripcion) VALUES ('{tipo}')"))
                print(f"Tipos de plaga agregados: {', '.join(tipos_faltantes)}")
            else:
                print("Todos los tipos de plaga requeridos ya existen.")
            
            # 3. Agregar fases de cultivo faltantes
            print("\nVerificando fases de cultivo...")
            result = conn.execute(text("SELECT descripcion FROM fases_cultivo"))
            fases_existentes = [row[0] for row in result.fetchall()]
            
            fases_requeridas = ['Germinación', 'Crecimiento vegetativo', 'Floración', 'Maduración', 'Cosecha']
            fases_faltantes = [fase for fase in fases_requeridas if fase not in fases_existentes]
            
            if fases_faltantes:
                for fase in fases_faltantes:
                    conn.execute(text(f"INSERT INTO fases_cultivo (descripcion) VALUES ('{fase}')"))
                print(f"Fases de cultivo agregadas: {', '.join(fases_faltantes)}")
            else:
                print("Todas las fases de cultivo requeridas ya existen.")
            
            # 4. Actualizar plagas existentes para usar las nuevas categorías
            print("\nActualizando plagas existentes...")
            
            # Obtener IDs de las categorías
            result = conn.execute(text("SELECT id_fase_cultivo, descripcion FROM fases_cultivo WHERE descripcion IN ('Maduración', 'Cosecha')"))
            fases_dict = {row[1]: row[0] for row in result.fetchall()}
            
            result = conn.execute(text("SELECT id_temporada, descripcion FROM temporadas WHERE descripcion IN ('Temporada de lluvia', 'Temporada de sequía')"))
            temporadas_dict = {row[1]: row[0] for row in result.fetchall()}
            
            result = conn.execute(text("SELECT id_tipo_plaga, descripcion FROM tipos_plaga WHERE descripcion IN ('Bacteria', 'Hongo')"))
            tipos_dict = {row[1]: row[0] for row in result.fetchall()}
            
            # Obtener plagas existentes
            result = conn.execute(text("SELECT id_plaga, nombre FROM plagas"))
            plagas = result.fetchall()
            
            # Actualizar cada plaga con nuevas categorías
            for plaga in plagas:
                id_plaga = plaga[0]
                nombre = plaga[1]
                
                # Asignar categorías según el nombre o ID de la plaga (ejemplo)
                if "roya" in nombre.lower() or "tizón" in nombre.lower():
                    # Hongos en temporada de lluvia, fase de maduración
                    id_tipo = tipos_dict.get('Hongo', 2)  # Usar ID 2 como fallback
                    id_temporada = temporadas_dict.get('Temporada de lluvia', 6)  # Usar nuevo ID como fallback
                    id_fase = fases_dict.get('Maduración', 4)  # Usar ID 4 como fallback
                elif "gusano" in nombre.lower() or "araña" in nombre.lower():
                    # Insectos en temporada de sequía, fase de cosecha
                    id_tipo = 1  # Insecto
                    id_temporada = temporadas_dict.get('Temporada de sequía', 7)  # Usar nuevo ID como fallback
                    id_fase = fases_dict.get('Cosecha', 5)  # Usar ID 5 como fallback
                else:
                    # Valores predeterminados
                    id_tipo = 1  # Insecto
                    id_temporada = temporadas_dict.get('Temporada de lluvia', 6)  # Usar nuevo ID como fallback
                    id_fase = fases_dict.get('Maduración', 4)  # Usar ID 4 como fallback
                
                # Actualizar la plaga
                conn.execute(
                    text("UPDATE plagas SET id_fase_cultivo = :fase, id_temporada = :temporada, id_tipo_plaga = :tipo WHERE id_plaga = :id"),
                    {"fase": id_fase, "temporada": id_temporada, "tipo": id_tipo, "id": id_plaga}
                )
                print(f"Plaga actualizada: {nombre}")
            
            # Confirmar cambios
            conn.commit()
            
            # Mostrar categorías actualizadas
            print("\nCategorías disponibles:")
            
            print("\nFASES DE CULTIVO:")
            result = conn.execute(text("SELECT * FROM fases_cultivo"))
            fases = result.fetchall()
            for fase in fases:
                print(f"ID: {fase[0]}, Descripción: {fase[1]}")
            
            print("\nTEMPORADAS:")
            result = conn.execute(text("SELECT * FROM temporadas"))
            temporadas = result.fetchall()
            for temporada in temporadas:
                print(f"ID: {temporada[0]}, Descripción: {temporada[1]}")
            
            print("\nTIPOS DE PLAGA:")
            result = conn.execute(text("SELECT * FROM tipos_plaga"))
            tipos = result.fetchall()
            for tipo in tipos:
                print(f"ID: {tipo[0]}, Descripción: {tipo[1]}")
            
            # Mostrar plagas actualizadas
            print("\nPLAGAS ACTUALIZADAS:")
            result = conn.execute(text("""
                SELECT p.id_plaga, p.nombre, fc.descripcion as fase, t.descripcion as temporada, tp.descripcion as tipo
                FROM plagas p
                LEFT JOIN fases_cultivo fc ON p.id_fase_cultivo = fc.id_fase_cultivo
                LEFT JOIN temporadas t ON p.id_temporada = t.id_temporada
                LEFT JOIN tipos_plaga tp ON p.id_tipo_plaga = tp.id_tipo_plaga
                ORDER BY p.id_plaga
            """))
            plagas = result.fetchall()
            for plaga in plagas:
                print(f"ID: {plaga[0]}, Nombre: {plaga[1]}, Fase: {plaga[2]}, Temporada: {plaga[3]}, Tipo: {plaga[4]}")
            
    except Exception as e:
        print(f"Error al actualizar categorías: {e}")

if __name__ == "__main__":
    actualizar_categorias()
