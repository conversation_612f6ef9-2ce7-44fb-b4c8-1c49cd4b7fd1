# app/models/models.py

from sqlalchemy import (
    Table, Column, Integer, String, DateTime, Text, LargeBinary, ForeignKey, func
)
from app.models.database import metadata

# Tabla: usuarios
usuarios = Table(
    "usuarios",
    metadata,
    Column("id_usuarios", Integer, primary_key=True, autoincrement=True),
    <PERSON>umn("id_estados", Integer, ForeignKey("estados.id_estados"), nullable=False),
    <PERSON>umn("id_rol", Integer, ForeignKey("roles.id_rol"), nullable=False, server_default="2"),
    <PERSON>umn("nombre", String(40), nullable=False),
    <PERSON>umn("apellido", String(40), nullable=False),
    <PERSON>umn("contrasena", String(255), nullable=False),
    <PERSON><PERSON>n("correo_electronico", String(100), nullable=False, unique=True),
    <PERSON>umn("fotografia", String(255)),
    Column("fecha_registro", DateTime, server_default=func.now()),
    <PERSON>umn("token_recuperacion", String(250)),
    <PERSON>umn("fecha_expiracion_token", DateTime)
)


# Tabla: estados
estados = Table(
    "estados",
    metadata,
    Column("id_estados", Integer, primary_key=True, autoincrement=True),
    Column("nombre_estado", String(1000), nullable=False)
)

roles = Table(
    "roles",
    metadata,
    Column("id_rol", Integer, primary_key=True, autoincrement=True),
    Column("nombre_rol", String(50), nullable=False, unique=True)
)


# Tabla: fase_cultivo
fase_cultivo = Table(
    "fase_cultivo",
    metadata,
    Column("id_fase_cultivo", Integer, primary_key=True, autoincrement=True),
    Column("descripcion", String(250), nullable=False)
)

# Tabla: registro_cultivo
registro_cultivo = Table(
    "registro_cultivo",
    metadata,
    Column("id_registro_cultivo", Integer, primary_key=True, autoincrement=True),
    Column("id_fase_cultivo", Integer, ForeignKey("fase_cultivo.id_fase_cultivo"), nullable=False),
    Column("id_usuarios", Integer, ForeignKey("usuarios.id_usuarios"), nullable=False),
    Column("ancho", Integer),  # DECIMAL no se usa en SQLAlchemy directamente, Integer es más compatible
    Column("largo", Integer),
    Column("url_img", String(255), nullable=False),
    Column("descripcion", Text),
    Column("id_estados", Integer, ForeignKey("estados.id_estados"), nullable=False),
    Column("fecha_registro", DateTime, server_default=func.now())
)


# Tabla: tipo_plaga
tipo_plaga = Table(
    "tipo_plaga",
    metadata,
    Column("id_tipo_plaga", Integer, primary_key=True, autoincrement=True),
    Column("nombre", String(45), nullable=False)
)

# Tabla: temporada
temporada = Table(
    "temporada",
    metadata,
    Column("id_temporada", Integer, primary_key=True, autoincrement=True),
    Column("nombre", String(45), nullable=False)
)

# Tabla: plaga
plaga = Table(
    "plaga",
    metadata,
    Column("id_plaga", Integer, primary_key=True, autoincrement=True),
    Column("id_usuarios", Integer, ForeignKey("usuarios.id_usuarios"), nullable=False),
    Column("nombre", String(60), nullable=False),
    Column("nombre_cientifico", String(100), nullable=False),
    Column("url_img", String(255), nullable=False),
    Column("descripcion", Text, nullable=False),
    Column("recomendaciones", Text, nullable=False),
    Column("id_fase_cultivo", Integer, ForeignKey("fase_cultivo.id_fase_cultivo"), nullable=False),
    Column("id_tipo_plaga", Integer, ForeignKey("tipo_plaga.id_tipo_plaga"), nullable=False),
    Column("id_temporada", Integer, ForeignKey("temporada.id_temporada"), nullable=False)
)


# Tabla: registro_plaga
registro_plaga = Table(
    "registro_plaga",
    metadata,
    Column("id_registro_plaga", Integer, primary_key=True, autoincrement=True),
    Column("id_usuarios", Integer, ForeignKey("usuarios.id_usuarios"), nullable=False),
    Column("id_registro_cultivo", Integer, ForeignKey("registro_cultivo.id_registro_cultivo"), nullable=False),
    Column("id_plaga", Integer, ForeignKey("plaga.id_plaga"), nullable=False),
    Column("url_img", String(255), nullable=False),
    Column("descripcion", Text, nullable=False),
    Column("fecha_hora", DateTime, server_default=func.now())
)


# Tabla: Consultas
consultas = Table(
    "consultas",
    metadata,
    Column("id_consultas", Integer, primary_key=True, autoincrement=True),
    Column("id_plaga", Integer, ForeignKey("plaga.id_plaga"), nullable=False),
    Column("id_usuarios", Integer, ForeignKey("usuarios.id_usuarios"), nullable=False),
    Column("fecha_consulta", DateTime, server_default=func.now())
)

