from app.database.config import engine
from sqlalchemy import text

def check_consultas_table():
    try:
        with engine.connect() as conn:
            # Verificar si la tabla consultas existe
            result = conn.execute(text("SHOW TABLES LIKE 'consultas'"))
            exists = bool(result.fetchone())
            print(f"La tabla 'consultas' existe: {exists}")
            
            if exists:
                # Mostrar estructura de la tabla
                result = conn.execute(text("DESCRIBE consultas"))
                columns = result.fetchall()
                print("\nEstructura de la tabla 'consultas':")
                for column in columns:
                    print(f"- {column[0]}: {column[1]}")
                
                # Contar registros
                result = conn.execute(text("SELECT COUNT(*) FROM consultas"))
                count = result.scalar()
                print(f"\nNúmero de consultas registradas: {count}")
                
                # Mostrar algunas consultas si existen
                if count > 0:
                    result = conn.execute(text("""
                        SELECT c.id_consultas, c.id_plaga, c.id_usuarios, c.fecha_consulta, 
                               u.nombre, u.apellido, p.nombre as nombre_plaga
                        FROM consultas c
                        JOIN usuarios u ON c.id_usuarios = u.id_usuarios
                        JOIN plaga p ON c.id_plaga = p.id_plaga
                        ORDER BY c.fecha_consulta DESC
                        LIMIT 5
                    """))
                    consultas = result.fetchall()
                    print("\nÚltimas consultas registradas:")
                    for consulta in consultas:
                        print(f"ID: {consulta[0]}, Plaga: {consulta[6]} (ID: {consulta[1]}), Usuario: {consulta[4]} {consulta[5]} (ID: {consulta[2]}), Fecha: {consulta[3]}")
            else:
                print("\nLa tabla 'consultas' no existe. Necesitamos crearla.")
    except Exception as e:
        print(f"Error al verificar la tabla 'consultas': {e}")

if __name__ == "__main__":
    check_consultas_table()
