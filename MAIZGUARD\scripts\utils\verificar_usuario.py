#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script para verificar si un usuario existe en la base de datos.
"""

import os
import sys
import argparse

# Añadir el directorio raíz al path para poder importar los módulos de la aplicación
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

# Importar la configuración de la base de datos
from app.database.config import get_db
from app.database.models import User

def verificar_usuario(email):
    """
    Verifica si un usuario con el correo electrónico especificado existe en la base de datos.
    """
    print(f"Verificando si el usuario con correo '{email}' existe en la base de datos...")
    
    # Obtener una sesión de la base de datos
    db_gen = get_db()
    if db_gen is None:
        print("❌ No se pudo conectar a la base de datos.")
        return False
    
    db = next(db_gen)
    
    try:
        # Buscar el usuario por correo electrónico
        user = db.query(User).filter(User.correo_electronico == email).first()
        
        if user:
            print("✅ Usuario encontrado:")
            print(f"ID: {user.id_usuarios}")
            print(f"Nombre: {user.nombre} {user.apellido}")
            print(f"Correo: {user.correo_electronico}")
            print(f"Rol: {user.id_rol}")
            print(f"Estado: {user.id_estados}")
            print(f"Fecha de registro: {user.fecha_registro}")
            return True
        else:
            print(f"❌ No se encontró ningún usuario con el correo '{email}'.")
            return False
    
    except Exception as e:
        print(f"❌ Error al verificar usuario: {e}")
        return False
    
    finally:
        db.close()

def listar_usuarios():
    """
    Lista todos los usuarios registrados en la base de datos.
    """
    print("\nUsuarios registrados en la base de datos:")
    
    # Obtener una sesión de la base de datos
    db_gen = get_db()
    if db_gen is None:
        print("❌ No se pudo conectar a la base de datos.")
        return
    
    db = next(db_gen)
    
    try:
        # Obtener todos los usuarios
        users = db.query(User).all()
        
        if not users:
            print("No hay usuarios registrados.")
            return
        
        # Mostrar información de cada usuario
        for user in users:
            print(f"ID: {user.id_usuarios}")
            print(f"Nombre: {user.nombre} {user.apellido}")
            print(f"Correo: {user.correo_electronico}")
            print(f"Rol: {user.id_rol}")
            print(f"Estado: {user.id_estados}")
            print(f"Fecha de registro: {user.fecha_registro}")
            print("-" * 30)
    
    except Exception as e:
        print(f"❌ Error al listar usuarios: {e}")
    
    finally:
        db.close()

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Verificar si un usuario existe en la base de datos.')
    parser.add_argument('--email', type=str, help='Correo electrónico del usuario a verificar')
    parser.add_argument('--list', action='store_true', help='Listar todos los usuarios')
    
    args = parser.parse_args()
    
    if args.email:
        verificar_usuario(args.email)
    
    if args.list or not args.email:
        listar_usuarios()
