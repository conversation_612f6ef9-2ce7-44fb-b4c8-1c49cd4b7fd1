from app.database.config import engine
from sqlalchemy import text

def create_consultas_table():
    try:
        with engine.connect() as conn:
            # Verificar si la tabla ya existe
            result = conn.execute(text("SHOW TABLES LIKE 'consultas'"))
            if result.fetchone():
                print("La tabla 'consultas' ya existe.")
                return

            # Crear la tabla consultas
            conn.execute(text("""
                CREATE TABLE consultas (
                    id_consultas INT AUTO_INCREMENT PRIMARY KEY,
                    id_plaga INT NOT NULL,
                    id_usuarios INT NOT NULL,
                    fecha_consulta DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (id_plaga) REFERENCES plagas(id_plaga),
                    FOREIGN KEY (id_usuarios) REFERENCES usuarios(id_usuarios)
                )
            """))

            print("Tabla 'consultas' creada exitosamente.")
    except Exception as e:
        print(f"Error al crear la tabla 'consultas': {e}")

if __name__ == "__main__":
    create_consultas_table()
