from app.database.config import engine
from sqlalchemy import text

def actualizar_categorias():
    """
    Actualiza las categorías de plagas según los requerimientos.
    """
    try:
        with engine.connect() as conn:
            # 1. Actualizar temporadas para incluir lluvia y sequía
            print("Actualizando temporadas...")
            
            # Verificar si ya existen las temporadas de lluvia y sequía
            result = conn.execute(text("SELECT id_temporada FROM temporadas WHERE descripcion IN ('Temporada de lluvia', 'Temporada de sequía')"))
            if not result.fetchone():
                # Eliminar temporadas existentes y crear nuevas
                conn.execute(text("DELETE FROM temporadas"))
                
                # Insertar nuevas temporadas
                conn.execute(text("INSERT INTO temporadas (descripcion) VALUES ('Temporada de lluvia')"))
                conn.execute(text("INSERT INTO temporadas (descripcion) VALUES ('Temporada de sequía')"))
                conn.execute(text("INSERT INTO temporadas (descripcion) VALUES ('Ambas temporadas')"))
                print("Temporadas actualizadas correctamente.")
            else:
                print("Las temporadas de lluvia y sequía ya existen.")
            
            # 2. Verificar tipos de plaga (bacterias, hongos, etc.)
            print("\nVerificando tipos de plaga...")
            result = conn.execute(text("SELECT id_tipo_plaga, descripcion FROM tipos_plaga"))
            tipos = result.fetchall()
            
            tipos_requeridos = ['Bacteria', 'Hongo', 'Virus', 'Insecto', 'Maleza']
            tipos_existentes = [tipo[1] for tipo in tipos]
            
            # Verificar si todos los tipos requeridos existen
            todos_existen = all(tipo in tipos_existentes for tipo in tipos_requeridos)
            
            if not todos_existen:
                # Eliminar tipos existentes y crear nuevos
                conn.execute(text("DELETE FROM tipos_plaga"))
                
                # Insertar nuevos tipos
                for tipo in tipos_requeridos:
                    conn.execute(text(f"INSERT INTO tipos_plaga (descripcion) VALUES ('{tipo}')"))
                print("Tipos de plaga actualizados correctamente.")
            else:
                print("Todos los tipos de plaga requeridos ya existen.")
            
            # 3. Verificar fases de cultivo (maduración y cosecha)
            print("\nVerificando fases de cultivo...")
            result = conn.execute(text("SELECT id_fase_cultivo, descripcion FROM fases_cultivo"))
            fases = result.fetchall()
            
            fases_requeridas = ['Germinación', 'Crecimiento vegetativo', 'Floración', 'Maduración', 'Cosecha']
            fases_existentes = [fase[1] for fase in fases]
            
            # Verificar si todas las fases requeridas existen
            todas_existen = all(fase in fases_existentes for fase in fases_requeridas)
            
            if not todas_existen:
                # Eliminar fases existentes y crear nuevas
                conn.execute(text("DELETE FROM fases_cultivo"))
                
                # Insertar nuevas fases
                for fase in fases_requeridas:
                    conn.execute(text(f"INSERT INTO fases_cultivo (descripcion) VALUES ('{fase}')"))
                print("Fases de cultivo actualizadas correctamente.")
            else:
                print("Todas las fases de cultivo requeridas ya existen.")
            
            # Confirmar cambios
            conn.commit()
            
            # Mostrar categorías actualizadas
            print("\nCategorías actualizadas:")
            
            print("\nFASES DE CULTIVO:")
            result = conn.execute(text("SELECT * FROM fases_cultivo"))
            fases = result.fetchall()
            for fase in fases:
                print(f"ID: {fase[0]}, Descripción: {fase[1]}")
            
            print("\nTEMPORADAS:")
            result = conn.execute(text("SELECT * FROM temporadas"))
            temporadas = result.fetchall()
            for temporada in temporadas:
                print(f"ID: {temporada[0]}, Descripción: {temporada[1]}")
            
            print("\nTIPOS DE PLAGA:")
            result = conn.execute(text("SELECT * FROM tipos_plaga"))
            tipos = result.fetchall()
            for tipo in tipos:
                print(f"ID: {tipo[0]}, Descripción: {tipo[1]}")
            
    except Exception as e:
        print(f"Error al actualizar categorías: {e}")

if __name__ == "__main__":
    actualizar_categorias()
