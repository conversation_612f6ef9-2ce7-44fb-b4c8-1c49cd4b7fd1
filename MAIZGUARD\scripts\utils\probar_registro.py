#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script para probar el registro de un usuario directamente en la base de datos.
"""

import os
import sys
import bcrypt
from datetime import datetime

# Añadir el directorio raíz al path para poder importar los módulos de la aplicación
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

# Importar la configuración de la base de datos
from app.database.config import get_db
from app.database.models import User

def registrar_usuario_prueba():
    """
    Registra un usuario de prueba en la base de datos.
    """
    print("Registrando usuario de prueba...")
    
    # Obtener una sesión de la base de datos
    db_gen = get_db()
    if db_gen is None:
        print("❌ No se pudo conectar a la base de datos.")
        return False
    
    db = next(db_gen)
    
    try:
        # Verificar si el correo ya está registrado
        email = "<EMAIL>"
        existing_user = db.query(User).filter(User.correo_electronico == email).first()
        
        if existing_user:
            print(f"❌ El correo {email} ya está registrado.")
            print(f"Usuario existente: ID {existing_user.id_usuarios}, Nombre: {existing_user.nombre} {existing_user.apellido}")
            return False
        
        # Datos del usuario de prueba
        nombre = "Usuario"
        apellido = "De Prueba"
        password = "password123"
        
        # Hashear la contraseña con bcrypt
        hashed_password = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
        
        # Crear el nuevo usuario
        new_user = User(
            nombre=nombre,
            apellido=apellido,
            correo_electronico=email,
            contrasena=hashed_password,
            id_estados=1,  # Activo
            id_rol=2       # Usuario regular
        )
        
        # Guardar en la base de datos
        db.add(new_user)
        db.commit()
        db.refresh(new_user)
        
        print(f"✅ Usuario registrado exitosamente con ID: {new_user.id_usuarios}")
        print(f"Nombre: {new_user.nombre} {new_user.apellido}")
        print(f"Correo: {new_user.correo_electronico}")
        print(f"Fecha de registro: {new_user.fecha_registro}")
        
        return True
    
    except Exception as e:
        db.rollback()
        print(f"❌ Error al registrar usuario: {e}")
        return False
    
    finally:
        db.close()

def listar_usuarios():
    """
    Lista todos los usuarios registrados en la base de datos.
    """
    print("\nUsuarios registrados en la base de datos:")
    
    # Obtener una sesión de la base de datos
    db_gen = get_db()
    if db_gen is None:
        print("❌ No se pudo conectar a la base de datos.")
        return
    
    db = next(db_gen)
    
    try:
        # Obtener todos los usuarios
        users = db.query(User).all()
        
        if not users:
            print("No hay usuarios registrados.")
            return
        
        # Mostrar información de cada usuario
        for user in users:
            print(f"ID: {user.id_usuarios}")
            print(f"Nombre: {user.nombre} {user.apellido}")
            print(f"Correo: {user.correo_electronico}")
            print(f"Rol: {user.id_rol}")
            print(f"Estado: {user.id_estados}")
            print(f"Fecha de registro: {user.fecha_registro}")
            print("-" * 30)
    
    except Exception as e:
        print(f"❌ Error al listar usuarios: {e}")
    
    finally:
        db.close()

if __name__ == "__main__":
    registrar_usuario_prueba()
    listar_usuarios()
