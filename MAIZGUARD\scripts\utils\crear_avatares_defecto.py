#!/usr/bin/env python3
"""
Script para crear avatares por defecto para los usuarios
"""

import os
import shutil

def crear_avatares_defecto():
    """Crear avatares por defecto en la carpeta de imágenes"""
    try:
        # Directorio de destino para avatares
        avatares_dir = "app/static/images/avatares"
        
        # Crear directorio si no existe
        os.makedirs(avatares_dir, exist_ok=True)
        
        # Lista de avatares por defecto (usando imágenes existentes como base)
        avatares_info = [
            {
                "nombre": "avatar-default-1.jpg",
                "descripcion": "Avatar por defecto 1 - Icono de usuario"
            },
            {
                "nombre": "avatar-default-2.jpg", 
                "descripcion": "Avatar por defecto 2 - Agricultor"
            },
            {
                "nombre": "avatar-default-3.jpg",
                "descripcion": "Avatar por defecto 3 - Profesional"
            }
        ]
        
        # Copiar el icono de perfil existente como avatar por defecto
        icono_perfil = "app/static/images/perfil icono.jpg"
        if os.path.exists(icono_perfil):
            for i, avatar in enumerate(avatares_info, 1):
                destino = os.path.join(avatares_dir, f"avatar-default-{i}.jpg")
                if not os.path.exists(destino):
                    shutil.copy2(icono_perfil, destino)
                    print(f"✅ Avatar por defecto {i} creado: {destino}")
                else:
                    print(f"⚠️  Avatar por defecto {i} ya existe: {destino}")
        
        print(f"\n🎉 Avatares por defecto configurados en: {avatares_dir}")
        
        # Listar avatares disponibles
        if os.path.exists(avatares_dir):
            avatares = [f for f in os.listdir(avatares_dir) if f.lower().endswith(('.jpg', '.jpeg', '.png', '.gif'))]
            print(f"\n📋 Avatares disponibles ({len(avatares)}):")
            for avatar in sorted(avatares):
                print(f"  🖼️  {avatar}")
        
    except Exception as e:
        print(f"❌ Error al crear avatares por defecto: {e}")

if __name__ == "__main__":
    print("🌽 Creando avatares por defecto para MaizGuard...")
    crear_avatares_defecto()
