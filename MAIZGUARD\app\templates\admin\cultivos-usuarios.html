<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cultivos de Usuarios - MAIZGUARD</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Estilos personalizados - EXACTAMENTE IGUAL QUE INICIO -->
    <link rel="stylesheet" href="/static/css/main.css">
    <link rel="stylesheet" href="/static/css/navbar.css">
    <link rel="stylesheet" href="/static/css/compact.css">
    <link rel="stylesheet" href="/static/css/super-responsive.css">
    <link rel="stylesheet" href="/static/css/small-laptop.css">
    <link rel="stylesheet" href="/static/css/admin.css">
    <link rel="stylesheet" href="/static/css/admin-panel.css">
</head>
<body id="admin-app" class="admin-page">
    <!-- Incluir el navbar normal -->
    {% include "partials/navbar.html" %}

    <!-- Estilos específicos para navbar del admin -->
    <style>
        .navbar {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
            padding-top: 0.5rem !important;
            padding-bottom: 0.5rem !important;
            min-height: 65px !important;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2) !important;
            border-bottom: 3px solid #ffc107 !important;
        }

        .rounded-logo {
            height: 40px !important;
            width: 40px !important;
            margin-right: 10px !important;
            left: 0 !important;
            border: 2px solid white !important;
            box-shadow: 0 0 5px rgba(0, 0, 0, 0.2) !important;
        }

        .navbar-brand {
            font-size: 1.3rem !important;
            margin-left: 0 !important;
            font-weight: bold !important;
        }

        .nav-link {
            font-weight: 600 !important;
            font-size: 1.1rem !important;
            padding: 0.6rem 1rem !important;
            border-radius: 8px !important;
            transition: all 0.3s ease !important;
        }

        .nav-link:hover {
            background-color: rgba(255, 255, 255, 0.1) !important;
            transform: translateY(-2px) !important;
        }

        .search-form .form-control {
            border-radius: 20px 0 0 20px !important;
            border: none !important;
            padding: 0.7rem 1.2rem !important;
        }

        .search-form .btn {
            border-radius: 0 20px 20px 0 !important;
            border: none !important;
            background: #ffc107 !important;
            color: white !important;
            padding: 0.7rem 1.2rem !important;
        }
    </style>

    <!-- Contenedor principal sin espaciado extra -->
    <div class="container-fluid admin-container">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block admin-sidebar">
                <div class="sidebar-content">
                    <div class="text-center mb-4">
                        <h5 class="text-white">Panel de Administración</h5>
                        <p class="text-light small">Bienvenido, {{ user.nombre }}</p>
                    </div>

                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="sidebar-link" href="/admin">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="sidebar-link" href="/admin/usuarios">
                                <i class="fas fa-users me-2"></i>
                                Usuarios
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="sidebar-link active" href="/admin/cultivos-usuarios">
                                <i class="fas fa-seedling me-2"></i>
                                Cultivos de Usuarios
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="sidebar-link" href="/admin/plagas">
                                <i class="fas fa-bug me-2"></i>
                                Plagas
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="sidebar-link" href="/">
                                <i class="fas fa-home me-2"></i>
                                Volver al Inicio
                            </a>
                        </li>
                        <li class="nav-item mt-3">
                            <a class="sidebar-link text-danger" href="/logout">
                                <i class="fas fa-sign-out-alt me-2"></i>
                                Cerrar Sesión
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Contenido principal -->
            <main class="col-md-9 col-lg-10 admin-main-content">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Cultivos de Usuarios</h1>
                    <button class="btn btn-warning" onclick="reiniciarIdsCultivos()">
                        <i class="fas fa-redo me-2"></i>Reiniciar IDs
                    </button>
                </div>

                <!-- Estadísticas de cultivos -->
                <div class="row mb-4">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <div class="text-white-50 small">Total Cultivos</div>
                                        <div class="h4 mb-0" id="total-cultivos">Cargando...</div>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-seedling fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <div class="text-white-50 small">En Maduración</div>
                                        <div class="h4 mb-0" id="cultivos-maduracion">Cargando...</div>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-leaf fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <div class="text-white-50 small">En Cosecha</div>
                                        <div class="h4 mb-0" id="cultivos-cosecha">Cargando...</div>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-cut fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <div class="text-white-50 small">Usuarios con Cultivos</div>
                                        <div class="h4 mb-0" id="usuarios-con-cultivos">Cargando...</div>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-users fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filtros y búsqueda -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3 bg-gradient-primary">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <h6 class="m-0 font-weight-bold text-white">
                                    <i class="fas fa-seedling me-2"></i>Cultivos Registrados por Usuarios
                                </h6>
                            </div>
                            <div class="col-md-6">
                                <div class="input-group">
                                    <input type="text" class="form-control" id="buscarCultivo" placeholder="Buscar cultivo o usuario...">
                                    <button class="btn btn-outline-light" type="button" onclick="filtrarCultivos()">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <!-- Filtros adicionales -->
                        <div class="row p-3 bg-light border-bottom">
                            <div class="col-md-3">
                                <select class="form-select form-select-sm" id="filtroFase" onchange="filtrarCultivos()">
                                    <option value="">Todas las fases</option>
                                    <option value="maduracion">Maduración</option>
                                    <option value="cosecha">Cosecha</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select form-select-sm" id="filtroOrden" onchange="ordenarCultivos()">
                                    <option value="fecha_desc">Más recientes</option>
                                    <option value="fecha_asc">Más antiguos</option>
                                    <option value="nombre_asc">Nombre A-Z</option>
                                    <option value="usuario_asc">Usuario A-Z</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-outline-success btn-sm" onclick="exportarCultivos()">
                                    <i class="fas fa-download me-1"></i>Exportar
                                </button>
                            </div>
                            <div class="col-md-3 text-end">
                                <span class="badge bg-info" id="contadorCultivos">0 cultivos</span>
                            </div>
                        </div>

                        <!-- Grid de cultivos mejorado -->
                        <div class="p-3">
                            <div class="row" id="cultivos-grid">
                                <div class="col-12 text-center py-5">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Cargando...</span>
                                    </div>
                                    <p class="mt-2 text-muted">Cargando cultivos...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Mensaje si no hay cultivos -->
                <div id="mensaje-sin-cultivos" class="card text-center" style="display: none;">
                    <div class="card-body">
                        <i class="fas fa-seedling fa-3x text-muted mb-3"></i>
                        <h5 class="card-title">No hay cultivos registrados</h5>
                        <p class="card-text text-muted">
                            Los usuarios aún no han registrado cultivos en el sistema. 
                            Cuando los usuarios registren sus cultivos, aparecerán aquí.
                        </p>
                        <a href="/cultivos" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Ir a Registro de Cultivos
                        </a>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Modal para ver detalle del cultivo -->
    <div class="modal fade" id="modalDetalleCultivo" tabindex="-1" aria-labelledby="modalDetalleCultivoLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalDetalleCultivoLabel">Detalle del Cultivo</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="detalle-cultivo-content">
                    <!-- Contenido del detalle se carga dinámicamente -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- JavaScript para cultivos -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            cargarCultivos();
            cargarEstadisticasCultivos();
        });

        async function cargarCultivos() {
            try {
                const response = await fetch('/admin/api/cultivos-usuarios');
                if (response.ok) {
                    const data = await response.json();
                    mostrarCultivos(data.cultivos || data);
                } else {
                    document.getElementById('cultivos-tbody').innerHTML = '<tr><td colspan="9" class="text-center text-danger">Error al cargar cultivos</td></tr>';
                }
            } catch (error) {
                console.error('Error:', error);
                document.getElementById('cultivos-tbody').innerHTML = '<tr><td colspan="9" class="text-center text-danger">Error de conexión</td></tr>';
            }
        }

        async function cargarEstadisticasCultivos() {
            try {
                const response = await fetch('/admin/api/estadisticas');
                if (response.ok) {
                    const stats = await response.json();
                    document.getElementById('total-cultivos').textContent = stats.total_cultivos || 0;
                }
                
                // Cargar estadísticas detalladas
                const responseCultivos = await fetch('/admin/api/cultivos-usuarios');
                if (responseCultivos.ok) {
                    const data = await responseCultivos.json();
                    const cultivos = data.cultivos || data;
                    
                    const maduracion = cultivos.filter(c => c.fase_cultivo === 'maduración').length;
                    const cosecha = cultivos.filter(c => c.fase_cultivo === 'cosecha').length;
                    const usuariosUnicos = new Set(cultivos.map(c => c.id_usuarios)).size;
                    
                    document.getElementById('cultivos-maduracion').textContent = maduracion;
                    document.getElementById('cultivos-cosecha').textContent = cosecha;
                    document.getElementById('usuarios-con-cultivos').textContent = usuariosUnicos;
                }
            } catch (error) {
                console.error('Error al cargar estadísticas:', error);
            }
        }

        // Variables globales para filtrado
        let cultivosOriginales = [];
        let cultivosFiltrados = [];

        function mostrarCultivos(cultivos) {
            cultivosOriginales = cultivos;
            cultivosFiltrados = [...cultivos];

            const grid = document.getElementById('cultivos-grid');
            const mensajeSinCultivos = document.getElementById('mensaje-sin-cultivos');
            const contador = document.getElementById('contadorCultivos');

            if (!cultivos || cultivos.length === 0) {
                grid.innerHTML = `
                    <div class="col-12 text-center py-5">
                        <i class="fas fa-seedling fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No hay cultivos registrados</h5>
                        <p class="text-muted">Los usuarios aún no han registrado cultivos en el sistema.</p>
                    </div>
                `;
                contador.textContent = '0 cultivos';
                mensajeSinCultivos.style.display = 'block';
                return;
            }

            mensajeSinCultivos.style.display = 'none';
            contador.textContent = `${cultivos.length} cultivo${cultivos.length !== 1 ? 's' : ''}`;

            let html = '';

            cultivos.forEach(cultivo => {
                const faseBadge = cultivo.fase_cultivo === 'maduración' ? 'warning' : cultivo.fase_cultivo === 'cosecha' ? 'success' : 'info';
                const fecha = cultivo.fecha_registro ? new Date(cultivo.fecha_registro).toLocaleDateString('es-ES') : 'N/A';
                const fechaSiembra = cultivo.fecha_siembra ? new Date(cultivo.fecha_siembra).toLocaleDateString('es-ES') : 'No especificada';
                const area = (cultivo.ancho && cultivo.altura) ? (cultivo.ancho * cultivo.altura).toFixed(2) : 'N/A';
                const descripcionCorta = cultivo.descripcion ?
                    (cultivo.descripcion.length > 80 ? cultivo.descripcion.substring(0, 80) + '...' : cultivo.descripcion) :
                    'Sin descripción';

                html += `
                    <div class="col-lg-6 col-xl-4 mb-4">
                        <div class="card h-100 shadow-sm border-0 cultivo-card" data-cultivo-id="${cultivo.id_cultivo}">
                            <div class="card-header bg-gradient-${faseBadge === 'warning' ? 'warning' : faseBadge === 'success' ? 'success' : 'primary'} text-white">
                                <div class="row align-items-center">
                                    <div class="col">
                                        <h6 class="mb-0 fw-bold">
                                            <i class="fas fa-seedling me-2"></i>${cultivo.nombre || 'Sin nombre'}
                                        </h6>
                                    </div>
                                    <div class="col-auto">
                                        <span class="badge bg-light text-dark">#${cultivo.id_cultivo}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="row mb-3">
                                    <div class="col-12">
                                        <div class="d-flex align-items-center mb-2">
                                            <i class="fas fa-user text-primary me-2"></i>
                                            <strong>${cultivo.usuario_nombre || 'Usuario'} ${cultivo.usuario_apellido || ''}</strong>
                                        </div>
                                        <div class="d-flex align-items-center mb-2">
                                            <i class="fas fa-envelope text-muted me-2"></i>
                                            <small class="text-muted">${cultivo.usuario_email || 'Sin email'}</small>
                                        </div>
                                    </div>
                                </div>

                                <p class="text-muted small mb-3" title="${cultivo.descripcion || ''}">${descripcionCorta}</p>

                                <div class="row text-center mb-3">
                                    <div class="col-4">
                                        <div class="border-end">
                                            <div class="fw-bold text-success">${cultivo.cantidad || 'N/A'}</div>
                                            <small class="text-muted">${cultivo.unidad_cantidad || 'unidades'}</small>
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <div class="border-end">
                                            <div class="fw-bold text-info">${area} m²</div>
                                            <small class="text-muted">Área total</small>
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <div class="fw-bold text-warning">${cultivo.ancho || 'N/A'} × ${cultivo.altura || 'N/A'}</div>
                                        <small class="text-muted">Dimensiones</small>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <span class="badge bg-${faseBadge} fs-6 px-3 py-2">
                                        <i class="fas fa-leaf me-1"></i>${cultivo.fase_cultivo || 'N/A'}
                                    </span>
                                </div>

                                <div class="row small text-muted mb-3">
                                    <div class="col-6">
                                        <i class="fas fa-calendar-plus me-1"></i>
                                        <strong>Siembra:</strong><br>
                                        ${fechaSiembra}
                                    </div>
                                    <div class="col-6">
                                        <i class="fas fa-calendar-check me-1"></i>
                                        <strong>Registro:</strong><br>
                                        ${fecha}
                                    </div>
                                </div>

                                ${cultivo.ubicacion ? `
                                <div class="mb-3">
                                    <i class="fas fa-map-marker-alt text-danger me-2"></i>
                                    <small class="text-muted">${cultivo.ubicacion}</small>
                                </div>
                                ` : ''}
                            </div>
                            <div class="card-footer bg-light">
                                <div class="btn-group w-100" role="group">
                                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="verDetalleCultivo(${cultivo.id_cultivo})" title="Ver detalles completos">
                                        <i class="fas fa-eye me-1"></i>Ver
                                    </button>
                                    <button type="button" class="btn btn-outline-info btn-sm" onclick="contactarUsuario('${cultivo.usuario_email}')" title="Contactar usuario">
                                        <i class="fas fa-envelope me-1"></i>Contactar
                                    </button>
                                    <button type="button" class="btn btn-outline-danger btn-sm" onclick="eliminarCultivo(${cultivo.id_cultivo})" title="Eliminar cultivo">
                                        <i class="fas fa-trash me-1"></i>Eliminar
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });

            grid.innerHTML = html;
        }

        async function verDetalleCultivo(idCultivo) {
            try {
                const response = await fetch(`/admin/api/cultivos-usuarios/${idCultivo}`);
                if (response.ok) {
                    const cultivo = await response.json();
                    mostrarDetalleCultivo(cultivo);
                } else {
                    alert('Error al cargar el detalle del cultivo');
                }
            } catch (error) {
                alert('Error de conexión');
            }
        }

        function mostrarDetalleCultivo(cultivo) {
            const content = document.getElementById('detalle-cultivo-content');
            const fechaRegistro = cultivo.fecha_registro ? new Date(cultivo.fecha_registro).toLocaleDateString('es-ES', {
                year: 'numeric', month: 'long', day: 'numeric', hour: '2-digit', minute: '2-digit'
            }) : 'N/A';
            const fechaSiembra = cultivo.fecha_siembra ? new Date(cultivo.fecha_siembra).toLocaleDateString('es-ES', {
                year: 'numeric', month: 'long', day: 'numeric'
            }) : 'No especificada';
            const fechaCosecha = cultivo.fecha_estimada_cosecha ? new Date(cultivo.fecha_estimada_cosecha).toLocaleDateString('es-ES', {
                year: 'numeric', month: 'long', day: 'numeric'
            }) : 'No especificada';
            const area = (cultivo.ancho && cultivo.altura) ? (cultivo.ancho * cultivo.altura).toFixed(2) : 'N/A';
            const faseBadge = cultivo.fase_cultivo === 'maduración' ? 'warning' : cultivo.fase_cultivo === 'cosecha' ? 'success' : 'info';

            content.innerHTML = `
                <div class="row">
                    <!-- Información del Cultivo -->
                    <div class="col-lg-8">
                        <div class="card border-0 shadow-sm mb-4">
                            <div class="card-header bg-gradient-success text-white">
                                <h6 class="mb-0"><i class="fas fa-seedling me-2"></i>Información del Cultivo</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label fw-bold text-muted">ID del Cultivo</label>
                                            <div class="p-2 bg-light rounded">#${cultivo.id_cultivo}</div>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label fw-bold text-muted">Nombre</label>
                                            <div class="p-2 bg-light rounded">${cultivo.nombre || 'Sin nombre'}</div>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label fw-bold text-muted">Fase del Cultivo</label>
                                            <div class="p-2 bg-light rounded">
                                                <span class="badge bg-${faseBadge} fs-6 px-3 py-2">
                                                    <i class="fas fa-leaf me-1"></i>${cultivo.fase_cultivo || 'N/A'}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label fw-bold text-muted">Cantidad</label>
                                            <div class="p-2 bg-light rounded">${cultivo.cantidad || 'N/A'} ${cultivo.unidad_cantidad || 'unidades'}</div>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label fw-bold text-muted">Dimensiones</label>
                                            <div class="p-2 bg-light rounded">${cultivo.ancho || 'N/A'} × ${cultivo.altura || 'N/A'} metros</div>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label fw-bold text-muted">Área Total</label>
                                            <div class="p-2 bg-light rounded">${area} m²</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label fw-bold text-muted">Descripción</label>
                                    <div class="p-3 bg-light rounded">${cultivo.descripcion || 'Sin descripción'}</div>
                                </div>
                                ${cultivo.ubicacion ? `
                                <div class="mb-3">
                                    <label class="form-label fw-bold text-muted">Ubicación</label>
                                    <div class="p-2 bg-light rounded">
                                        <i class="fas fa-map-marker-alt text-danger me-2"></i>${cultivo.ubicacion}
                                    </div>
                                </div>
                                ` : ''}
                                ${cultivo.notas ? `
                                <div class="mb-3">
                                    <label class="form-label fw-bold text-muted">Notas Adicionales</label>
                                    <div class="p-3 bg-light rounded">${cultivo.notas}</div>
                                </div>
                                ` : ''}
                            </div>
                        </div>
                    </div>

                    <!-- Información del Usuario y Fechas -->
                    <div class="col-lg-4">
                        <div class="card border-0 shadow-sm mb-4">
                            <div class="card-header bg-gradient-primary text-white">
                                <h6 class="mb-0"><i class="fas fa-user me-2"></i>Propietario</h6>
                            </div>
                            <div class="card-body">
                                <div class="text-center mb-3">
                                    <div class="bg-primary rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 60px; height: 60px;">
                                        <i class="fas fa-user fa-2x text-white"></i>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label fw-bold text-muted">Nombre Completo</label>
                                    <div class="p-2 bg-light rounded">${cultivo.usuario_nombre || 'Usuario'} ${cultivo.usuario_apellido || ''}</div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label fw-bold text-muted">Email</label>
                                    <div class="p-2 bg-light rounded">
                                        ${cultivo.usuario_email ? `
                                            <a href="mailto:${cultivo.usuario_email}" class="text-decoration-none">
                                                <i class="fas fa-envelope me-2"></i>${cultivo.usuario_email}
                                            </a>
                                        ` : 'No especificado'}
                                    </div>
                                </div>
                                <div class="d-grid">
                                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="contactarUsuario('${cultivo.usuario_email}')">
                                        <i class="fas fa-envelope me-2"></i>Contactar Usuario
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="card border-0 shadow-sm">
                            <div class="card-header bg-gradient-info text-white">
                                <h6 class="mb-0"><i class="fas fa-calendar me-2"></i>Fechas Importantes</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label fw-bold text-muted">Fecha de Siembra</label>
                                    <div class="p-2 bg-light rounded">
                                        <i class="fas fa-calendar-plus text-success me-2"></i>${fechaSiembra}
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label fw-bold text-muted">Cosecha Estimada</label>
                                    <div class="p-2 bg-light rounded">
                                        <i class="fas fa-calendar-check text-warning me-2"></i>${fechaCosecha}
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label fw-bold text-muted">Fecha de Registro</label>
                                    <div class="p-2 bg-light rounded">
                                        <i class="fas fa-calendar text-info me-2"></i>${fechaRegistro}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            new bootstrap.Modal(document.getElementById('modalDetalleCultivo')).show();
        }

        // Función para filtrar cultivos
        function filtrarCultivos() {
            const busqueda = document.getElementById('buscarCultivo').value.toLowerCase();
            const filtroFase = document.getElementById('filtroFase').value;

            cultivosFiltrados = cultivosOriginales.filter(cultivo => {
                const coincideBusqueda = !busqueda ||
                    (cultivo.nombre && cultivo.nombre.toLowerCase().includes(busqueda)) ||
                    (cultivo.descripcion && cultivo.descripcion.toLowerCase().includes(busqueda)) ||
                    (cultivo.usuario_nombre && cultivo.usuario_nombre.toLowerCase().includes(busqueda)) ||
                    (cultivo.usuario_apellido && cultivo.usuario_apellido.toLowerCase().includes(busqueda)) ||
                    (cultivo.usuario_email && cultivo.usuario_email.toLowerCase().includes(busqueda));

                const coincideFase = !filtroFase || cultivo.fase_cultivo === filtroFase;

                return coincideBusqueda && coincideFase;
            });

            mostrarCultivos(cultivosFiltrados);
        }

        // Función para ordenar cultivos
        function ordenarCultivos() {
            const orden = document.getElementById('filtroOrden').value;

            cultivosFiltrados.sort((a, b) => {
                switch(orden) {
                    case 'fecha_desc':
                        return new Date(b.fecha_registro) - new Date(a.fecha_registro);
                    case 'fecha_asc':
                        return new Date(a.fecha_registro) - new Date(b.fecha_registro);
                    case 'nombre_asc':
                        return (a.nombre || '').localeCompare(b.nombre || '');
                    case 'usuario_asc':
                        return (a.usuario_nombre || '').localeCompare(b.usuario_nombre || '');
                    default:
                        return 0;
                }
            });

            mostrarCultivos(cultivosFiltrados);
        }

        // Función para exportar cultivos
        function exportarCultivos() {
            if (cultivosFiltrados.length === 0) {
                alert('No hay cultivos para exportar');
                return;
            }

            const csv = [
                ['ID', 'Usuario', 'Email', 'Nombre Cultivo', 'Descripción', 'Cantidad', 'Unidad', 'Ancho', 'Altura', 'Área', 'Fase', 'Ubicación', 'Fecha Siembra', 'Fecha Registro'].join(','),
                ...cultivosFiltrados.map(c => [
                    c.id_cultivo,
                    `"${c.usuario_nombre || ''} ${c.usuario_apellido || ''}"`,
                    c.usuario_email || '',
                    `"${c.nombre || ''}"`,
                    `"${(c.descripcion || '').replace(/"/g, '""')}"`,
                    c.cantidad || '',
                    c.unidad_cantidad || '',
                    c.ancho || '',
                    c.altura || '',
                    (c.ancho && c.altura) ? (c.ancho * c.altura).toFixed(2) : '',
                    c.fase_cultivo || '',
                    `"${c.ubicacion || ''}"`,
                    c.fecha_siembra ? new Date(c.fecha_siembra).toLocaleDateString('es-ES') : '',
                    c.fecha_registro ? new Date(c.fecha_registro).toLocaleDateString('es-ES') : ''
                ].join(','))
            ].join('\n');

            const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = `cultivos_${new Date().toISOString().split('T')[0]}.csv`;
            link.click();
        }

        // Función para contactar usuario
        function contactarUsuario(email) {
            if (email && email !== 'Sin email') {
                window.location.href = `mailto:${email}?subject=Consulta sobre su cultivo - MaizGuard`;
            } else {
                alert('Este usuario no tiene email registrado');
            }
        }

        async function eliminarCultivo(idCultivo) {
            if (confirm('¿Estás seguro de que quieres eliminar este cultivo?\n\nEsta acción no se puede deshacer.')) {
                try {
                    const response = await fetch(`/admin/api/cultivos-usuarios/${idCultivo}`, {
                        method: 'DELETE'
                    });

                    if (response.ok) {
                        // Mostrar mensaje de éxito
                        const alertDiv = document.createElement('div');
                        alertDiv.className = 'alert alert-success alert-dismissible fade show position-fixed';
                        alertDiv.style.top = '100px';
                        alertDiv.style.right = '20px';
                        alertDiv.style.zIndex = '9999';
                        alertDiv.innerHTML = `
                            <i class="fas fa-check-circle me-2"></i>Cultivo eliminado exitosamente
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        `;
                        document.body.appendChild(alertDiv);

                        // Recargar datos
                        cargarCultivos();
                        cargarEstadisticasCultivos();

                        // Remover alerta después de 3 segundos
                        setTimeout(() => {
                            if (alertDiv.parentNode) {
                                alertDiv.parentNode.removeChild(alertDiv);
                            }
                        }, 3000);
                    } else {
                        const error = await response.json();
                        alert('Error: ' + (error.detail || 'Error al eliminar el cultivo'));
                    }
                } catch (error) {
                    alert('Error de conexión');
                }
            }
        }

        async function reiniciarIdsCultivos() {
            if (confirm('¿Estás seguro de que quieres reiniciar los IDs de cultivos? Esta acción reiniciará el contador AUTO_INCREMENT a 1.')) {
                try {
                    const response = await fetch('/admin/api/reiniciar-cultivos-ids', {
                        method: 'POST'
                    });

                    if (response.ok) {
                        const result = await response.json();
                        alert('IDs de cultivos reiniciados exitosamente');
                        cargarCultivos(); // Recargar la lista
                    } else {
                        const error = await response.json();
                        alert('Error: ' + (error.detail || 'Error al reiniciar IDs'));
                    }
                } catch (error) {
                    alert('Error de conexión');
                    console.error('Error:', error);
                }
            }
        }

        // Event listeners para búsqueda en tiempo real
        document.addEventListener('DOMContentLoaded', function() {
            const buscarInput = document.getElementById('buscarCultivo');
            if (buscarInput) {
                buscarInput.addEventListener('input', function() {
                    clearTimeout(this.searchTimeout);
                    this.searchTimeout = setTimeout(filtrarCultivos, 300);
                });
            }
        });
    </script>
</body>
</html>
