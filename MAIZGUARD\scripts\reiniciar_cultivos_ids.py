#!/usr/bin/env python3
"""
Script para reiniciar los IDs de la tabla cultivos
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.database.config import engine
from sqlalchemy import text

def reiniciar_ids_cultivos():
    """Reinicia el AUTO_INCREMENT de la tabla cultivos a 1"""
    try:
        with engine.connect() as conn:
            print("🔄 Reiniciando IDs de la tabla cultivos...")
            
            # Reiniciar AUTO_INCREMENT
            conn.execute(text("ALTER TABLE cultivos AUTO_INCREMENT = 1"))
            conn.commit()
            
            print("✅ IDs de cultivos reiniciados exitosamente")
            print("📋 El próximo cultivo que se registre tendrá ID = 1")
            
    except Exception as e:
        print(f"❌ Error al reiniciar IDs de cultivos: {e}")

if __name__ == "__main__":
    reiniciar_ids_cultivos()
