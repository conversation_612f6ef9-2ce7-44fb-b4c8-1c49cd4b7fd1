-- Eliminar la base de datos si existe
DROP DATABASE IF EXISTS maizguard;

-- Crear la base de datos
CREATE DATABASE maizguard;

-- Usar la base de datos
USE maizguard;

-- Tabla de estados (activo/inactivo)
CREATE TABLE IF NOT EXISTS estados (
    id_estados INT AUTO_INCREMENT PRIMARY KEY,
    descripcion VARCHAR(50) NOT NULL
);

-- Insertar estados básicos si no existen
INSERT INTO estados (id_estados, descripcion) VALUES
(1, 'Activo'),
(2, 'Inactivo')
ON DUPLICATE KEY UPDATE descripcion = VALUES(descripcion);

-- Tabla de roles
CREATE TABLE IF NOT EXISTS roles (
    id_rol INT AUTO_INCREMENT PRIMARY KEY,
    nombre VARCHAR(50) NOT NULL,
    descripcion VARCHAR(255)
);

-- Insertar roles básicos si no existen
INSERT INTO roles (id_rol, nombre, descripcion) VALUES
(1, 'Administrador', 'Control total del sistema'),
(2, 'Usuario', 'Usuario regular del sistema')
ON DUPLICATE KEY UPDATE nombre = VALUES(nombre), descripcion = VALUES(descripcion);

-- Tabla de usuarios
CREATE TABLE IF NOT EXISTS usuarios (
    id_usuarios INT AUTO_INCREMENT PRIMARY KEY,
    id_estados INT NOT NULL,
    id_rol INT NOT NULL,
    nombre VARCHAR(40) NOT NULL,
    apellido VARCHAR(40) NOT NULL,
    contrasena VARCHAR(255) NOT NULL,
    correo_electronico VARCHAR(100) NOT NULL UNIQUE,
    fotografia VARCHAR(255),
    fecha_registro DATETIME DEFAULT CURRENT_TIMESTAMP,
    token_recuperacion VARCHAR(250),
    fecha_expiracion_token DATETIME,
    FOREIGN KEY (id_estados) REFERENCES estados(id_estados),
    FOREIGN KEY (id_rol) REFERENCES roles(id_rol)
);

-- Tabla de fases de cultivo
CREATE TABLE IF NOT EXISTS fases_cultivo (
    id_fase_cultivo INT AUTO_INCREMENT PRIMARY KEY,
    descripcion VARCHAR(100) NOT NULL
);

-- Insertar fases de cultivo básicas si no existen
INSERT INTO fases_cultivo (id_fase_cultivo, descripcion) VALUES
(1, 'Germinación'),
(2, 'Crecimiento vegetativo'),
(3, 'Floración'),
(4, 'Llenado de grano'),
(5, 'Maduración')
ON DUPLICATE KEY UPDATE descripcion = VALUES(descripcion);

-- Tabla de tipos de plagas
CREATE TABLE IF NOT EXISTS tipos_plaga (
    id_tipo_plaga INT AUTO_INCREMENT PRIMARY KEY,
    nombre VARCHAR(50) NOT NULL,
    descripcion VARCHAR(255)
);

-- Insertar tipos de plagas básicos si no existen
INSERT INTO tipos_plaga (id_tipo_plaga, nombre, descripcion) VALUES
(1, 'Insecto', 'Plagas de tipo insecto'),
(2, 'Hongo', 'Enfermedades fúngicas'),
(3, 'Bacteria', 'Enfermedades bacterianas'),
(4, 'Virus', 'Enfermedades virales'),
(5, 'Maleza', 'Plantas no deseadas')
ON DUPLICATE KEY UPDATE nombre = VALUES(nombre), descripcion = VALUES(descripcion);

-- Tabla de temporadas
CREATE TABLE IF NOT EXISTS temporadas (
    id_temporada INT AUTO_INCREMENT PRIMARY KEY,
    nombre VARCHAR(50) NOT NULL,
    descripcion VARCHAR(255)
);

-- Insertar temporadas básicas si no existen
INSERT INTO temporadas (id_temporada, nombre, descripcion) VALUES
(1, 'Primavera', 'Marzo a Junio'),
(2, 'Verano', 'Junio a Septiembre'),
(3, 'Otoño', 'Septiembre a Diciembre'),
(4, 'Invierno', 'Diciembre a Marzo'),
(5, 'Todo el año', 'Presente durante todo el año')
ON DUPLICATE KEY UPDATE nombre = VALUES(nombre), descripcion = VALUES(descripcion);

-- Tabla de regiones
CREATE TABLE IF NOT EXISTS regiones (
    id_region INT AUTO_INCREMENT PRIMARY KEY,
    nombre VARCHAR(50) NOT NULL,
    descripcion VARCHAR(255)
);

-- Insertar regiones básicas si no existen
INSERT INTO regiones (id_region, nombre, descripcion) VALUES
(1, 'Norte', 'Región norte del país'),
(2, 'Centro', 'Región central del país'),
(3, 'Sur', 'Región sur del país'),
(4, 'Este', 'Región este del país'),
(5, 'Oeste', 'Región oeste del país')
ON DUPLICATE KEY UPDATE nombre = VALUES(nombre), descripcion = VALUES(descripcion);

-- Tabla de plagas
CREATE TABLE IF NOT EXISTS plagas (
    id_plaga INT AUTO_INCREMENT PRIMARY KEY,
    nombre VARCHAR(100) NOT NULL,
    nombre_cientifico VARCHAR(100),
    url_img VARCHAR(255),
    descripcion TEXT NOT NULL,
    recomendaciones TEXT,
    id_fase_cultivo INT NOT NULL,
    id_tipo_plaga INT NOT NULL,
    id_temporada INT NOT NULL,
    id_usuarios INT NOT NULL,
    fecha_registro DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (id_fase_cultivo) REFERENCES fases_cultivo(id_fase_cultivo),
    FOREIGN KEY (id_tipo_plaga) REFERENCES tipos_plaga(id_tipo_plaga),
    FOREIGN KEY (id_temporada) REFERENCES temporadas(id_temporada),
    FOREIGN KEY (id_usuarios) REFERENCES usuarios(id_usuarios)
);

-- Tabla de cultivos
CREATE TABLE IF NOT EXISTS cultivos (
    id_cultivo INT AUTO_INCREMENT PRIMARY KEY,
    nombre VARCHAR(100) NOT NULL,
    variedad VARCHAR(100),
    url_img VARCHAR(255),
    descripcion TEXT NOT NULL,
    cuidados TEXT,
    id_region INT NOT NULL,
    id_temporada INT NOT NULL,
    id_usuarios INT NOT NULL,
    fecha_registro DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (id_region) REFERENCES regiones(id_region),
    FOREIGN KEY (id_temporada) REFERENCES temporadas(id_temporada),
    FOREIGN KEY (id_usuarios) REFERENCES usuarios(id_usuarios)
);

-- Comentado: No crear un usuario administrador por defecto
/*
INSERT INTO usuarios (id_usuarios, id_estados, id_rol, nombre, apellido, contrasena, correo_electronico)
VALUES (1, 1, 1, 'Admin', 'Sistema', '$2b$12$K3JNi5xUQEJcptXo4zT9UOKsWsULQXQMyl8QzQtGzjKJCzSGqJQ0e', '<EMAIL>')
ON DUPLICATE KEY UPDATE id_estados = VALUES(id_estados), id_rol = VALUES(id_rol), nombre = VALUES(nombre),
apellido = VALUES(apellido), contrasena = VALUES(contrasena), correo_electronico = VALUES(correo_electronico);
*/
