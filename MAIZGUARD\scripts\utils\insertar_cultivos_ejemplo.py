#!/usr/bin/env python3
"""
Script para insertar cultivos de ejemplo en la base de datos
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from app.database.config import engine
from sqlalchemy import text

def insertar_cultivos_ejemplo():
    """Insertar cultivos de ejemplo en la base de datos"""
    try:
        with engine.connect() as conn:
            # Obtener el ID de un usuario regular
            result = conn.execute(text("SELECT id_usuarios FROM usuarios WHERE id_rol = 2 LIMIT 1"))
            user = result.fetchone()
            
            if not user:
                print("⚠️  No se encontró ningún usuario regular para asignar los cultivos.")
                return
            
            user_id = user.id_usuarios
            print(f"🧑‍🌾 Usando usuario ID: {user_id} para los cultivos de ejemplo")
            
            # Cultivos de ejemplo
            cultivos = [
                {
                    "nombre": "<PERSON><PERSON><PERSON>",
                    "variedad": "<PERSON>",
                    "url_img": "/static/images/maiz-criollo.jpg",
                    "descripcion": "Variedad tradicional de maíz adaptada a condiciones locales, con excelente resistencia a plagas y enfermedades. Este maíz ha sido cultivado por generaciones en la región y está perfectamente adaptado al clima y suelo locales.",
                    "cuidados": "Requiere riego moderado y suelos bien drenados. Se recomienda fertilización orgánica y control manual de malezas. Sembrar a una distancia de 80 cm entre surcos y 40 cm entre plantas. Aplicar abono orgánico antes de la siembra.",
                    "id_region": 2,
                    "id_temporada": 2
                },
                {
                    "nombre": "Maíz Híbrido",
                    "variedad": "Amarillo",
                    "url_img": "/static/images/maiz-hibrido.jpg",
                    "descripcion": "Variedad mejorada con alto rendimiento y resistencia a condiciones adversas. Este maíz híbrido ha sido desarrollado para maximizar la producción y ofrecer resistencia a enfermedades comunes en la región.",
                    "cuidados": "Necesita fertilización balanceada y control preventivo de plagas. Riego regular especialmente en etapas críticas. Aplicar fertilizante NPK 15-15-15 al momento de la siembra y urea durante el desarrollo vegetativo.",
                    "id_region": 1,
                    "id_temporada": 2
                },
                {
                    "nombre": "Maíz Dulce",
                    "variedad": "Golden Bantam",
                    "url_img": "/static/images/maiz-dulce.jpg",
                    "descripcion": "Variedad de maíz con alto contenido de azúcares, ideal para consumo fresco. Este maíz es perfecto para consumo humano directo, ya sea cocido o asado. Su sabor dulce lo hace muy apreciado en el mercado.",
                    "cuidados": "Requiere suelos ricos en materia orgánica y riego constante. Sensible a heladas y sequías. Mantener el suelo húmedo durante la formación de mazorcas. Cosechar cuando los granos estén en estado lechoso.",
                    "id_region": 3,
                    "id_temporada": 1
                }
            ]
            
            # Verificar si ya existen cultivos
            result = conn.execute(text("SELECT COUNT(*) as count FROM cultivos"))
            count = result.fetchone().count
            
            if count > 0:
                print(f"⚠️  Ya existen {count} cultivos en la base de datos.")
                
                # Preguntar si desea continuar
                respuesta = input("¿Desea insertar los cultivos de ejemplo de todos modos? (s/n): ")
                if respuesta.lower() != 's':
                    print("❌ Operación cancelada.")
                    return
            
            # Insertar cultivos
            for cultivo in cultivos:
                # Verificar si ya existe un cultivo con el mismo nombre y variedad
                result = conn.execute(text("""
                    SELECT COUNT(*) as count FROM cultivos 
                    WHERE nombre = :nombre AND variedad = :variedad
                """), {
                    "nombre": cultivo["nombre"],
                    "variedad": cultivo["variedad"]
                })
                
                if result.fetchone().count > 0:
                    print(f"⚠️  El cultivo '{cultivo['nombre']} - {cultivo['variedad']}' ya existe. Omitiendo...")
                    continue
                
                # Insertar cultivo
                conn.execute(text("""
                    INSERT INTO cultivos (
                        nombre, variedad, url_img, descripcion, cuidados,
                        id_region, id_temporada, id_usuarios
                    ) VALUES (
                        :nombre, :variedad, :url_img, :descripcion, :cuidados,
                        :id_region, :id_temporada, :id_usuarios
                    )
                """), {
                    **cultivo,
                    "id_usuarios": user_id
                })
                
                print(f"✅ Cultivo '{cultivo['nombre']} - {cultivo['variedad']}' insertado.")
            
            conn.commit()
            
            # Verificar cultivos insertados
            result = conn.execute(text("""
                SELECT c.id_cultivo, c.nombre, c.variedad, r.nombre as region, t.nombre as temporada
                FROM cultivos c
                JOIN regiones r ON c.id_region = r.id_region
                JOIN temporadas t ON c.id_temporada = t.id_temporada
                ORDER BY c.id_cultivo
            """))
            
            cultivos_insertados = result.fetchall()
            
            print("\n📋 Cultivos en la base de datos:")
            for cultivo in cultivos_insertados:
                print(f"  🌽 ID: {cultivo.id_cultivo}, Nombre: {cultivo.nombre}, Variedad: {cultivo.variedad}, Región: {cultivo.region}, Temporada: {cultivo.temporada}")
            
            print(f"\n🎉 Total de cultivos: {len(cultivos_insertados)}")
            
    except Exception as e:
        print(f"❌ Error al insertar cultivos de ejemplo: {e}")

if __name__ == "__main__":
    print("🌽 Insertando cultivos de ejemplo en MaizGuard...")
    insertar_cultivos_ejemplo()
