from fastapi import APIRouter, Request, Depends, HTTPException, status, <PERSON>ie
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse, RedirectResponse
from sqlalchemy.orm import Session
from typing import Optional

from app.database.config import get_db
from app.database.models import User

router = APIRouter(
    prefix="/admin",
    tags=["admin"],
    responses={404: {"description": "Not found"}},
)

templates = Jinja2Templates(directory="app/templates")

# Función para verificar si el usuario es administrador
async def verify_admin(
    user_id: Optional[str] = <PERSON><PERSON>(None),
    user_role: Optional[str] = <PERSON><PERSON>(None),
    db: Session = Depends(get_db)
):
    """Verificar que el usuario esté autenticado y sea administrador"""

    # Verificar si el usuario está autenticado
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Debes iniciar sesión para acceder al panel de administración"
        )

    try:
        # Verificar que el usuario existe y obtener su rol actual de la base de datos
        user = db.query(User).filter(User.id_usuarios == int(user_id)).first()

        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Usuario no encontrado"
            )

        # Verificar que el usuario tiene rol de administrador (id_rol = 1)
        # Siempre verificar desde la base de datos, no desde las cookies
        if user.id_rol != 1:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="No tienes permisos para acceder al panel de administración"
            )

        return user

    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="ID de usuario inválido"
        )
    except HTTPException:
        # Re-lanzar HTTPExceptions sin modificar
        raise
    except Exception as e:
        print(f"Error en verify_admin: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error al verificar permisos de administrador: {str(e)}"
        )

# Ruta principal del panel de administración
@router.get("/", response_class=HTMLResponse)
async def admin_dashboard(
    request: Request,
    admin_user: User = Depends(verify_admin)
):
    """Panel principal de administración - Solo para administradores"""
    # Agregar el usuario al estado de la request para el navbar
    request.state.user = admin_user

    return templates.TemplateResponse("admin/dashboard.html", {
        "request": request,
        "user": admin_user,
        "user_id": str(admin_user.id_usuarios),
        "user_role": str(admin_user.id_rol)
    })

# Ruta para gestión de usuarios
@router.get("/usuarios", response_class=HTMLResponse)
async def admin_usuarios(
    request: Request,
    admin_user: User = Depends(verify_admin)
):
    """Gestión de usuarios - Solo para administradores"""
    # Agregar el usuario al estado de la request para el navbar
    request.state.user = admin_user

    return templates.TemplateResponse("admin/usuarios.html", {
        "request": request,
        "user": admin_user,
        "user_id": request.cookies.get("user_id"),
        "user_role": request.cookies.get("user_role")
    })

# Ruta para gestión de plagas
@router.get("/plagas", response_class=HTMLResponse)
async def admin_plagas(
    request: Request,
    admin_user: User = Depends(verify_admin)
):
    """Gestión de plagas - Solo para administradores"""
    # Agregar el usuario al estado de la request para el navbar
    request.state.user = admin_user

    return templates.TemplateResponse("admin/plagas.html", {
        "request": request,
        "user": admin_user,
        "user_id": request.cookies.get("user_id"),
        "user_role": request.cookies.get("user_role")
    })

# Ruta para gestión de cultivos
@router.get("/cultivos", response_class=HTMLResponse)
async def admin_cultivos(
    request: Request,
    admin_user: User = Depends(verify_admin)
):
    """Gestión de cultivos - Solo para administradores"""
    return templates.TemplateResponse("admin/cultivos.html", {
        "request": request,
        "user": admin_user
    })

# Ruta para configuración
@router.get("/configuracion", response_class=HTMLResponse)
async def admin_configuracion(
    request: Request,
    admin_user: User = Depends(verify_admin)
):
    """Configuración del sistema - Solo para administradores"""
    return templates.TemplateResponse("admin/configuracion.html", {
        "request": request,
        "user": admin_user
    })

# API para obtener estadísticas del dashboard
@router.get("/api/estadisticas")
async def get_estadisticas(
    admin_user: User = Depends(verify_admin),
    db: Session = Depends(get_db)
):
    try:
        from sqlalchemy import text

        # Obtener estadísticas reales de la base de datos

        # Usuarios registrados
        result = db.execute(text("SELECT COUNT(*) as total FROM usuarios"))
        total_usuarios = result.fetchone().total

        # Usuarios activos
        result = db.execute(text("SELECT COUNT(*) as total FROM usuarios WHERE id_estados = 1"))
        usuarios_activos = result.fetchone().total

        # Plagas registradas
        result = db.execute(text("SELECT COUNT(*) as total FROM plagas"))
        total_plagas = result.fetchone().total

        # Cultivos registrados (sin filtro de activo)
        result = db.execute(text("SELECT COUNT(*) as total FROM cultivos"))
        total_cultivos = result.fetchone().total

        return {
            "total_usuarios": total_usuarios,
            "usuarios_activos": usuarios_activos,
            "total_plagas": total_plagas,
            "total_cultivos": total_cultivos
        }
    except Exception as e:
        print(f"Error al obtener estadísticas: {e}")
        # En caso de error, devolver valores por defecto
        return {
            "total_usuarios": 0,
            "usuarios_activos": 0,
            "total_plagas": 0,
            "total_cultivos": 0
        }

# API para obtener usuarios recientes
@router.get("/api/usuarios-recientes")
async def get_usuarios_recientes(admin_user: User = Depends(verify_admin), db: Session = Depends(get_db)):
    try:
        from sqlalchemy import text

        # Obtener los 5 usuarios más recientes de la base de datos
        query = text("""
            SELECT
                u.id_usuarios, u.nombre, u.apellido, u.correo_electronico,
                u.fecha_registro, u.id_estados
            FROM usuarios u
            ORDER BY u.fecha_registro DESC
            LIMIT 5
        """)

        result = db.execute(query)
        usuarios_recientes = []

        for row in result:
            usuario = dict(row._mapping)

            # Formatear fecha
            if usuario['fecha_registro']:
                fecha_obj = usuario['fecha_registro']
                usuario['fecha_registro'] = fecha_obj.strftime("%d/%m/%Y")
            else:
                usuario['fecha_registro'] = 'N/A'

            # Formatear estado
            usuario['estado'] = 'Activo' if usuario['id_estados'] == 1 else 'Inactivo'

            usuarios_recientes.append(usuario)

        return usuarios_recientes

    except Exception as e:
        print(f"Error al obtener usuarios recientes: {e}")
        # En caso de error, devolver una lista vacía
        return []

# API para obtener actividades recientes
@router.get("/api/actividades-recientes")
async def get_actividades_recientes(admin_user: User = Depends(verify_admin), db: Session = Depends(get_db)):
    try:
        from sqlalchemy import text
        from datetime import datetime, timedelta

        # Generar actividades basadas en datos reales de la base de datos
        actividades = []

        # Obtener usuarios recientes
        query = text("""
            SELECT nombre, apellido, fecha_registro
            FROM usuarios
            ORDER BY fecha_registro DESC
            LIMIT 3
        """)
        result = db.execute(query)
        usuarios_recientes = result.fetchall()

        for usuario in usuarios_recientes:
            if usuario.fecha_registro:
                fecha_str = usuario.fecha_registro.strftime("%d/%m/%Y %H:%M")
                actividades.append({
                    "id": len(actividades) + 1,
                    "titulo": "Usuario registrado",
                    "descripcion": f"Se registró el usuario: {usuario.nombre} {usuario.apellido}",
                    "fecha": fecha_str,
                    "icono": "fas fa-user-plus text-success"
                })

        # Obtener cultivos recientes (sin filtro de activo)
        query = text("""
            SELECT c.nombre, c.fecha_registro, u.nombre as usuario_nombre, u.apellido as usuario_apellido
            FROM cultivos c
            LEFT JOIN usuarios u ON c.id_usuarios = u.id_usuarios
            ORDER BY c.fecha_registro DESC
            LIMIT 2
        """)
        result = db.execute(query)
        cultivos_recientes = result.fetchall()

        for cultivo in cultivos_recientes:
            if cultivo.fecha_registro:
                fecha_str = cultivo.fecha_registro.strftime("%d/%m/%Y %H:%M")
                usuario = f"{cultivo.usuario_nombre} {cultivo.usuario_apellido}" if cultivo.usuario_nombre else "Usuario"
                actividades.append({
                    "id": len(actividades) + 1,
                    "titulo": "Cultivo registrado",
                    "descripcion": f"Se registró el cultivo: {cultivo.nombre} por {usuario}",
                    "fecha": fecha_str,
                    "icono": "fas fa-seedling text-success"
                })

        # Ordenar por fecha más reciente
        actividades.sort(key=lambda x: datetime.strptime(x['fecha'], "%d/%m/%Y %H:%M"), reverse=True)

        # Retornar solo las 5 más recientes
        return actividades[:5]

    except Exception as e:
        print(f"Error al obtener actividades recientes: {e}")
        # En caso de error, devolver una lista vacía
        return []

# API para obtener cultivos de usuarios
@router.get("/api/cultivos-usuarios")
async def get_cultivos_usuarios(admin_user: User = Depends(verify_admin), db: Session = Depends(get_db)):
    try:
        from sqlalchemy import text

        # Consulta para obtener cultivos con información del usuario
        query = text("""
            SELECT
                c.id_cultivo, c.nombre, c.descripcion, c.cantidad, c.unidad_cantidad,
                c.ancho, c.altura, c.fase_cultivo, c.fecha_siembra, c.fecha_estimada_cosecha,
                c.ubicacion, c.notas, c.fecha_registro, c.fecha_actualizacion, c.activo,
                c.id_usuarios,
                u.nombre as usuario_nombre, u.apellido as usuario_apellido,
                u.correo_electronico as usuario_email
            FROM cultivos c
            LEFT JOIN usuarios u ON c.id_usuarios = u.id_usuarios
            WHERE c.activo = 1
            ORDER BY c.fecha_registro DESC
        """)

        result = db.execute(query)
        cultivos = []

        for row in result:
            cultivo = dict(row._mapping)

            # Procesar fechas
            if cultivo['fecha_registro']:
                cultivo['fecha_registro'] = cultivo['fecha_registro'].isoformat()
            if cultivo['fecha_siembra']:
                cultivo['fecha_siembra'] = cultivo['fecha_siembra'].isoformat()
            if cultivo['fecha_estimada_cosecha']:
                cultivo['fecha_estimada_cosecha'] = cultivo['fecha_estimada_cosecha'].isoformat()
            if cultivo['fecha_actualizacion']:
                cultivo['fecha_actualizacion'] = cultivo['fecha_actualizacion'].isoformat()

            cultivos.append(cultivo)

        return {"cultivos": cultivos}

    except Exception as e:
        print(f"Error al obtener cultivos de usuarios: {e}")
        return {"cultivos": []}

# API para obtener un cultivo específico de usuario
@router.get("/api/cultivos-usuarios/{id_cultivo}")
async def get_cultivo_usuario(
    id_cultivo: int,
    admin_user: User = Depends(verify_admin),
    db: Session = Depends(get_db)
):
    try:
        from sqlalchemy import text

        query = text("""
            SELECT
                c.id_cultivo, c.nombre, c.descripcion, c.cantidad, c.unidad_cantidad,
                c.ancho, c.altura, c.fase_cultivo, c.fecha_siembra, c.fecha_estimada_cosecha,
                c.ubicacion, c.notas, c.fecha_registro, c.fecha_actualizacion, c.activo,
                c.id_usuarios,
                u.nombre as usuario_nombre, u.apellido as usuario_apellido,
                u.correo_electronico as usuario_email
            FROM cultivos c
            LEFT JOIN usuarios u ON c.id_usuarios = u.id_usuarios
            WHERE c.id_cultivo = :id_cultivo AND c.activo = 1
        """)

        result = db.execute(query, {"id_cultivo": id_cultivo})
        row = result.fetchone()

        if not row:
            raise HTTPException(status_code=404, detail="Cultivo no encontrado")

        cultivo = dict(row._mapping)

        # Procesar fechas
        if cultivo['fecha_registro']:
            cultivo['fecha_registro'] = cultivo['fecha_registro'].isoformat()
        if cultivo['fecha_siembra']:
            cultivo['fecha_siembra'] = cultivo['fecha_siembra'].isoformat()
        if cultivo['fecha_estimada_cosecha']:
            cultivo['fecha_estimada_cosecha'] = cultivo['fecha_estimada_cosecha'].isoformat()
        if cultivo['fecha_actualizacion']:
            cultivo['fecha_actualizacion'] = cultivo['fecha_actualizacion'].isoformat()

        return cultivo

    except HTTPException:
        raise
    except Exception as e:
        print(f"Error al obtener cultivo: {e}")
        raise HTTPException(status_code=500, detail="Error interno del servidor")

# Ruta para la página de cultivos de usuarios
@router.get("/cultivos-usuarios", response_class=HTMLResponse)
async def admin_cultivos_usuarios(
    request: Request,
    admin_user: User = Depends(verify_admin)
):
    """Gestión de cultivos de usuarios - Solo para administradores"""
    # Agregar el usuario al estado de la request para el navbar
    request.state.user = admin_user

    return templates.TemplateResponse("admin/cultivos-usuarios.html", {
        "request": request,
        "user": admin_user,
        "user_id": request.cookies.get("user_id"),
        "user_role": request.cookies.get("user_role")
    })

# API para obtener todos los usuarios
@router.get("/api/usuarios")
async def get_usuarios(admin_user: User = Depends(verify_admin), db: Session = Depends(get_db)):
    try:
        from sqlalchemy import text

        # Consulta para obtener usuarios con información de roles
        query = text("""
            SELECT
                u.id_usuarios, u.nombre, u.apellido, u.correo_electronico,
                u.id_rol, u.id_estados, u.fecha_registro, u.fecha_ultima_actividad,
                r.nombre_rol
            FROM usuarios u
            LEFT JOIN roles r ON u.id_rol = r.id_rol
            ORDER BY u.id_usuarios ASC
        """)

        result = db.execute(query)
        usuarios = []

        for row in result:
            usuario = dict(row._mapping)

            # Procesar fechas
            if usuario['fecha_registro']:
                usuario['fecha_registro'] = usuario['fecha_registro'].isoformat()
            if usuario['fecha_ultima_actividad']:
                usuario['fecha_ultima_actividad'] = usuario['fecha_ultima_actividad'].isoformat()

            usuarios.append(usuario)

        return {"usuarios": usuarios}

    except Exception as e:
        print(f"Error al obtener usuarios: {e}")
        return {"usuarios": []}

# API para cambiar el rol de un usuario
@router.put("/api/usuarios/{id_usuario}/rol")
async def cambiar_rol_usuario(
    id_usuario: int,
    nuevo_rol: dict,
    admin_user: User = Depends(verify_admin),
    db: Session = Depends(get_db)
):
    try:
        from sqlalchemy import text

        # Verificar que el usuario existe
        query = text("SELECT id_usuarios, id_rol FROM usuarios WHERE id_usuarios = :id_usuario")
        result = db.execute(query, {"id_usuario": id_usuario})
        usuario = result.fetchone()

        if not usuario:
            raise HTTPException(status_code=404, detail="Usuario no encontrado")

        # Verificar que el nuevo rol es válido
        nuevo_id_rol = nuevo_rol.get("id_rol")
        if nuevo_id_rol not in [1, 2]:
            raise HTTPException(status_code=400, detail="Rol inválido")

        # No permitir que el último administrador se quite el rol de admin
        if usuario.id_rol == 1 and nuevo_id_rol != 1:
            query_admins = text("SELECT COUNT(*) as total FROM usuarios WHERE id_rol = 1")
            result_admins = db.execute(query_admins)
            total_admins = result_admins.fetchone().total

            if total_admins <= 1:
                raise HTTPException(
                    status_code=400,
                    detail="No se puede quitar el rol de administrador al último administrador del sistema"
                )

        # Actualizar el rol
        update_query = text("""
            UPDATE usuarios
            SET id_rol = :nuevo_rol, fecha_ultima_actividad = NOW()
            WHERE id_usuarios = :id_usuario
        """)

        db.execute(update_query, {"nuevo_rol": nuevo_id_rol, "id_usuario": id_usuario})
        db.commit()

        return {"message": "Rol actualizado exitosamente"}

    except HTTPException:
        raise
    except Exception as e:
        print(f"Error al cambiar rol: {e}")
        raise HTTPException(status_code=500, detail="Error interno del servidor")

# API para cambiar el estado de un usuario
@router.put("/api/usuarios/{id_usuario}/estado")
async def cambiar_estado_usuario(
    id_usuario: int,
    nuevo_estado: dict,
    admin_user: User = Depends(verify_admin),
    db: Session = Depends(get_db)
):
    try:
        from sqlalchemy import text

        # Verificar que el usuario existe
        query = text("SELECT id_usuarios, id_rol FROM usuarios WHERE id_usuarios = :id_usuario")
        result = db.execute(query, {"id_usuario": id_usuario})
        usuario = result.fetchone()

        if not usuario:
            raise HTTPException(status_code=404, detail="Usuario no encontrado")

        # Verificar que el nuevo estado es válido
        nuevo_id_estado = nuevo_estado.get("id_estados")
        if nuevo_id_estado not in [1, 2]:
            raise HTTPException(status_code=400, detail="Estado inválido")

        # No permitir desactivar al último administrador
        if usuario.id_rol == 1 and nuevo_id_estado == 2:
            query_admins = text("SELECT COUNT(*) as total FROM usuarios WHERE id_rol = 1 AND id_estados = 1")
            result_admins = db.execute(query_admins)
            total_admins_activos = result_admins.fetchone().total

            if total_admins_activos <= 1:
                raise HTTPException(
                    status_code=400,
                    detail="No se puede desactivar al último administrador activo del sistema"
                )

        # Actualizar el estado
        update_query = text("""
            UPDATE usuarios
            SET id_estados = :nuevo_estado, fecha_ultima_actividad = NOW()
            WHERE id_usuarios = :id_usuario
        """)

        db.execute(update_query, {"nuevo_estado": nuevo_id_estado, "id_usuario": id_usuario})
        db.commit()

        return {"message": "Estado actualizado exitosamente"}

    except HTTPException:
        raise
    except Exception as e:
        print(f"Error al cambiar estado: {e}")
        raise HTTPException(status_code=500, detail="Error interno del servidor")

# ==================== RUTAS DE PÁGINAS ADICIONALES ====================

@router.get("/usuarios", response_class=HTMLResponse)
async def admin_usuarios_page(
    request: Request,
    admin_user: User = Depends(verify_admin)
):
    """Página de gestión de usuarios - Solo para administradores"""
    request.state.user = admin_user

    return templates.TemplateResponse("admin/usuarios.html", {
        "request": request,
        "user": admin_user,
        "user_id": str(admin_user.id_usuarios),
        "user_role": str(admin_user.id_rol)
    })

@router.get("/plagas", response_class=HTMLResponse)
async def admin_plagas_page(
    request: Request,
    admin_user: User = Depends(verify_admin)
):
    """Página de gestión de plagas - Solo para administradores"""
    request.state.user = admin_user

    return templates.TemplateResponse("admin/plagas.html", {
        "request": request,
        "user": admin_user,
        "user_id": str(admin_user.id_usuarios),
        "user_role": str(admin_user.id_rol)
    })

# ==================== APIS DE PLAGAS ====================

@router.get("/api/plagas")
async def get_plagas_admin(
    admin_user: User = Depends(verify_admin),
    db: Session = Depends(get_db)
):
    """Obtener todas las plagas - Solo para administradores"""
    try:
        from sqlalchemy import text

        # Consulta simplificada para evitar errores
        query = text("""
            SELECT
                p.id_plaga,
                p.nombre,
                p.descripcion,
                p.url_img as imagen_url,
                p.recomendaciones,
                p.fecha_registro,
                'INSECTOS' as tipo,
                'maduración' as fase_cultivo,
                'seca' as estacion,
                p.recomendaciones as tipo_plaguicida,
                0.0 as cantidad_metros_cubicos,
                p.recomendaciones as forma_aplicacion
            FROM plagas p
            ORDER BY p.nombre ASC
        """)

        result = db.execute(query)
        plagas = []

        for row in result:
            plaga = dict(row._mapping)

            # Procesar fecha
            if plaga['fecha_registro']:
                plaga['fecha_registro'] = plaga['fecha_registro'].isoformat()

            plagas.append(plaga)

        return {"plagas": plagas}

    except Exception as e:
        print(f"Error al obtener plagas: {e}")
        return {"plagas": []}

@router.post("/api/plagas")
async def crear_plaga(
    request: Request,
    admin_user: User = Depends(verify_admin),
    db: Session = Depends(get_db)
):
    """Crear nueva plaga - Solo para administradores"""
    try:
        from sqlalchemy import text
        import os
        from fastapi import Form, File, UploadFile

        # Obtener datos del formulario
        form = await request.form()

        nombre = form.get("nombre")
        tipo = form.get("tipo")
        descripcion = form.get("descripcion")
        fase_cultivo = form.get("fase_cultivo")
        estacion = form.get("estacion")
        tipo_plaguicida = form.get("tipo_plaguicida")
        cantidad_metros_cubicos = form.get("cantidad_metros_cubicos")
        forma_aplicacion = form.get("forma_aplicacion")
        imagen = form.get("imagen")

        # Validar campos requeridos
        if not all([nombre, tipo, descripcion, fase_cultivo, estacion, tipo_plaguicida, cantidad_metros_cubicos, forma_aplicacion]):
            raise HTTPException(status_code=400, detail="Todos los campos son requeridos")

        # Procesar imagen si se subió
        imagen_url = None
        if imagen and hasattr(imagen, 'filename') and imagen.filename:
            # Crear directorio si no existe
            upload_dir = "app/static/images"
            os.makedirs(upload_dir, exist_ok=True)

            # Generar nombre único para la imagen
            import uuid
            file_extension = os.path.splitext(imagen.filename)[1]
            unique_filename = f"{uuid.uuid4()}{file_extension}"
            file_path = os.path.join(upload_dir, unique_filename)

            # Guardar imagen
            with open(file_path, "wb") as buffer:
                content = await imagen.read()
                buffer.write(content)

            imagen_url = f"/static/images/{unique_filename}"

        # Insertar en la base de datos
        query = text("""
            INSERT INTO plagas (
                nombre, tipo, descripcion, fase_cultivo, estacion,
                tipo_plaguicida, cantidad_metros_cubicos, forma_aplicacion,
                imagen_url, fecha_registro
            ) VALUES (
                :nombre, :tipo, :descripcion, :fase_cultivo, :estacion,
                :tipo_plaguicida, :cantidad_metros_cubicos, :forma_aplicacion,
                :imagen_url, NOW()
            )
        """)

        db.execute(query, {
            "nombre": nombre,
            "tipo": tipo,
            "descripcion": descripcion,
            "fase_cultivo": fase_cultivo,
            "estacion": estacion,
            "tipo_plaguicida": tipo_plaguicida,
            "cantidad_metros_cubicos": float(cantidad_metros_cubicos),
            "forma_aplicacion": forma_aplicacion,
            "imagen_url": imagen_url
        })

        db.commit()

        return {"message": "Plaga creada exitosamente"}

    except HTTPException:
        raise
    except Exception as e:
        print(f"Error al crear plaga: {e}")
        raise HTTPException(status_code=500, detail="Error interno del servidor")

@router.delete("/api/plagas/{id_plaga}")
async def eliminar_plaga(
    id_plaga: int,
    admin_user: User = Depends(verify_admin),
    db: Session = Depends(get_db)
):
    """Eliminar plaga - Solo para administradores"""
    try:
        from sqlalchemy import text

        # Verificar que la plaga existe
        query = text("SELECT id_plaga, imagen_url FROM plagas WHERE id_plaga = :id_plaga")
        result = db.execute(query, {"id_plaga": id_plaga})
        plaga = result.fetchone()

        if not plaga:
            raise HTTPException(status_code=404, detail="Plaga no encontrada")

        # Eliminar imagen si existe
        if plaga.imagen_url:
            import os
            file_path = f"app/static{plaga.imagen_url}"
            if os.path.exists(file_path):
                os.remove(file_path)

        # Eliminar de la base de datos
        delete_query = text("DELETE FROM plagas WHERE id_plaga = :id_plaga")
        db.execute(delete_query, {"id_plaga": id_plaga})
        db.commit()

        return {"message": "Plaga eliminada exitosamente"}

    except HTTPException:
        raise
    except Exception as e:
        print(f"Error al eliminar plaga: {e}")
        raise HTTPException(status_code=500, detail="Error interno del servidor")

# ==================== API PARA ELIMINAR CULTIVOS ====================

@router.delete("/api/cultivos-usuarios/{id_cultivo}")
async def eliminar_cultivo_usuario(
    id_cultivo: int,
    admin_user: User = Depends(verify_admin),
    db: Session = Depends(get_db)
):
    """Eliminar cultivo de usuario - Solo para administradores"""
    try:
        from sqlalchemy import text

        # Verificar que el cultivo existe
        query = text("SELECT id_cultivo FROM cultivos WHERE id_cultivo = :id_cultivo")
        result = db.execute(query, {"id_cultivo": id_cultivo})
        cultivo = result.fetchone()

        if not cultivo:
            raise HTTPException(status_code=404, detail="Cultivo no encontrado")

        # Eliminar el cultivo (marcar como inactivo)
        update_query = text("""
            UPDATE cultivos
            SET activo = 0
            WHERE id_cultivo = :id_cultivo
        """)

        db.execute(update_query, {"id_cultivo": id_cultivo})
        db.commit()

        return {"message": "Cultivo eliminado exitosamente"}

    except HTTPException:
        raise
    except Exception as e:
        print(f"Error al eliminar cultivo: {e}")
        raise HTTPException(status_code=500, detail="Error interno del servidor")
