#!/usr/bin/env python3
"""
Script para insertar hongos, bacterias y virus adicionales basándose en las imágenes disponibles
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from app.database.config import engine
from sqlalchemy import text

def insertar_plagas_adicionales_parte2():
    """Insertar hongos, bacterias y virus adicionales"""
    try:
        with engine.connect() as conn:
            # Obtener el ID de un usuario administrador
            result = conn.execute(text("SELECT id_usuarios FROM usuarios WHERE id_rol = 1 LIMIT 1"))
            admin_user = result.fetchone()

            if not admin_user:
                result = conn.execute(text("SELECT id_usuarios FROM usuarios LIMIT 1"))
                admin_user = result.fetchone()
                admin_user_id = admin_user.id_usuarios if admin_user else 1
            else:
                admin_user_id = admin_user.id_usuarios

            # HONGOS ADICIONALES
            hongos_adicionales = [
                {
                    "nombre": "Alternaria",
                    "nombre_cientifico": "Alternaria alternata",
                    "descripcion": "Hongo que causa manchas foliares y pudrición de mazorcas, especialmente en condiciones de alta humedad y temperatura.",
                    "url_img": "/static/images/Alternaria.webp",
                    "recomendaciones": """
CONTROL QUÍMICO:
• Fungicida: Difenoconazol 25% EC
• Cantidad: 0.3-0.5 litros por hectárea (30-50 ml por 100 m²)
• Aplicación: Aspersión foliar al detectar síntomas
• Frecuencia: Cada 10-15 días según severidad

MANEJO INTEGRADO:
• Eliminación de residuos infectados
• Rotación con cultivos no susceptibles
• Mejoramiento de drenaje
• Uso de variedades resistentes
                    """
                },
                {
                    "nombre": "Aspergillus",
                    "nombre_cientifico": "Aspergillus niger",
                    "descripcion": "Hongo que causa pudrición negra en mazorcas y puede producir micotoxinas. Común en condiciones de almacenamiento inadecuado.",
                    "url_img": "/static/images/Aspergillus.webp",
                    "recomendaciones": """
CONTROL QUÍMICO:
• Fungicida: Tebuconazol 25% EC
• Cantidad: 0.5-0.7 litros por hectárea (50-70 ml por 100 m²)
• Aplicación: Aspersión preventiva durante llenado de grano
• Frecuencia: 2 aplicaciones con intervalo de 20 días

CONTROL POST-COSECHA:
• Secado inmediato <14% humedad
• Almacenamiento en condiciones secas
• Control de temperatura <25°C
• Monitoreo de micotoxinas
                    """
                },
                {
                    "nombre": "Cladosporium",
                    "nombre_cientifico": "Cladosporium herbarum",
                    "descripcion": "Hongo que causa manchas grisáceas en hojas y mazorcas. Puede aparecer en condiciones de sequía y alta temperatura.",
                    "url_img": "/static/images/Cladosporium.jpg",
                    "recomendaciones": """
CONTROL QUÍMICO:
• Fungicida: Mancozeb 80% WP
• Cantidad: 2.0-2.5 kg por hectárea (200-250 g por 100 m²)
• Aplicación: Aspersión foliar preventiva
• Frecuencia: Cada 20 días en condiciones favorables

MANEJO AMBIENTAL:
• Control de humedad relativa
• Mejoramiento de ventilación
• Eliminación de material infectado
• Monitoreo regular de síntomas
                    """
                },
                {
                    "nombre": "Fusarium",
                    "nombre_cientifico": "Fusarium moniliforme",
                    "descripcion": "Hongo que causa pudrición rosada en mazorcas y produce fumonisinas. Una de las enfermedades más importantes en maíz.",
                    "url_img": "/static/images/fusarium.jpg",
                    "recomendaciones": """
CONTROL QUÍMICO:
• Fungicida: Propiconazol 25% EC
• Cantidad: 0.5-0.8 litros por hectárea (50-80 ml por 100 m²)
• Aplicación: Aspersión dirigida a mazorcas durante floración
• Frecuencia: 2 aplicaciones con intervalo de 15 días

MANEJO CULTURAL:
• Cosecha oportuna con humedad <20%
• Secado rápido post-cosecha
• Almacenamiento con humedad <14%
• Rotación de cultivos
                    """
                },
                {
                    "nombre": "Penicillium",
                    "nombre_cientifico": "Penicillium oxalicum",
                    "descripcion": "Hongo que causa pudrición azul-verde en granos almacenados y puede producir micotoxinas como la patulina.",
                    "url_img": "/static/images/Penicillium.jpg",
                    "recomendaciones": """
CONTROL QUÍMICO:
• Fungicida: Imazalil 50% EC
• Cantidad: 0.3-0.5 litros por hectárea (30-50 ml por 100 m²)
• Aplicación: Tratamiento de semillas y aspersión preventiva
• Frecuencia: Una aplicación preventiva

CONTROL POST-COSECHA:
• Secado rápido <14% humedad
• Almacenamiento hermético
• Control de temperatura y humedad
• Limpieza de instalaciones
                    """
                },
                {
                    "nombre": "Mancha de la Espiga",
                    "nombre_cientifico": "Curvularia lunata",
                    "descripcion": "Hongo que afecta específicamente las espigas del maíz, causando manchas oscuras y reduciendo la producción de polen.",
                    "url_img": "/static/images/Mancha de la espiga.jpg",
                    "recomendaciones": """
CONTROL QUÍMICO:
• Fungicida: Azoxistrobina 25% SC
• Cantidad: 0.8-1.0 litros por hectárea (80-100 ml por 100 m²)
• Aplicación: Aspersión dirigida a espigas durante emergencia
• Frecuencia: 2 aplicaciones con intervalo de 10 días

MANEJO PREVENTIVO:
• Eliminación de espigas infectadas
• Mejoramiento de ventilación
• Control de humedad relativa
• Uso de variedades resistentes
                    """
                },
                {
                    "nombre": "Mancha de la Hoja",
                    "nombre_cientifico": "Exserohilum turcicum",
                    "descripcion": "Hongo que causa manchas alargadas en las hojas, reduciendo el área fotosintética y afectando el rendimiento.",
                    "url_img": "/static/images/Mancha de la hoja.jpg",
                    "recomendaciones": """
CONTROL QUÍMICO:
• Fungicida: Triazol + Estrobilurina
• Cantidad: 0.5-0.8 litros por hectárea (50-80 ml por 100 m²)
• Aplicación: Aspersión foliar al detectar primeros síntomas
• Frecuencia: Cada 15 días según severidad

MANEJO CULTURAL:
• Rotación con cultivos no gramíneas
• Eliminación de residuos infectados
• Uso de variedades resistentes
• Mejoramiento de drenaje
                    """
                },
                {
                    "nombre": "Mancha Foliar",
                    "nombre_cientifico": "Helminthosporium maydis",
                    "descripcion": "Hongo que causa manchas foliares características, debilitando la planta y reduciendo la capacidad fotosintética.",
                    "url_img": "/static/images/Mancha foliar.jpg",
                    "recomendaciones": """
CONTROL QUÍMICO:
• Fungicida: Carbendazim 50% WP
• Cantidad: 0.5-0.8 kg por hectárea (50-80 g por 100 m²)
• Aplicación: Aspersión foliar preventiva
• Frecuencia: Cada 20 días durante período crítico

MANEJO INTEGRADO:
• Uso de semillas certificadas
• Rotación de cultivos
• Eliminación de malezas hospederas
• Monitoreo regular de síntomas
                    """
                },
                {
                    "nombre": "Marchitez de la Raíz",
                    "nombre_cientifico": "Pythium ultimum",
                    "descripcion": "Hongo del suelo que causa marchitez y pudrición de raíces, especialmente en condiciones de exceso de humedad.",
                    "url_img": "/static/images/Marchitez de la Raíz.jpg",
                    "recomendaciones": """
CONTROL QUÍMICO:
• Fungicida: Metalaxil 25% WP
• Cantidad: 0.5-0.8 kg por hectárea (50-80 g por 100 m²)
• Aplicación: Drench al suelo
• Frecuencia: Al detectar síntomas

MANEJO CULTURAL:
• Mejoramiento del drenaje
• Evitar encharcamientos
• Rotación con cultivos no susceptibles
• Desinfección del suelo
                    """
                }
            ]

            # BACTERIAS ADICIONALES
            bacterias_adicionales = [
                {
                    "nombre": "Clavibacter michiganensis",
                    "nombre_cientifico": "Clavibacter michiganensis subsp. michiganensis",
                    "descripcion": "Bacteria que causa marchitez vascular y cancros en tallos, afectando principalmente a tomates pero también puede impactar al maíz.",
                    "url_img": "/static/images/Clavibacter michiganensis subsp. Michiganensis.jpg",
                    "recomendaciones": """
CONTROL QUÍMICO:
• Bactericida: Kasugamicina 2% SL
• Cantidad: 1.0-1.5 litros por hectárea (100-150 ml por 100 m²)
• Aplicación: Aspersión foliar
• Frecuencia: Cada 10 días al detectar síntomas

MANEJO INTEGRADO:
• Uso de semillas certificadas
• Desinfección de herramientas con alcohol 70%
• Eliminación de plantas infectadas
• Rotación con cultivos no hospederos
                    """
                },
                {
                    "nombre": "Ralstonia",
                    "nombre_cientifico": "Ralstonia solanacearum",
                    "descripcion": "Bacteria que causa marchitez bacteriana, bloqueando el sistema vascular de la planta y causando muerte súbita.",
                    "url_img": "/static/images/Ralstonia.jpg",
                    "recomendaciones": """
CONTROL QUÍMICO:
• Bactericida: Estreptomicina 20% + Oxitetraciclina 1.5%
• Cantidad: 0.5-0.8 kg por hectárea (50-80 g por 100 m²)
• Aplicación: Aspersión foliar y drench al suelo
• Frecuencia: Cada 10-15 días según severidad

CONTROL PREVENTIVO:
• Mejoramiento del drenaje
• Desinfección de herramientas
• Eliminación de plantas infectadas
• Control de vectores
                    """
                },
                {
                    "nombre": "Pudrición Bacteriana",
                    "nombre_cientifico": "Erwinia carotovora",
                    "descripcion": "Bacteria que causa pudrición blanda y maloliente en tallos y mazorcas, especialmente en condiciones de alta humedad.",
                    "url_img": "/static/images/Pudrición Bacteriana.jpg",
                    "recomendaciones": """
CONTROL QUÍMICO:
• Bactericida: Sulfato de cobre pentahidratado 25% WP
• Cantidad: 1.5-2.0 kg por hectárea (150-200 g por 100 m²)
• Aplicación: Aspersión foliar preventiva
• Frecuencia: Cada 15 días en condiciones húmedas

MANEJO SANITARIO:
• Eliminación inmediata de plantas infectadas
• Mejoramiento del drenaje
• Evitar heridas en plantas
• Control de insectos vectores
                    """
                }
            ]

            # Insertar HONGOS ADICIONALES
            for plaga in hongos_adicionales:
                result = conn.execute(text("""
                    SELECT COUNT(*) as count FROM plagas
                    WHERE nombre = :nombre
                """), {"nombre": plaga["nombre"]})

                if result.fetchone().count == 0:
                    conn.execute(text("""
                        INSERT INTO plagas (
                            nombre, nombre_cientifico, url_img, descripcion, recomendaciones,
                            id_fase_cultivo, id_tipo_plaga, id_temporada, id_usuarios
                        ) VALUES (
                            :nombre, :nombre_cientifico, :url_img, :descripcion, :recomendaciones,
                            4, 3, 1, :id_usuarios
                        )
                    """), {
                        **plaga,
                        "id_usuarios": admin_user_id
                    })
                    print(f"✅ Hongo '{plaga['nombre']}' insertado.")

            # Insertar BACTERIAS ADICIONALES
            for plaga in bacterias_adicionales:
                result = conn.execute(text("""
                    SELECT COUNT(*) as count FROM plagas
                    WHERE nombre = :nombre
                """), {"nombre": plaga["nombre"]})

                if result.fetchone().count == 0:
                    conn.execute(text("""
                        INSERT INTO plagas (
                            nombre, nombre_cientifico, url_img, descripcion, recomendaciones,
                            id_fase_cultivo, id_tipo_plaga, id_temporada, id_usuarios
                        ) VALUES (
                            :nombre, :nombre_cientifico, :url_img, :descripcion, :recomendaciones,
                            4, 4, 1, :id_usuarios
                        )
                    """), {
                        **plaga,
                        "id_usuarios": admin_user_id
                    })
                    print(f"✅ Bacteria '{plaga['nombre']}' insertada.")

            conn.commit()
            print(f"\n🎉 Hongos y bacterias adicionales insertados exitosamente!")

    except Exception as e:
        print(f"❌ Error al insertar plagas adicionales: {e}")

if __name__ == "__main__":
    print("🌽 Insertando hongos y bacterias adicionales...")
    insertar_plagas_adicionales_parte2()
