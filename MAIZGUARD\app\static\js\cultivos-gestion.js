// Gestión de cultivos - <PERSON><PERSON>, Editar, Eliminar
document.addEventListener('DOMContentLoaded', function() {
    // Filtrar plagas según la fase del cultivo seleccionada
    const faseCultivoSelect = document.getElementById('fase_cultivo');
    const plagaSelect = document.getElementById('plaga_afecta');
    
    if (faseCultivoSelect && plagaSelect) {
        faseCultivoSelect.addEventListener('change', function() {
            const faseSeleccionada = this.value;
            const opciones = plagaSelect.querySelectorAll('option');
            
            opciones.forEach(opcion => {
                if (opcion.value === '') {
                    opcion.style.display = 'block';
                    return;
                }
                
                const faseOpcion = opcion.getAttribute('data-fase');
                if (faseSeleccionada === '' || faseOpcion === faseSeleccionada) {
                    opcion.style.display = 'block';
                } else {
                    opcion.style.display = 'none';
                }
            });
            
            // Resetear selección si la opción actual no es válida
            if (plagaSelect.value !== '') {
                const opcionActual = plagaSelect.querySelector(`option[value="${plagaSelect.value}"]`);
                if (opcionActual && opcionActual.style.display === 'none') {
                    plagaSelect.value = '';
                }
            }
        });
    }
});

// Variable global para almacenar los datos de cultivos
let cultivosData = [];

// Función para cargar datos de cultivos (se puede llamar desde el template)
function cargarCultivos(cultivos) {
    cultivosData = cultivos;
}

// Función para ver detalles del cultivo
function verDetalleCultivo(idCultivo) {
    const cultivo = cultivosData.find(c => c.id_cultivo === idCultivo);
    if (!cultivo) {
        alert('Cultivo no encontrado');
        return;
    }
    
    const contenido = `
        <div class="row">
            <div class="col-md-6">
                <h6 class="text-success">Información General</h6>
                <table class="table table-sm">
                    <tr><td><strong>Nombre:</strong></td><td>${cultivo.nombre}</td></tr>
                    <tr><td><strong>Descripción:</strong></td><td>${cultivo.descripcion}</td></tr>
                    <tr><td><strong>Fase:</strong></td><td><span class="badge bg-${cultivo.fase_cultivo === 'cosecha' ? 'success' : 'warning'}">${cultivo.fase_cultivo}</span></td></tr>
                    <tr><td><strong>Ubicación:</strong></td><td>${cultivo.ubicacion || 'No especificada'}</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6 class="text-success">Medidas y Cantidad</h6>
                <table class="table table-sm">
                    <tr><td><strong>Cantidad:</strong></td><td>${cultivo.cantidad} ${cultivo.unidad_cantidad}</td></tr>
                    <tr><td><strong>Dimensiones:</strong></td><td>${cultivo.ancho} × ${cultivo.altura} metros</td></tr>
                    <tr><td><strong>Área total:</strong></td><td>${(cultivo.ancho * cultivo.altura).toFixed(2)} m²</td></tr>
                    <tr><td><strong>Fecha siembra:</strong></td><td>${cultivo.fecha_siembra ? new Date(cultivo.fecha_siembra).toLocaleDateString() : 'No especificada'}</td></tr>
                </table>
            </div>
        </div>
        ${cultivo.notas ? `
        <div class="row mt-3">
            <div class="col-12">
                <h6 class="text-success">Notas Adicionales</h6>
                <p class="text-muted">${cultivo.notas}</p>
            </div>
        </div>
        ` : ''}
        <div class="row mt-3">
            <div class="col-12">
                <h6 class="text-success">Fechas Importantes</h6>
                <table class="table table-sm">
                    <tr><td><strong>Fecha de registro:</strong></td><td>${new Date(cultivo.fecha_registro).toLocaleDateString()}</td></tr>
                    ${cultivo.fecha_estimada_cosecha ? `<tr><td><strong>Cosecha estimada:</strong></td><td>${new Date(cultivo.fecha_estimada_cosecha).toLocaleDateString()}</td></tr>` : ''}
                </table>
            </div>
        </div>
    `;
    
    document.getElementById('contenidoDetalleCultivo').innerHTML = contenido;
    new bootstrap.Modal(document.getElementById('modalDetalleCultivo')).show();
}

// Función para editar cultivo
function editarCultivo(idCultivo) {
    const cultivo = cultivosData.find(c => c.id_cultivo === idCultivo);
    if (!cultivo) {
        alert('Cultivo no encontrado');
        return;
    }
    
    const contenido = `
        <input type="hidden" name="id_cultivo" value="${cultivo.id_cultivo}">
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="edit_nombre" class="form-label">Nombre del Cultivo</label>
                    <input type="text" class="form-control" id="edit_nombre" name="nombre" value="${cultivo.nombre}" required>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="edit_ubicacion" class="form-label">Ubicación</label>
                    <input type="text" class="form-control" id="edit_ubicacion" name="ubicacion" value="${cultivo.ubicacion || ''}">
                </div>
            </div>
            <div class="col-12">
                <div class="mb-3">
                    <label for="edit_descripcion" class="form-label">Descripción</label>
                    <textarea class="form-control" id="edit_descripcion" name="descripcion" rows="3" required>${cultivo.descripcion}</textarea>
                </div>
            </div>
            <div class="col-md-4">
                <div class="mb-3">
                    <label for="edit_cantidad" class="form-label">Cantidad</label>
                    <input type="number" class="form-control" id="edit_cantidad" name="cantidad" value="${cultivo.cantidad}" required min="1">
                </div>
            </div>
            <div class="col-md-4">
                <div class="mb-3">
                    <label for="edit_unidad_cantidad" class="form-label">Unidad</label>
                    <select class="form-select" id="edit_unidad_cantidad" name="unidad_cantidad" required>
                        <option value="plantas" ${cultivo.unidad_cantidad === 'plantas' ? 'selected' : ''}>Plantas</option>
                        <option value="metros_cuadrados" ${cultivo.unidad_cantidad === 'metros_cuadrados' ? 'selected' : ''}>Metros Cuadrados</option>
                        <option value="hectareas" ${cultivo.unidad_cantidad === 'hectareas' ? 'selected' : ''}>Hectáreas</option>
                    </select>
                </div>
            </div>
            <div class="col-md-4">
                <div class="mb-3">
                    <label for="edit_fase_cultivo" class="form-label">Fase del Cultivo</label>
                    <select class="form-select" id="edit_fase_cultivo" name="fase_cultivo" required>
                        <option value="maduracion" ${cultivo.fase_cultivo === 'maduracion' ? 'selected' : ''}>Maduración</option>
                        <option value="cosecha" ${cultivo.fase_cultivo === 'cosecha' ? 'selected' : ''}>Cosecha</option>
                    </select>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="edit_ancho" class="form-label">Ancho (metros)</label>
                    <input type="number" class="form-control" id="edit_ancho" name="ancho" value="${cultivo.ancho}" required step="0.01" min="0.01">
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="edit_altura" class="form-label">Altura (metros)</label>
                    <input type="number" class="form-control" id="edit_altura" name="altura" value="${cultivo.altura}" required step="0.01" min="0.01">
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="edit_fecha_siembra" class="form-label">Fecha de Siembra</label>
                    <input type="date" class="form-control" id="edit_fecha_siembra" name="fecha_siembra" value="${cultivo.fecha_siembra ? cultivo.fecha_siembra.split('T')[0] : ''}">
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="edit_fecha_estimada_cosecha" class="form-label">Fecha Estimada de Cosecha</label>
                    <input type="date" class="form-control" id="edit_fecha_estimada_cosecha" name="fecha_estimada_cosecha" value="${cultivo.fecha_estimada_cosecha ? cultivo.fecha_estimada_cosecha.split('T')[0] : ''}">
                </div>
            </div>
            <div class="col-12">
                <div class="mb-3">
                    <label for="edit_notas" class="form-label">Notas Adicionales</label>
                    <textarea class="form-control" id="edit_notas" name="notas" rows="2">${cultivo.notas || ''}</textarea>
                </div>
            </div>
        </div>
    `;
    
    document.getElementById('contenidoEditarCultivo').innerHTML = contenido;
    document.getElementById('formEditarCultivo').action = `/cultivos/editar/${cultivo.id_cultivo}`;
    new bootstrap.Modal(document.getElementById('modalEditarCultivo')).show();
}

// Función para eliminar cultivo
function eliminarCultivo(idCultivo, nombreCultivo) {
    document.getElementById('nombreCultivoEliminar').textContent = nombreCultivo;
    
    const btnConfirmar = document.getElementById('btnConfirmarEliminar');
    btnConfirmar.onclick = function() {
        // Realizar petición de eliminación
        fetch(`/cultivos/eliminar/${idCultivo}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => {
            if (response.ok) {
                // Recargar la página para mostrar los cambios
                window.location.reload();
            } else {
                alert('Error al eliminar el cultivo');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error al eliminar el cultivo');
        });
        
        // Cerrar modal
        bootstrap.Modal.getInstance(document.getElementById('modalEliminarCultivo')).hide();
    };
    
    new bootstrap.Modal(document.getElementById('modalEliminarCultivo')).show();
}

// Manejar envío del formulario de edición
document.getElementById('formEditarCultivo').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const action = this.action;
    
    fetch(action, {
        method: 'POST',
        body: formData
    })
    .then(response => {
        if (response.ok) {
            // Recargar la página para mostrar los cambios
            window.location.reload();
        } else {
            alert('Error al actualizar el cultivo');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error al actualizar el cultivo');
    });
});
