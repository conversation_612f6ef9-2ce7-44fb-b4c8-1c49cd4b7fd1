from sqlalchemy import Column, Integer, String, Date, DateTime, func, Boolean, ForeignKey
from sqlalchemy.orm import relationship
from .config import Base

class User(Base):
    __tablename__ = "usuarios"

    id_usuarios = Column(Integer, primary_key=True, index=True)
    id_estados = Column(Integer, ForeignKey("estados.id_estados"), nullable=False, default=1)
    id_rol = Column(Integer, ForeignKey("roles.id_rol"), nullable=False, default=2)
    nombre = Column(String(40), nullable=False)
    apellido = Column(String(40), nullable=False)
    contrasena = Column(String(255), nullable=False)
    correo_electronico = Column(String(100), unique=True, nullable=False, index=True)
    fotografia = Column(String(255), nullable=True)
    fecha_registro = Column(DateTime, default=func.now())
    token_recuperacion = Column(String(250), nullable=True)
    fecha_expiracion_token = Column(DateTime, nullable=True)

    # Relaciones
    estado = relationship("Estado", back_populates="usuarios")
    rol = relationship("Rol", back_populates="usuarios")

class Estado(Base):
    __tablename__ = "estados"

    id_estados = Column(Integer, primary_key=True, index=True)
    descripcion = Column(String(50), nullable=False)

    # Relaciones
    usuarios = relationship("User", back_populates="estado")

class Rol(Base):
    __tablename__ = "roles"

    id_rol = Column(Integer, primary_key=True, index=True)
    nombre = Column(String(50), nullable=False)
    descripcion = Column(String(255), nullable=True)

    # Relaciones
    usuarios = relationship("User", back_populates="rol")

class HistorialBusqueda(Base):
    __tablename__ = "historial_busqueda"

    id_historial = Column(Integer, primary_key=True, index=True)
    id_usuario = Column(Integer, ForeignKey("usuarios.id_usuarios"), nullable=False)
    termino_busqueda = Column(String(255), nullable=False)
    fecha_busqueda = Column(DateTime, default=func.now())

    # Relaciones
    usuario = relationship("User")
