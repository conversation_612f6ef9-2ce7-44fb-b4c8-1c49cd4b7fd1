#!/usr/bin/env python3
"""
Script para corregir la clasificación final de plagas mal categorizadas
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from app.database.config import engine
from sqlalchemy import text

def corregir_clasificacion_final():
    """Corregir la clasificación final de plagas"""
    try:
        with engine.connect() as conn:
            # Obtener IDs de tipos de plaga
            result = conn.execute(text("SELECT id_tipo_plaga, nombre FROM tipos_plaga"))
            tipos = {row.nombre: row.id_tipo_plaga for row in result}
            
            print("🔧 Corrigiendo clasificación final de plagas...")
            
            # HONGOS que están mal clasificados como BACTERIAS
            hongos_mal_clasificados = [
                "Alternaria",
                "Aspergillus", 
                "Clados<PERSON><PERSON>",
                "Fusarium",
                "Mancha de la Espiga",
                "Mancha de la Hoja",
                "Mancha Foliar",
                "March<PERSON>z de la Raíz",
                "Penicillium"
            ]
            
            # BACTERIAS que están mal clasificadas como VIRUS
            bacterias_mal_clasificadas = [
                "Clavibacter michiganensis",
                "Pudrición Bacteriana",
                "Ralstonia"
            ]
            
            # Corregir HONGOS
            for plaga_nombre in hongos_mal_clasificados:
                result = conn.execute(text("""
                    UPDATE plagas 
                    SET id_tipo_plaga = :tipo_correcto 
                    WHERE nombre = :nombre
                """), {
                    "tipo_correcto": tipos["Hongo"],
                    "nombre": plaga_nombre
                })
                
                if result.rowcount > 0:
                    print(f"✅ '{plaga_nombre}' corregido a HONGO")
                else:
                    print(f"⚠️  No se encontró '{plaga_nombre}'")
            
            # Corregir BACTERIAS
            for plaga_nombre in bacterias_mal_clasificadas:
                result = conn.execute(text("""
                    UPDATE plagas 
                    SET id_tipo_plaga = :tipo_correcto 
                    WHERE nombre = :nombre
                """), {
                    "tipo_correcto": tipos["Bacteria"],
                    "nombre": plaga_nombre
                })
                
                if result.rowcount > 0:
                    print(f"✅ '{plaga_nombre}' corregido a BACTERIA")
                else:
                    print(f"⚠️  No se encontró '{plaga_nombre}'")
            
            conn.commit()
            print("\n🎉 Corrección final completada!")
            
            # Verificar corrección
            print("\n📊 Verificando corrección final...")
            result = conn.execute(text("""
                SELECT tp.nombre as tipo, COUNT(p.id_plaga) as cantidad
                FROM tipos_plaga tp
                LEFT JOIN plagas p ON tp.id_tipo_plaga = p.id_tipo_plaga
                WHERE p.id_fase_cultivo = 4 AND p.id_temporada = 1
                GROUP BY tp.id_tipo_plaga, tp.nombre
                ORDER BY tp.nombre
            """))
            
            print("Distribución final corregida:")
            total = 0
            for row in result:
                print(f"  📊 {row.tipo}: {row.cantidad} plagas")
                total += row.cantidad
            
            print(f"\n🎯 TOTAL FINAL: {total} plagas")
            
    except Exception as e:
        print(f"❌ Error al corregir clasificación: {e}")

if __name__ == "__main__":
    print("🌽 Corrigiendo clasificación final de plagas...")
    corregir_clasificacion_final()
