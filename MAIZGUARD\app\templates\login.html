<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Iniciar Se<PERSON><PERSON> - MAIZGUARD</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <!-- Estilos personalizados -->
    <link rel="stylesheet" href="/static/css/main.css">
    <link rel="stylesheet" href="/static/css/compact.css">
    <link rel="stylesheet" href="/static/css/fixed-navbar.css">
    <link rel="stylesheet" href="/static/css/super-responsive.css">
    <link rel="stylesheet" href="/static/css/small-laptop.css">
    <link rel="preload" href="/static/images/Logo.jpeg" as="image">
</head>
<body class="d-flex flex-column min-vh-100">
    <!-- Incluir el navbar -->
    {% include "partials/navbar.html" %}

    <!-- Formulario de Inicio de Sesión -->
    <section class="py-2">
        <div class="container login-container">
            <div class="row justify-content-center">
                <div class="col-md-12">
                    <div class="card shadow-lg border-0 rounded-lg login-card">
                        <div class="card-header bg-success text-white text-center py-2">
                            <h4 class="mb-0 fw-bold">Iniciar Sesión</h4>
                        </div>
                        {% if error %}
                        <div class="alert alert-danger alert-dismissible fade show m-3" role="alert">
                            {{ error }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                        {% endif %}
                        {% if success %}
                        <div class="alert alert-success alert-dismissible fade show m-3" role="alert">
                            {{ success }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                        {% endif %}
                        <div class="card-body p-2">
                            <form action="/login" method="post" id="login-form">
                                <div class="mb-2">
                                    <label for="inputEmail" class="form-label">Correo electrónico</label>
                                    <input class="form-control" id="inputEmail" type="email" name="email" placeholder="<EMAIL>" required />
                                </div>
                                <div class="mb-2">
                                    <label for="inputPassword" class="form-label">Contraseña</label>
                                    <div class="input-group">
                                        <input class="form-control" id="inputPassword" type="password" name="password" placeholder="Contraseña" required />
                                        <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('inputPassword')" title="Mostrar/ocultar contraseña">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="d-flex align-items-center justify-content-between mt-2 mb-0">
                                    <a class="small text-success" href="/recuperar-password">¿Olvidaste tu contraseña?</a>
                                    <button class="btn btn-success btn-sm" type="submit">
                                        Iniciar Sesión
                                    </button>
                                </div>
                            </form>
                        </div>
                        <div class="card-footer text-center py-2">
                            <div class="small">
                                ¿No tienes una cuenta? <a href="/registro" class="text-success">¡Regístrate ahora!</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Incluir el footer -->
    {% include "partials/footer.html" %}

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Scripts personalizados -->
    <script src="/static/js/scripts.js"></script>
    <script src="/static/js/script.js"></script>

    <!-- Script específico para ojitos de contraseña -->
    <script>
        function togglePassword(inputId) {
            const input = document.getElementById(inputId);
            const button = document.querySelector(`[onclick*="${inputId}"]`);
            const icon = button ? button.querySelector('i') : null;

            if (input && icon) {
                if (input.type === 'password') {
                    input.type = 'text';
                    icon.className = 'fas fa-eye-slash';
                } else {
                    input.type = 'password';
                    icon.className = 'fas fa-eye';
                }
            }
        }
    </script>
</body>
</html>
