from app.database.config import engine
from sqlalchemy import text

def actualizar_tipos_plaga():
    """
    Actualiza los tipos de plaga para incluir: Insecto, Ácaro, Hongo, Bacteria y Virus.
    """
    try:
        with engine.connect() as conn:
            print("Actualizando tipos de plaga...")
            
            # Actualizar los tipos de plaga existentes
            conn.execute(text("UPDATE tipos_plaga SET descripcion = 'Insecto' WHERE id_tipo_plaga = 1"))
            conn.execute(text("UPDATE tipos_plaga SET descripcion = 'Ácaro' WHERE id_tipo_plaga = 2"))
            conn.execute(text("UPDATE tipos_plaga SET descripcion = 'Hongo' WHERE id_tipo_plaga = 3"))
            conn.execute(text("UPDATE tipos_plaga SET descripcion = 'Bacteria' WHERE id_tipo_plaga = 4"))
            conn.execute(text("UPDATE tipos_plaga SET descripcion = 'Virus' WHERE id_tipo_plaga = 5"))
            
            # Eliminar tipos adicionales si existen
            conn.execute(text("DELETE FROM tipos_plaga WHERE id_tipo_plaga > 5"))
            
            # Actualizar las plagas existentes
            print("\nActualizando categorías de plagas existentes...")
            
            # Araña roja: Ácaro
            conn.execute(text("""
                UPDATE plagas SET id_tipo_plaga = 2
                WHERE nombre = 'Araña roja'
            """))
            
            # Roya del maíz y Tizón foliar: Hongo
            conn.execute(text("""
                UPDATE plagas SET id_tipo_plaga = 3
                WHERE nombre IN ('Roya del maíz', 'Tizón foliar')
            """))
            
            # Confirmar cambios
            conn.commit()
            
            # Mostrar tipos de plaga actualizados
            print("\nTIPOS DE PLAGA ACTUALIZADOS:")
            result = conn.execute(text("SELECT * FROM tipos_plaga"))
            tipos = result.fetchall()
            for tipo in tipos:
                print(f"ID: {tipo[0]}, Descripción: {tipo[1]}")
            
            # Mostrar plagas actualizadas
            print("\nPLAGAS ACTUALIZADAS:")
            result = conn.execute(text("""
                SELECT p.id_plaga, p.nombre, tp.descripcion as tipo
                FROM plagas p
                LEFT JOIN tipos_plaga tp ON p.id_tipo_plaga = tp.id_tipo_plaga
                ORDER BY p.id_plaga
            """))
            plagas = result.fetchall()
            for plaga in plagas:
                print(f"ID: {plaga[0]}, Nombre: {plaga[1]}, Tipo: {plaga[2]}")
            
    except Exception as e:
        print(f"Error al actualizar tipos de plaga: {e}")

if __name__ == "__main__":
    actualizar_tipos_plaga()
