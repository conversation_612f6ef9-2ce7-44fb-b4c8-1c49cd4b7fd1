#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script para actualizar las recomendaciones detalladas de plagas adicionales.
"""

import os
import sys

# Añadir el directorio raíz al path para poder importar los módulos de la aplicación
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from app.database.config import engine
from sqlalchemy import text

def actualizar_recomendaciones_adicionales():
    """
    Actualiza las recomendaciones de plagas adicionales con información detallada sobre tipos de plaguicidas
    (químicos, polvo, líquido) y cantidades específicas en metros cúbicos o metros.
    """
    try:
        with engine.connect() as conn:
            print("Actualizando recomendaciones detalladas de plagas adicionales...")

            # Gallina ciega
            conn.execute(text("""
                UPDATE plaga
                SET recomendaciones = 'PLAGUICIDAS RECOMENDADOS:

1. QUÍMICOS LÍQUIDOS:
   - Imidacloprid 35 SC: 30-40 ml por 20 litros de agua (1.5-2 ml/litro)
   - Clorpirifos 48 EC: 50-60 ml por 20 litros de agua (2.5-3 ml/litro)
   - Fipronil 20 SC: 20-25 ml por 20 litros de agua (1-1.25 ml/litro)
   - Cobertura: 400-500 litros de mezcla por hectárea (0.04-0.05 litros/m²)

2. QUÍMICOS EN POLVO:
   - Diazinon 10 DP: Aplicar 25-30 kg/ha directamente al suelo (2.5-3 g/m²)
   - Carbofuran 5 G: Aplicar 20-25 kg/ha al momento de la siembra (2-2.5 g/m²)
   - Cobertura: Incorporar al suelo a una profundidad de 5-10 cm

3. BIOLÓGICOS:
   - Beauveria bassiana: 1-2 kg por hectárea (0.1-0.2 g/m²)
   - Metarhizium anisopliae: 1-2 kg por hectárea (0.1-0.2 g/m²)
   - Heterorhabditis bacteriophora (nematodos): 2.5-5 billones/ha

APLICACIÓN:
- Frecuencia: 1-2 aplicaciones antes de la siembra o durante la preparación del terreno
- Momento: Aplicar al suelo húmedo para mejor penetración
- Área de tratamiento: Tratar toda la superficie del cultivo y un margen de 5 metros
- Rotación: Implementar rotación de cultivos con leguminosas cada 2-3 temporadas'
                WHERE nombre LIKE '%Gallina ciega%'
            """))

            # Pulgones
            conn.execute(text("""
                UPDATE plaga
                SET recomendaciones = 'PLAGUICIDAS RECOMENDADOS:

1. QUÍMICOS LÍQUIDOS:
   - Imidacloprid 35 SC: 15-20 ml por 20 litros de agua (0.75-1 ml/litro)
   - Tiametoxam 25 WG: 10-15 g por 20 litros de agua (0.5-0.75 g/litro)
   - Acetamiprid 20 SP: 10-15 g por 20 litros de agua (0.5-0.75 g/litro)
   - Cobertura: 250-300 litros de mezcla por hectárea (0.025-0.03 litros/m²)

2. QUÍMICOS EN POLVO:
   - Pirimicarb 50 WG: 15-20 g por 20 litros de agua (0.75-1 g/litro)
   - Dinotefuran 20 SG: 10-15 g por 20 litros de agua (0.5-0.75 g/litro)
   - Cobertura: Asegurar buena cobertura del envés de las hojas

3. BIOLÓGICOS:
   - Liberación de Chrysoperla carnea: 10,000-15,000 huevos/ha
   - Liberación de Aphidius colemani: 0.5-1 individuo/m²
   - Extracto de ajo: 50-60 ml por 20 litros de agua (2.5-3 ml/litro)
   - Aceite de neem: 40-50 ml por 20 litros de agua (2-2.5 ml/litro)

APLICACIÓN:
- Frecuencia: 2-3 aplicaciones con intervalos de 7 días
- Momento: Temprano en la mañana o al atardecer
- Área de tratamiento: Tratar un radio de 10 metros alrededor de las plantas infestadas
- Monitoreo: Instalar trampas amarillas (1 trampa cada 100 m²) para detección temprana'
                WHERE nombre LIKE '%Pulgones%'
            """))

            # Trips
            conn.execute(text("""
                UPDATE plaga
                SET recomendaciones = 'PLAGUICIDAS RECOMENDADOS:

1. QUÍMICOS LÍQUIDOS:
   - Spinosad 12 SC: 10-15 ml por 20 litros de agua (0.5-0.75 ml/litro)
   - Imidacloprid 35 SC: 15-20 ml por 20 litros de agua (0.75-1 ml/litro)
   - Abamectina 1.8 EC: 10-15 ml por 20 litros de agua (0.5-0.75 ml/litro)
   - Cobertura: 300-400 litros de mezcla por hectárea (0.03-0.04 litros/m²)

2. QUÍMICOS EN POLVO:
   - Acefato 75 SP: 20-25 g por 20 litros de agua (1-1.25 g/litro)
   - Tiametoxam 25 WG: 10-15 g por 20 litros de agua (0.5-0.75 g/litro)
   - Cobertura: Asegurar buena cobertura del envés de las hojas y brotes tiernos

3. BIOLÓGICOS:
   - Ácaros depredadores Amblyseius swirskii: 50-100 individuos/m²
   - Ácaros depredadores Amblyseius cucumeris: 100-200 individuos/m²
   - Extracto de ajo: 50-60 ml por 20 litros de agua (2.5-3 ml/litro)
   - Aceite de neem: 40-50 ml por 20 litros de agua (2-2.5 ml/litro)

APLICACIÓN:
- Frecuencia: 2-3 aplicaciones con intervalos de 7 días
- Momento: Temprano en la mañana cuando la humedad es alta
- Área de tratamiento: Tratar un radio de 10 metros alrededor de las plantas infestadas
- Monitoreo: Instalar trampas azules (1 trampa cada 100 m²) para detección temprana'
                WHERE nombre LIKE '%Trips%'
            """))

            # Bacteriosis del maíz
            conn.execute(text("""
                UPDATE plaga
                SET recomendaciones = 'BACTERICIDAS RECOMENDADOS:

1. BACTERICIDAS LÍQUIDOS:
   - Kasugamicina 2 SL: 30-40 ml por 20 litros de agua (1.5-2 ml/litro)
   - Oxitetraciclina 20 WP: 20-25 g por 20 litros de agua (1-1.25 g/litro)
   - Ácido oxolínico 20 WP: 20-25 g por 20 litros de agua (1-1.25 g/litro)
   - Cobertura: 300-400 litros de mezcla por hectárea (0.03-0.04 litros/m²)

2. BACTERICIDAS EN POLVO:
   - Sulfato de cobre pentahidratado 24 SC: 40-50 ml por 20 litros de agua (2-2.5 ml/litro)
   - Hidróxido de cobre 50 WP: 40-50 g por 20 litros de agua (2-2.5 g/litro)
   - Cobertura: Asegurar buena cobertura del follaje

3. BIOLÓGICOS:
   - Bacillus subtilis: 2-3 kg por hectárea (0.2-0.3 g/m²)
   - Pseudomonas fluorescens: 2-3 kg por hectárea (0.2-0.3 g/m²)
   - Extracto de cítricos: 30-40 ml por 20 litros de agua (1.5-2 ml/litro)

APLICACIÓN:
- Frecuencia: 3-4 aplicaciones con intervalos de 7-10 días
- Momento: Aplicación preventiva en condiciones favorables para la enfermedad
- Área de tratamiento: Tratar un radio de 20 metros alrededor de las plantas infectadas
- Manejo de residuos: Eliminar y quemar residuos infectados en un área de al menos 500 m²
- Rotación: Implementar rotación de cultivos con especies no susceptibles por 2-3 años'
                WHERE nombre LIKE '%Bacteriosis del maíz%'
            """))

            # Virus del mosaico del maíz
            conn.execute(text("""
                UPDATE plagas
                SET recomendaciones = 'MANEJO INTEGRADO RECOMENDADO:

1. CONTROL DE VECTORES (INSECTICIDAS LÍQUIDOS):
   - Imidacloprid 35 SC: 15-20 ml por 20 litros de agua (0.75-1 ml/litro)
   - Tiametoxam 25 WG: 10-15 g por 20 litros de agua (0.5-0.75 g/litro)
   - Lambda-cihalotrina 5 CS: 15-20 ml por 20 litros de agua (0.75-1 ml/litro)
   - Cobertura: 250-300 litros de mezcla por hectárea (0.025-0.03 litros/m²)

2. CONTROL DE VECTORES (INSECTICIDAS EN POLVO):
   - Dinotefuran 20 SG: 10-15 g por 20 litros de agua (0.5-0.75 g/litro)
   - Acetamiprid 20 SP: 10-15 g por 20 litros de agua (0.5-0.75 g/litro)
   - Cobertura: Asegurar buena cobertura del follaje

3. MEDIDAS PREVENTIVAS:
   - Variedades resistentes: Utilizar híbridos con resistencia al virus del mosaico
   - Semillas certificadas: Usar semillas libres de virus (tratadas térmicamente)
   - Control de malezas: Eliminar hospederos alternos en un radio de 50 metros
   - Barreras vivas: Establecer barreras de 3-5 metros de ancho con cultivos no susceptibles

APLICACIÓN:
- Frecuencia: 2-3 aplicaciones con intervalos de 10-14 días para control de vectores
- Momento: Aplicar al detectar los primeros vectores
- Área de tratamiento: Tratar un radio de 20-30 metros alrededor de las plantas infectadas
- Eliminación: Remover y destruir plantas infectadas en un radio de 5 metros
- Desinfección: Desinfectar herramientas con solución de hipoclorito de sodio al 5%'
                WHERE nombre LIKE '%Virus del mosaico del maíz%'
            """))

            # Confirmar cambios
            conn.commit()

            # Verificar las actualizaciones
            result = conn.execute(text("""
                SELECT id_plaga, nombre, recomendaciones
                FROM plagas
                ORDER BY id_plaga
            """))

            plagas = result.fetchall()
            print("\nRecomendaciones detalladas actualizadas:")
            for plaga in plagas:
                print(f"ID: {plaga[0]}, Nombre: {plaga[1]}")
                print(f"Recomendaciones: {plaga[2][:100]}...")
                print()

            print("Actualización completada exitosamente.")

    except Exception as e:
        print(f"Error al actualizar recomendaciones detalladas de plagas adicionales: {e}")

if __name__ == "__main__":
    actualizar_recomendaciones_adicionales()
