from app.database.config import engine
from sqlalchemy import text

def corregir_categorias():
    """
    Corrige manualmente las categorías para asegurar que solo existan:
    - Fases: Maduración y Cosecha
    - Temporadas: Temporada de lluvia y Temporada de sequía
    """
    try:
        with engine.connect() as conn:
            # 1. Limpiar y recrear las fases de cultivo
            print("Corrigiendo fases de cultivo...")
            
            # Primero, actualizar todas las plagas a una fase temporal (ID 4)
            conn.execute(text("UPDATE plagas SET id_fase_cultivo = 4"))
            
            # Eliminar todas las fases excepto la 4 y 5
            conn.execute(text("DELETE FROM fases_cultivo WHERE id_fase_cultivo NOT IN (4, 5)"))
            
            # Asegurarse de que las fases 4 y 5 tengan los nombres correctos
            conn.execute(text("UPDATE fases_cultivo SET descripcion = 'Maduración' WHERE id_fase_cultivo = 4"))
            conn.execute(text("UPDATE fases_cultivo SET descripcion = 'Cosecha' WHERE id_fase_cultivo = 5"))
            
            # 2. Limpiar y recrear las temporadas
            print("\nCorrigiendo temporadas...")
            
            # Primero, actualizar todas las plagas a una temporada temporal (ID 1)
            conn.execute(text("UPDATE plagas SET id_temporada = 1"))
            
            # Eliminar todas las temporadas excepto la 1 y 2
            conn.execute(text("DELETE FROM temporadas WHERE id_temporada NOT IN (1, 2)"))
            
            # Asegurarse de que las temporadas 1 y 2 tengan los nombres correctos
            conn.execute(text("UPDATE temporadas SET descripcion = 'Temporada de lluvia' WHERE id_temporada = 1"))
            conn.execute(text("UPDATE temporadas SET descripcion = 'Temporada de sequía' WHERE id_temporada = 2"))
            
            # 3. Actualizar las plagas con las categorías correctas
            print("\nActualizando plagas con categorías correctas...")
            
            # Gusano cogollero: Cosecha, Sequía
            conn.execute(text("""
                UPDATE plagas SET 
                    id_fase_cultivo = 5, 
                    id_temporada = 2
                WHERE nombre = 'Gusano cogollero'
            """))
            
            # Gusano elotero: Cosecha, Sequía
            conn.execute(text("""
                UPDATE plagas SET 
                    id_fase_cultivo = 5, 
                    id_temporada = 2
                WHERE nombre = 'Gusano elotero'
            """))
            
            # Araña roja: Maduración, Sequía
            conn.execute(text("""
                UPDATE plagas SET 
                    id_fase_cultivo = 4, 
                    id_temporada = 2
                WHERE nombre = 'Araña roja'
            """))
            
            # Roya del maíz: Maduración, Lluvia
            conn.execute(text("""
                UPDATE plagas SET 
                    id_fase_cultivo = 4, 
                    id_temporada = 1
                WHERE nombre = 'Roya del maíz'
            """))
            
            # Tizón foliar: Maduración, Lluvia
            conn.execute(text("""
                UPDATE plagas SET 
                    id_fase_cultivo = 4, 
                    id_temporada = 1
                WHERE nombre = 'Tizón foliar'
            """))
            
            # Confirmar cambios
            conn.commit()
            
            # Mostrar categorías actualizadas
            print("\nCategorías corregidas:")
            
            print("\nFASES DE CULTIVO:")
            result = conn.execute(text("SELECT * FROM fases_cultivo"))
            fases = result.fetchall()
            for fase in fases:
                print(f"ID: {fase[0]}, Descripción: {fase[1]}")
            
            print("\nTEMPORADAS:")
            result = conn.execute(text("SELECT * FROM temporadas"))
            temporadas = result.fetchall()
            for temporada in temporadas:
                print(f"ID: {temporada[0]}, Descripción: {temporada[1]}")
            
            # Mostrar plagas actualizadas
            print("\nPLAGAS ACTUALIZADAS:")
            result = conn.execute(text("""
                SELECT p.id_plaga, p.nombre, fc.descripcion as fase, t.descripcion as temporada, tp.descripcion as tipo
                FROM plagas p
                LEFT JOIN fases_cultivo fc ON p.id_fase_cultivo = fc.id_fase_cultivo
                LEFT JOIN temporadas t ON p.id_temporada = t.id_temporada
                LEFT JOIN tipos_plaga tp ON p.id_tipo_plaga = tp.id_tipo_plaga
                ORDER BY p.id_plaga
            """))
            plagas = result.fetchall()
            for plaga in plagas:
                print(f"ID: {plaga[0]}, Nombre: {plaga[1]}, Fase: {plaga[2]}, Temporada: {plaga[3]}, Tipo: {plaga[4]}")
            
    except Exception as e:
        print(f"Error al corregir categorías: {e}")

if __name__ == "__main__":
    corregir_categorias()
