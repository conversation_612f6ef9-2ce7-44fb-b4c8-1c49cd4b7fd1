/* Estilos para la selección de avatares */

.avatar-selection {
    margin-top: 1rem;
    padding: 1rem;
    border: 2px dashed #28a745;
    border-radius: 10px;
    background-color: #f8f9fa;
    transition: all 0.3s ease;
}

.avatar-selection.active {
    border-color: #28a745;
    background-color: #e8f5e8;
    box-shadow: 0 0 10px rgba(40, 167, 69, 0.2);
}

.avatar-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    gap: 15px;
    margin-top: 1rem;
}

.avatar-option {
    position: relative;
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: 50%;
    overflow: hidden;
    aspect-ratio: 1;
    border: 3px solid transparent;
}

.avatar-option:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.avatar-option.selected {
    border-color: #28a745;
    box-shadow: 0 0 15px rgba(40, 167, 69, 0.5);
    transform: scale(1.05);
}

.avatar-option img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.avatar-option::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0);
    width: 25px;
    height: 25px;
    background-color: #28a745;
    border-radius: 50%;
    transition: transform 0.3s ease;
}

.avatar-option.selected::after {
    transform: translate(-50%, -50%) scale(1);
}

.avatar-option.selected::before {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-weight: bold;
    font-size: 14px;
    z-index: 2;
}

/* Estilos para el toggle entre foto personal y avatar */
.photo-type-toggle {
    display: flex;
    background-color: #e9ecef;
    border-radius: 25px;
    padding: 5px;
    margin-bottom: 1rem;
    overflow: hidden;
}

.photo-type-option {
    flex: 1;
    padding: 10px 20px;
    text-align: center;
    background: transparent;
    border: none;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    color: #6c757d;
}

.photo-type-option.active {
    background-color: #28a745;
    color: white;
    box-shadow: 0 2px 5px rgba(40, 167, 69, 0.3);
}

.photo-type-option:hover:not(.active) {
    background-color: #dee2e6;
    color: #495057;
}

/* Estilos para la vista previa de la foto */
.photo-preview {
    text-align: center;
    margin-top: 1rem;
}

.photo-preview img {
    width: 120px;
    height: 120px;
    object-fit: cover;
    border-radius: 50%;
    border: 4px solid #28a745;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.photo-preview .preview-label {
    margin-top: 10px;
    font-size: 0.9rem;
    color: #6c757d;
    font-weight: 500;
}

/* Estilos para el input de archivo personalizado */
.custom-file-input {
    position: relative;
    display: inline-block;
    width: 100%;
}

.custom-file-input input[type="file"] {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.custom-file-label {
    display: block;
    padding: 10px 15px;
    background-color: #f8f9fa;
    border: 2px dashed #ced4da;
    border-radius: 8px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #6c757d;
}

.custom-file-label:hover {
    background-color: #e9ecef;
    border-color: #28a745;
    color: #28a745;
}

.custom-file-label i {
    font-size: 2rem;
    margin-bottom: 10px;
    display: block;
    color: #28a745;
}

/* Animaciones */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.avatar-selection,
.photo-upload-section {
    animation: fadeIn 0.5s ease;
}

/* Responsive */
@media (max-width: 768px) {
    .avatar-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 10px;
    }
    
    .avatar-option {
        min-width: 60px;
    }
    
    .photo-type-option {
        padding: 8px 15px;
        font-size: 0.9rem;
    }
    
    .photo-preview img {
        width: 100px;
        height: 100px;
    }
}

@media (max-width: 480px) {
    .avatar-grid {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .photo-type-toggle {
        flex-direction: column;
        border-radius: 10px;
    }
    
    .photo-type-option {
        border-radius: 8px;
        margin: 2px;
    }
}
